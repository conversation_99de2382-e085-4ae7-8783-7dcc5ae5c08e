syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "validate/validate.proto";

import "sqlbuilder/search.proto";
import "common/enum.proto";
import "manager/base.proto";
import "dispatcher/base.proto";
import "dispatcher/publish.proto";


message PublishReq {
	common.TriggerMode trigger_mode = 1; // 触发模式
	string trigger_rule = 2; // 触发规则
	string project_id = 3;  // 项目id
	string task_id = 4; // 任务id
	string execute_id = 5;  // 执行id
	manager.ApiExecutionDataType execute_type = 6;  // 执行类型
	PublishType publish_type = 7; // 推送类型
	string user_id = 8;

	oneof data {
		PlanPublishInfo plan = 31;
		InterfaceDocumentPublishInfo interface = 32;
		SuitePublishInfo suite = 33;
		CasePublishInfo case = 34;
		InterfaceCasePublishInfo interface_case = 35;
		ComponentGroupPublishInfo component_group = 36;
		UICasePublishInfo ui_case = 37;
		UISuitePublishInfo ui_suite = 38;
		UIPlanPublishInfo ui_plan = 39;
		PrecisionSuitePublishInfo precision_suite = 40;
		PrecisionInterfaceDocumentPublishInfo precision_interface = 41;
		ServicePublishInfo service = 42;
		PerfCasePublishInfo perf_case = 43;
		PerfSuitePublishInfo perf_suite = 44;
		PerfPlanPublishInfo perf_plan = 45;
		StabilityPlanPublishInfo stability_plan = 46;
	}

	message SubEnvInfo {
		string sub_env_name = 1;
	}
	SubEnvInfo sub_env_info = 9;

	message TriggerUser {
		string email = 1;
		string account = 2;
	}
	TriggerUser trigger_user = 10;
	common.PurposeType purpose_type = 11; // 计划用途
	common.PriorityType  priority_type = 12 [(validate.rules).enum.defined_only = true]; // 优先级

	message ApproverUser {
		string email = 1;
		string account = 2;
	}
	repeated ApproverUser approvers = 13; // 审批人

	bool debug = 99;
}
message PublishResp {
	string project_id = 1;
	string task_id = 2;
	string execute_id = 3;
	string version = 4;
}

message StopMetadata {
	StopType stop_type = 1 [(validate.rules).enum = {not_in: [0]}]; // 停止类型
	string reason = 2;      // 停止原因
	oneof detail {          // 停止详细信息
		StopDetailOfPerfStopRule rule = 11; // 压测停止规则
	}
}
message StopReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string id = 3 [(validate.rules).string = {pattern: "(?:component_group|case|interface_case|suite|interface_document|plan|ui_plan|perf_plan|stability_plan)_id:.+?"}]; // 执行对象的ID，如：组件组ID、用例ID、集合ID、计划ID
	manager.ApiExecutionDataType execute_type = 4 [(validate.rules).enum = {in: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94]}]; // 执行类型
	string execute_id = 5 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行ID

	StopMetadata metadata = 11 [(validate.rules).message.required = true]; // 元数据
}
message StopResp {
}


message SearchTaskInfoReq{
	string project_id = 1; // 项目ID
	sqlbuilder.Condition condition = 2; // 查询条件
	sqlbuilder.Pagination pagination = 3; // 查询分页
	repeated sqlbuilder.SortField sorts = 4; // 查询排序
}
message SearchTaskInfoResp{
	uint64 current_page = 1; // 当前页
	uint64 page_size = 2;    // 每页大小
	uint64 total_count = 3;  // 总数
	uint64 total_page = 4;   // 总页数
	repeated SearchTaskInfoItem Items = 5;
}

message SearchTaskInfoItem{
	string project_id = 1;   // 项目ID
	string task_id = 2;      // 任务ID
	string plan_id = 3;      // 计划ID
	string plan_name = 4;    // 计划名称
	string trigger_mode = 5; // 触发类型
	common.ExecuteStatus task_execute_status = 6 [(validate.rules).enum.defined_only = true];   // 执行状态(0排队中,1执行中,2已完成,3已停止)
	common.PriorityType  priority_type = 7 [(validate.rules).enum.defined_only = true];         // 优先级策略(0 1 2 3 4 => Default、Middle、High、Ultra、Low)
	common.ExecutedResult task_executed_result = 8 [(validate.rules).enum.defined_only = true]; // 执行结果(0缺省,1成功,2失败,3异常)
	int64 total_case = 9;            // 总测试用例数
	int64 finished_case = 10;        // 已经执行的测试用例数
	int64 success_case = 11;         // 执行成功的测试用例数
	int64 total_suite = 12;          // 测试集合总数
	int64 finished_suite = 13;       // 执行完的测试集合数
	int64 success_suite = 14;        // 执行成功的测试集合数
	string execute_by = 15;          // 创建者
	int64 cost_time = 16;            // 执行耗时
	int64 create_time = 17;          // 创建时间
	int64 wait_time = 18;            // 排队耗时
	int64 started_at = 19;           // 开始时间
	int64 ended_at = 20;             // 结束时间
	string execute_id = 21;          // 执行ID
	string report_view_url = 22;     // 查看地址
	string report_download_url = 23; // 下载地址
	int64 update_at = 24;            // 更新时间
}


service Dispatcher {
	rpc Publish(PublishReq) returns (PublishResp);
	rpc Stop(StopReq) returns (StopResp);
	rpc SearchTaskInfo(SearchTaskInfoReq) returns (SearchTaskInfoResp);
}
