syntax = "proto3";

package reporter;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb";

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

import "sqlbuilder/search.proto";
import "common/config.proto";
import "common/enum.proto";
import "common/load.proto";
import "common/perf.proto";
import "common/stability.proto";
import "common/app.proto";
import "reporter/common.proto";
import "reporter/perfreporter.proto";
import "reporter/uireporter.proto";
import "reporter/stareporter.proto";


service reporter {
	// 组件组及用例记录 RPC接口
	rpc createRecord(PutRecordRequest) returns (CreateRecordResponse);
	rpc modifyRecord(PutRecordRequest) returns (ModifyRecordResponse);

	// 查询自身的RPC接口
	rpc getExecuteRecord(GetExecuteRecordRequest) returns (GetExecuteRecordResponse);
	// 查询Parent的RPC接口
	rpc getParentRecord(GetParentRecordRequest) returns (GetParentRecordResponse);
	// 查询Children的RPC接口
	rpc getChildrenRecord(GetChildrenRecordRequest) returns (GetChildrenRecordResponse);

	// `接口`
	// 创建接口记录
	rpc createInterfaceRecord(PutInterfaceRecordRequest) returns (CreateInterfaceRecordResponse);
	// 修改接口记录
	rpc modifyInterfaceRecord(PutInterfaceRecordRequest) returns (ModifyInterfaceRecordResponse);
	// 获取接口中用例最新一次执行记录
	rpc getCaseLatestRecord(GetCaseLatestRecordRequest) returns (GetCaseLatestRecordResponse);
	// 接口`调试`记录列表
	rpc listInterfaceRecord(ListInterfaceRecordRequest) returns (ListInterfaceRecordResponse);
	// 接口执行记录详情
	rpc getInterfaceRecord(GetInterfaceRecordRequest) returns (GetInterfaceRecordResponse);

	// `集合`
	// 创建集合记录
	rpc createSuiteRecord(PutSuiteRecordRequest) returns (CreateSuiteRecordResponse);
	// 修改集合记录
	rpc modifySuiteRecord(PutSuiteRecordRequest) returns (ModifySuiteRecordResponse);
	// 集合`调试`记录列表
	rpc listSuiteRecord(ListSuiteRecordRequest) returns (ListSuiteRecordResponse);
	// 集合执行记录详情
	rpc getSuiteRecord(GetSuiteRecordRequest) returns (GetSuiteRecordResponse);

	// `精准测试服务`
	// 创建精准测试服务记录
	rpc createServiceRecord(PutServiceRecordRequest) returns (CreateServiceRecordResponse);
	// 修改精准测试服务记录
	rpc modifyServiceRecord(PutServiceRecordRequest) returns (ModifyServiceRecordResponse);
	// 精准测试服务`调试`记录列表
	rpc listServiceRecord(ListServiceRecordRequest) returns (ListServiceRecordResponse);
	// 精准测试服务执行记录详情
	rpc getServiceRecord(GetServiceRecordRequest) returns (GetServiceRecordResponse);

	// `计划`
	// 创建计划记录
	rpc createPlanRecord(PutPlanRecordRequest) returns (CreatePlanRecordResponse);
	// 修改计划记录
	rpc modifyPlanRecord(PutPlanRecordRequest) returns (ModifyPlanRecordResponse);
	// 计划执行记录列表
	rpc listPlanRecord(ListPlanRecordRequest) returns (ListPlanRecordResponse);
	// 计划执行记录详情
	rpc getPlanRecord(GetPlanRecordRequest) returns (GetPlanRecordResponse);
	// 计划执行详情查看其下各集合用例执行时间刻度信息
	rpc getPlanTimeScale(GetPlanTimeScaleRequest) returns (GetPlanTimeScaleResponse);
	// 获取测试计划执行报告（ci/cd专用）
	rpc getPlanSummary(GetPlanSummaryRequest) returns (GetPlanSummaryResponse);
	// 获取API计划关联用例信息
	rpc getPlanCasesInfo(GetPlanCasesInfoRequest) returns (GetPlanCasesInfoResponse);

	// `用例`
	// 用例失败管理计划列表
	rpc listFailCaseForPlanRecord(ListFailCaseRecordForPlanRequest) returns (ListFailCaseRecordForPlanResponse);
	rpc delCaseFailStatForPlan(DelCaseFailStatForPlanReq) returns(DelCaseFailStatForPlanResp);
	// CountFailedCaseInLastNDays 统计最近N天指定用例的失败数
	rpc CountFailedCaseInLastNDays(CountFailedCaseInLastNDaysReq) returns(CountFailedCaseInLastNDaysResp);
}

// 组件、组件组、用例
message PutRecordRequest {
	string task_id = 1;                       // 任务执行id，dispatcher生成，不可为空（不会更新它）
	string project_id = 2;                    // 项目id，不可为空（不会更新它）
	string execute_id = 3;                    // 执行id, 不可为空（不会更新它）
	string execute_type = 4;                  // 执行类型，字符串表示枚举，不可为空（不会更新它）
	string general_config = 5;                // 通用配置（不会更新它）
	string account_config = 6;                // 池账号配置（不会更新它）
	string component_id = 7;                  // 组件id，不可为空（不会更新它）
	string component_name = 8;                // 组件名称，可为空（不会更新它）
	string component_type = 9;                // 组件类型，不可为空（不会更新它）
	string component_execute_id = 10;         // 组件执行记录id，必须传递（创建非循环组件传递空字符串，创建循环组件第1次传递非空字符串，第2次开始传递第1次返回的非空字符串；更新记录必须传递非空字符串）
	string parent_component_id = 11;          // 父组件id，必须传递，但可为空字符串（不会更新它）
	string parent_component_execute_id = 12;  // 父组件执行记录id，必须传递，但可为空字符串（不会更新它）
	string version = 13;                      // 版本，父组件版本能确定组件版本，不可为空（不会更新它)
	int64 times = 14;                         // 第几次执行（兼容循环组件）,默认为1（不会更新它）
	string status = 15;                       // 执行状态（结果）
	string content = 16;                      // 执行数据详情（更新时可为空）
	int64 is_root = 17;                       // 是否是根节点, 必须传递（不会更新它）
	string executed_by = 18;                  // 执行人的用户ID，不可为空（不会更新它）
	int64 started_at = 19;                    // 开始执行的时间戳，13位，精度为毫秒，（新增时不可为空）
	int64 ended_at = 20;                      // 结束执行的时间戳，13位，精度为毫秒，（更新时不可为空）
	string callback = 21;                     // callback日志（有传递新的值就覆盖，没传递就不覆盖）
	string maintained_by = 22;                // 维护人的用户ID（只有 API_CASE 和 INTERFACE_CASE 在用）
}

message CreateRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
	string component_execute_id = 3;          // 组件执行id
}

message ModifyRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
}

message GetExecuteRecordRequest {
	string task_id = 1;                       // 任务执行id
	string execute_id = 2;                    // 执行id
	string project_id = 3;                    // 项目id
	string execute_type = 4;                  // 执行类型，字符串表示枚举
	string component_id = 5;                  // 计划id、集合id、接口id、用例id、业务组id
	string component_type = 6;                // 组件类型（计划、集合、接口、用例、业务组）
	string component_execute_id = 7;          // 接口执行id 或 集合执行id 或 计划id
}

message GetExecuteRecordResponse {
	string task_id = 1;                       // 任务执行id
	string execute_id = 2;                    // 执行id
	string project_id = 3;                    // 项目id
	string execute_type = 4;                  // 执行类型，字符串表示枚举
	string component_id = 5;                  // 组件id
	string component_name = 6;                // 组件名称
	string component_type = 7;                // 组件类型
	string component_execute_id = 8;          // 组件执行id
	string parent_component_id = 9;           // 父组件id
	string parent_component_execute_id = 10;  // 父组件执行id
	string version = 11;                      // 组件版本
	int64 times = 12;                         // 第几次执行
	string status = 13;                       // 执行状态（结果）
	int64 is_root = 14;                       // 是否是根节点, 0:不是根节点； 1:是跟节点
	string executed_by = 15;                  // 执行人的用户ID
	int64 started_at = 16;                    // 开始执行的时间(戳)
	int64 ended_at = 17;                      // 结束执行的时间(戳)
	int64 cost_time = 18;                     // 执行耗时(毫秒)
}

message GetParentRecordRequest {
	string task_id = 1;                       // 任务执行id
	string execute_id = 2;                    // 执行id
	string project_id = 3;                    // 项目id
	string execute_type = 4;                  // 执行类型，字符串表示枚举
	string component_id = 5;                  // 计划id、集合id （或 接口id）、用例id、业务组件id
	string component_type = 6;                // 组件类型（接口、集合、计划）
	string component_execute_id = 7;          // 接口执行id 或 集合执行id 或 计划id
}

message GetParentRecordResponse {
	string task_id = 1;                       // 任务执行id
	string execute_id = 2;                    // 执行id
	string project_id = 3;                    // 项目id
	string execute_type = 4;                  // 执行类型，字符串表示枚举
	string component_id = 5;                  // 组件id
	string component_name = 6;                // 组件名称
	string component_type = 7;                // 组件类型
	string component_execute_id = 8;          // 组件执行id
	string parent_component_id = 9;           // 父组件id
	string parent_component_execute_id = 10;  // 父组件执行id
	string version = 11;                      // 组件版本
	int64 times = 12;                         // 第几次执行
	string status = 13;                       // 执行状态（结果）
	int64 is_root = 14;                       // 是否是根节点, 0:不是根节点； 1:是跟节点
	string executed_by = 15;              // 执行人id
	int64 started_at = 16;                    // 开始执行的时间(戳)
	int64 ended_at = 17;                      // 结束执行的时间(戳)
	int64 cost_time = 18;                     // 执行耗时(毫秒)
}

message GetChildrenRecordRequest {
	string task_id = 1;                 // 任务执行id
	string execute_id = 2;              // 执行id
	string project_id = 3;              // 项目id
	string execute_type = 4;            // 执行类型，字符串表示枚举
	string component_id = 5;            // 接口id 或 集合id 或 计划id
	string component_type = 6;          // 组件类型（接口、集合、计划）
	string component_execute_id = 7;    // 接口执行id 或 集合执行id 或 计划id
}

message GetChildrenRecordResponse {
	message ChildRecord {
		string task_id = 1;               // 任务执行id
		string execute_id = 2;            // 执行id
		string project_id = 3;            // 项目id
		string execute_type = 4;          // 执行类型，字符串表示枚举
		string component_id = 5;          // 组件id
		string component_name = 6;        // 组件名称
		string component_type = 7;        // 组件类型
		string component_execute_id = 8;  // 组件执行id
		string parent_component_id = 9;   // 父组件id
		string parent_component_execute_id = 10;   // 父组件执行id
		string version = 11;              // 组件版本
		int64 times = 12;                 // 第几次执行
		string status = 13;               // 执行状态（结果）
		int64 is_root = 14;               // 是否是根节点, 0:不是根节点； 1:是跟节点
		string executed_by = 15;          // 执行人的用户ID
		int64 started_at = 16;            // 开始执行的时间(戳)
		int64 ended_at = 17;              // 结束执行的时间(戳)
		int64 cost_time = 18;             // 执行耗时(毫秒)
	}
	repeated ChildRecord child_record_array = 1;
}

// 接口
message PutInterfaceRecordRequest {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id，dispatcher生成，不可为空（不会更新它）
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行id，必须传递（不会更新它）
	string execute_type = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1}];                  // 执行类型, 不可为空（不会更新它）
	string project_id = 4 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id，不可为空（不会更新它）
	string general_config = 5;                // 通用配置（不会更新它）
	string account_config = 6;                // 池账号配置（不会更新它）
	string interface_id = 7; // 接口id，不可为空（不会更新它）
	string interface_execute_id = 8 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 接口执行id (不会更新它）
	string interface_name = 9 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}]; // 接口名称，不可为空（不会更新它）
	string plan_execute_id = 10;              // 计划执行id，可为空（不会更新它）
	int64 total_case = 11;                    // 接口总用例个数，必须大于0（不会更新它）
	int64 success_case = 12;                  // 接口成功用例个数，更新记录时传递
	int64 failure_case = 13;                  // 接口失败用例个数，更新记录时传递
	string status = 14;                       // 执行状态（结果）
	string content = 15;                      // 执行数据详情（更新时可为空）
	string executed_by = 16; // 执行人的用户ID，不可为空（不会更新它）
	int64 started_at = 17;  // 开始执行的时间戳，13位，精度为毫秒，（新增时不可为空）
	int64 ended_at = 18;                      // 结束执行的时间戳，13位，精度为毫秒，（更新时不可为空）
	string callback = 19;                     // callback日志（有传递新的值就覆盖，没传递就不覆盖）
}

message CreateInterfaceRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
	string interface_execute_id = 3;          // 接口执行id
}

message ModifyInterfaceRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
}

message ListInterfaceRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string interface_id = 2; // 接口id
	sqlbuilder.Pagination pagination = 3; // 查询分页
	repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}

message ListInterfaceRecordResponse {
	message InterfaceRecord {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string interface_execute_id = 4;
		string interface_id = 5;
		string interface_name = 6;
		string executed_by = 7;
		int64 started_at = 8;
		int64 ended_at = 9;
		int64 cost_time = 10;
		int64 total_case = 11;
		int64 success_case = 12;
		int64 failure_case = 13;
		string status = 14;
		int64 cleaned = 15;
	}
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated InterfaceRecord items = 5;
}

message GetInterfaceRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id
	string interface_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 接口执行id
}

message GetInterfaceRecordResponse {
	message CaseItem {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string case_execute_id = 4;
		string case_id = 5;
		string case_name = 6;
		string version = 7;
		int64 started_at = 8;
		int64 ended_at = 9;
		int64 cost_time = 10;
		string status = 11;
		string maintained_by = 12;
	}
	int64 cost_time = 1;
	string executed_by = 2;
	int64 started_at = 3;
	int64 ended_at = 4;
	int64 total_case = 5;
	int64 success_case = 6;
	int64 failure_case = 7;
	string interface_id = 8;
	string interface_name = 9;
	google.protobuf.Struct content = 10;
	common.GeneralConfig general_config = 11;
	repeated common.AccountConfig account_config = 12;
	repeated CaseItem case_items = 13;
}

// 集合
message PutSuiteRecordRequest {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id，dispatcher生成，不可为空（不会更新它）
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行id，必须传递（不会更新它）
	string execute_type = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 执行类型, 不可为空（不会更新它）
	string project_id = 4 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id，不可为空（不会更新它）
	string general_config = 5;                // 通用配置（不会更新它）
	string account_config = 6;                // 池账号配置（不会更新它）
	string suite_id = 7; // 集合id，不可为空（不会更新它）
	string suite_execute_id = 8 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}];  // 集合执行id (不会更新它）
	string suite_name = 9 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}]; // 集合名称，不可为空（不会更新它）
	string plan_execute_id = 10;              // 计划执行id，可为空（不会更新它）
	int64 total_case = 11;                    // 集合总用例个数，必须大于0（不会更新它）
	int64 success_case = 12;                  // 集合成功用例个数，更新记录时传递
	int64 failure_case = 13;                  // 集合失败用例个数，更新记录时传递
	string status = 14;                       // 执行状态（结果）
	string content = 15;                      // 执行数据详情（更新时可为空）
	string executed_by = 16; // 执行人的用户ID，不可为空（不会更新它）
	int64 started_at = 17;  // 开始执行的时间戳，13位，精度为毫秒，（新增时不可为空）
	int64 ended_at = 18;                      // 结束执行的时间戳，13位，精度为毫秒，（更新时不可为空）
	string callback = 19;                     // callback日志（有传递新的值就覆盖，没传递就不覆盖）
}

message CreateSuiteRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
	string suite_execute_id = 3;              // 接口执行id
}

message ModifySuiteRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
}

message ListSuiteRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string suite_id = 2; // 集合ID
	sqlbuilder.Pagination pagination = 3; // 查询分页
	repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}

message ListSuiteRecordResponse {
	message SuiteRecord {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string suite_execute_id = 4;
		string suite_id = 5;
		string suite_name = 6;
		string executed_by = 7;
		int64 started_at = 8;
		int64 ended_at = 9;
		int64 cost_time = 10;
		int64 total_case = 11;
		int64 success_case = 12;
		int64 failure_case = 13;
		string status = 14;
		int64 cleaned = 15;
	}
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated SuiteRecord items = 5;
}

message GetSuiteRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id
	string suite_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 集合执行id
}

message GetSuiteRecordResponse {
	message CaseItem {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string case_execute_id = 4;
		string case_id = 5;
		string case_name = 6;
		string version = 7;
		int64 started_at = 8;
		int64 ended_at = 9;
		int64 cost_time = 10;
		string status = 11;
		string maintained_by = 12;
		string case_type = 13;
	}
	int64 cost_time = 1;
	string executed_by = 2;
	int64 started_at = 3;
	int64 ended_at = 4;
	int64 total_case = 5;
	int64 success_case = 6;
	int64 failure_case = 7;
	string suite_id = 8;
	string suite_name = 9;
	google.protobuf.Struct content = 10;
	common.GeneralConfig general_config = 11;
	repeated common.AccountConfig account_config = 12;
	repeated CaseItem case_items = 13;
}


// 精准测试服务
message PutServiceRecordRequest {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id，dispatcher生成，不可为空（不会更新它）
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行id，必须传递（不会更新它）
	string execute_type = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 执行类型, 不可为空（不会更新它）
	string project_id = 4 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id，不可为空（不会更新它）
	string general_config = 5;                // 通用配置（不会更新它）
	string account_config = 6;                // 池账号配置（不会更新它）
	string service_id = 7; // 精准id，不可为空（不会更新它）
	string service_execute_id = 8 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}];  // 服务执行id (不会更新它）
	string service_name = 9 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}]; // 服务名称，不可为空（不会更新它）
	string plan_execute_id = 10;              // 计划执行id，可为空（不会更新它）
	int64 total_case = 11;                    // 集合总用例个数，必须大于0（不会更新它）
	int64 success_case = 12;                  // 集合成功用例个数，更新记录时传递
	int64 failure_case = 13;                  // 集合失败用例个数，更新记录时传递
	string status = 14;                       // 执行状态（结果）
	string content = 15;                      // 执行数据详情（更新时可为空）
	string executed_by = 16; // 执行人的用户ID，不可为空（不会更新它）
	int64 started_at = 17;  // 开始执行的时间戳，13位，精度为毫秒，（新增时不可为空）
	int64 ended_at = 18;                      // 结束执行的时间戳，13位，精度为毫秒，（更新时不可为空）
	string callback = 19;                     // callback日志（有传递新的值就覆盖，没传递就不覆盖）
}

message CreateServiceRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
	string service_execute_id = 3;              // 接口执行id
}

message ModifyServiceRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
}

message ListServiceRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string service_id = 2; // 服务ID
	sqlbuilder.Pagination pagination = 3; // 查询分页
	repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}

message ListServiceRecordResponse {
	message ServiceRecord {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string service_execute_id = 4;
		string service_id = 5;
		string service_name = 6;
		string executed_by = 7;
		int64 started_at = 8;
		int64 ended_at = 9;
		int64 cost_time = 10;
		int64 total_case = 11;
		int64 success_case = 12;
		int64 failure_case = 13;
		string status = 14;
		int64 cleaned = 15;
	}
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated ServiceRecord items = 5;
}

message GetServiceRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id
	string service_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 服务执行id
}

message GetServiceRecordResponse {
	message CaseItem {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string case_execute_id = 4;
		string case_id = 5;
		string case_name = 6;
		string version = 7;
		int64 started_at = 8;
		int64 ended_at = 9;
		int64 cost_time = 10;
		string status = 11;
		string maintained_by = 12;
	}
	int64 cost_time = 1;
	string executed_by = 2;
	int64 started_at = 3;
	int64 ended_at = 4;
	int64 total_case = 5;
	int64 success_case = 6;
	int64 failure_case = 7;
	string service_id = 8;
	string service_name = 9;
	google.protobuf.Struct content = 10;
	common.GeneralConfig general_config = 11;
	repeated common.AccountConfig account_config = 12;
	repeated CaseItem case_items = 13;
}

// 计划
message PutPlanRecordRequest {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id，dispatcher生成，不可为空（不会更新它）
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行id，必须传递（不会更新它）
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id，不可为空（不会更新它）
	string general_config = 4;               // 通用配置（不会更新它）
	string account_config = 5;               // 池账号配置（不会更新它）
	string plan_id = 6 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划id，不可为空（不会更新它）
	string plan_execute_id = 7 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 计划执行id (不会更新它）
	string plan_name = 8 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}];  // 计划名称，不可为空（不会更新它）
	string plan_purpose = 9 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}];  // 计划用途，不可为空（不会更新它）
	int64 total_suite = 10;                   // 计划总集合（或接口）个数，必须大于0（不会更新它）
	int64 success_suite = 11;                 // 计划成功集合（或接口）个数，更新记录时传递
	int64 failure_suite = 12;                 // 计划失败集合(或接口)个数，更新记录时传递
	int64 total_case = 13;                   // 计划总用例（或接口）个数，必须大于0（不会更新它）
	int64 success_case = 14;                 // 计划成功用例（或接口）个数，更新记录时传递
	int64 failure_case = 15;                 // 计划失败用例(或接口)个数，更新记录时传递
	string status = 16;                       // 执行状态（结果）
	string content = 17;                      // 执行数据详情（更新时可为空）
	string service_cases_content = 18;        // 保存每个服务与测试用例关系，用于精准测试（不会更新它）
	string executed_by = 19; // 执行人的用户ID，不可为空（不会更新它）
	int64 started_at = 20;                    // 开始执行的时间戳，13位，精度为毫秒，（新增时不可为空）
	int64 ended_at = 21;                      // 结束执行的时间戳，13位，精度为毫秒，（更新时不可为空）
	string trigger_mode = 22;                 // 触发模式
	string approved_by = 23;                  // 审批人的用户ID
}

message CreatePlanRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
	string plan_execute_id = 3;              // 接口执行id
}

message ModifyPlanRecordResponse {
	uint32 code = 1;                          // 结果码
	string message = 2;                       // 提示信息
}

message GetCaseLatestRecordRequest {
	string project_id = 1;                    // 项目id，不可为空
	string interface_id = 2;                  // 接口id，不可为空
	repeated string case_id_array = 3;        // 用例id列表，不可为空
}

message GetCaseLatestRecordResponse {
	message RecordCaseRecord {// 用例最新一次执行记录
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string case_id = 4;
		string status = 5;
		string content = 6;
		int64 started_at = 7;
		int64 ended_at = 8;
	}
	repeated RecordCaseRecord case_record_array = 1;
}

message ListPlanRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划id
	sqlbuilder.Pagination pagination = 3; // 查询分页
	repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}

message ListPlanRecordResponse {
	message PlanRecord {
		string task_id = 1;
		string project_id = 2;
		string execute_id = 3;
		string plan_execute_id = 4;
		string executed_by = 5;
		int64 started_at = 6;
		int64 ended_at = 7;
		int64 cost_time = 8;
		string status = 9;
		int64 total_suite = 10;
		int64 success_suite = 11;
		int64 failure_suite = 12;
		int64 total_case = 13;
		int64 success_case = 14;
		int64 failure_case = 15;
		int64 cleaned = 16;
		string purpose_type = 17;
		string trigger_mode = 18;
		string type = 19; // 触发模式，同trigger_mode
	}
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated PlanRecord items = 5;
}

message GetPlanRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id
	string plan_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 计划执行id
}

message GetPlanRecordResponse {
	message SuiteItem {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string suite_execute_id = 4;
		string suite_id = 5;
		string suite_name = 6;
		int64 total_case = 7;
		int64 success_case = 8;
		int64 failure_case = 9;
		int64 started_at = 10;
		int64 ended_at = 11;
		int64 cost_time = 12;
		string status = 13;
	}
	message InterfaceDocumentItem {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string interface_execute_id = 4;
		string interface_id = 5;
		string interface_name = 6;
		int64 total_case = 7;
		int64 success_case = 8;
		int64 failure_case = 9;
		int64 started_at = 10;
		int64 ended_at = 11;
		int64 cost_time = 12;
		string status = 13;
	}
	message ServiceItem {
		string task_id = 1;
		string execute_id = 2;
		string project_id = 3;
		string service_execute_id = 4;
		string service_id = 5;
		string service_name = 6;
		int64 total_case = 7;
		int64 success_case = 8;
		int64 failure_case = 9;
		int64 started_at = 10;
		int64 ended_at = 11;
		int64 cost_time = 12;
		string status = 13;
	}
	int64 cost_time = 1;
	string executed_by = 2;
	int64 started_at = 3;
	int64 ended_at = 4;
	int64 total_suite = 5;
	int64 success_suite = 6;
	int64 failure_suite = 7;
	int64 total_case = 8;
	int64 success_case = 9;
	int64 failure_case = 10;
	string plan_id = 11;
	string plan_name = 12;
	string purpose_type = 13;
	google.protobuf.Struct content = 14;
	google.protobuf.ListValue service_cases_content = 15;
	common.GeneralConfig general_config = 16;
	repeated common.AccountConfig account_config = 17;
	repeated SuiteItem suite_items = 18;
	repeated InterfaceDocumentItem interface_document_items = 19;
	repeated ServiceItem service_items = 20;
	string status = 21;
	string trigger_mode = 22;
	string type = 23; // 触发模式，同 trigger_mode
	repeated string approvers = 24; // 审批人的用户ID
}

message GetPlanTimeScaleRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id
	string plan_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 计划执行id
}

message GetPlanTimeScaleResponse {
	message CaseRecord {
		string case_id = 1;
		string case_name = 2;
		int64 started_at = 3;
		int64 ended_at = 4;
		int64 cost_time = 5;
		string status = 6;
	}
	message SuiteRecord {
		string suite_id = 1;
		string suite_name = 2;
		repeated CaseRecord case_items = 3;
	}
	repeated SuiteRecord suite_items = 1;
}

message GetPlanSummaryRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务执行id
	string execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 计划执行id 同plan_execute_id
	int64 plan_type = 4; // 计划类型

}

message GetPlanSummaryResponse {
	string status = 1;  // 测试计划执行状态
	message Record {
		int64 total_case = 1;  // 测试计划包含的测试用例个数
		int64 success_case = 2; // 测试计划执行成功用例个数
		string pass_rate = 3; // 测试计划用例通过率（形如：80.00%） 保留小数点后两位
		string passing_rate = 4;  // 测试计划用例通过率（形如：80/100)
		string report_path = 5;   // 测试计划报告地址
	}
	Record record = 2;
}

message CleanConfig {
	string Type = 1;
	string Mode = 2;
	string Value = 3;
}
message CleanConfigs {
	repeated CleanConfig config = 1;
}

message RedundantPlanRecord {
	string task_id = 1;
	string project_id = 2;
	string plan_id = 3;
	int64 created_at = 4;

}
message FindRedundantPlanRecordResp {
	repeated RedundantPlanRecord items = 1;
}

message GetPlanCasesInfoRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 计划id
	string execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 计划执行id
}

message GetPlanCasesInfoResponse {
	int64 total_case = 1; // 用例总数
	int64 failure_case = 2; // 失败用例数
	int64 success_case = 3; // 成功用例数
}

message ListFailCaseRecordForPlanRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string case_id = 2; // 用例id
	sqlbuilder.Pagination pagination = 3; // 查询分页
	string case_type = 4 ; // 用例类型[API_CASE，INTERFACE_CASE]
}

message ListFailCaseRecordForPlanResponse {
	message FailCaseRecordForPlanRecord {
		string task_id = 1;
		string project_id = 2;
		string execute_id = 3;
		string plan_execute_id = 4;
		string executed_by = 5;
		int64 started_at = 6;
		int64 ended_at = 7;
		int64 cost_time = 8;
		string status = 9;
		int64 total_suite = 10;
		int64 success_suite = 11;
		int64 failure_suite = 12;
		int64 total_case = 13;
		int64 success_case = 14;
		int64 failure_case = 15;
		int64 cleaned = 16;
		string purpose_type = 17;
		string trigger_mode = 18;
		string type = 19; // 触发模式，同trigger_mode
		string plan_id = 20;
		string plan_name = 21;
	}
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated FailCaseRecordForPlanRecord items = 5;
}

message CaseFailForPlanStatForMq {
	string project_id = 1; // 项目ID
	string branch_id = 2; // 分支ID
	string execute_id = 3; // 执行ID
	string case_id = 4; // 用例ID
	string case_type = 5; // 用例类型
	string task_id = 6; // 任务ID
	string plan_id = 7; // 计划ID
	string fail_reason_url = 8; // 失败原因地址
}

message DelCaseFailStatForPlanReq{
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id
	string case_id = 2; // 用例id
	string case_type = 3 ; // 用例类型[API_CASE，INTERFACE_CASE]
}
message DelCaseFailStatForPlanResp{
}

message CountFailedCaseInLastNDaysReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string case_type = 2 [(validate.rules).string = {in: ["API_CASE", "INTERFACE_CASE"]}]; // 用例类型
	string case_id = 3 [(validate.rules).string = {pattern: "(?:case|interface_case)_id:.+?"}]; // 用例ID
	int32 days = 4; // 统计最近N天指定用例的执行失败数量
}
message CountFailedCaseInLastNDaysResp {
	int64 count = 1; // 指定用例最近N天执行失败数量
	int64 last_updated_at = 2; // 最近一次的更新时间
}


service UIReporter {
	//CreateUICaseRecord 创建UI用例执行记录
	rpc CreateUICaseRecord(PutUICaseRecordRequest) returns (CreateUICaseRecordResponse);
	//ModifyUICaseRecord 修改UI用例执行记录
	rpc ModifyUICaseRecord(PutUICaseRecordRequest) returns (ModifyUICaseRecordResponse);
	//CreateUISuiteRecord 创建UI集合执行记录
	rpc CreateUISuiteRecord(PutUISuiteRecordRequest) returns (CreateUISuiteRecordResponse);
	//ModifyUISuiteRecord 修改UI集合执行记录
	rpc ModifyUISuiteRecord(PutUISuiteRecordRequest) returns (ModifyUISuiteRecordResponse);
	//CreateUIPlanRecord 创建UI计划执行记录
	rpc CreateUIPlanRecord(PutUIPlanRecordRequest) returns (CreateUIPlanRecordResponse);
	//ModifyUIPlanRecord 修改UI计划执行记录
	rpc ModifyUIPlanRecord(PutUIPlanRecordRequest) returns (ModifyUIPlanRecordResponse);
	//ViewUIPlanRecord 查看UI计划执行记录
	rpc ViewUIPlanRecord(ViewUIPlanRecordRequest) returns (ViewUIPlanRecordResponse);
	//ListUIPlanRecord UI计划执行记录列表
	rpc ListUIPlanRecord(ListUIPlanRecordRequest) returns (ListUIPlanRecordResponse);
	//GetUIPlanCasesInfo 获取UI计划关联用例信息
	rpc GetUIPlanCasesInfo(GetUIPlanCasesInfoRequest) returns (GetUIPlanCasesInfoResponse);

	//GetUIPlanRecord 获取UI计划执行记录
	rpc GetUIPlanRecord(GetUIPlanRecordReq) returns (GetUIPlanRecordResp);
	//SearchUISuiteRecord 搜索UI计划执行记录下的UI集合执行记录
	rpc SearchUISuiteRecord(SearchUISuiteRecordReq) returns (SearchUISuiteRecordResp);
	//SearchUICaseRecord 搜索UI集合执行记录下的UI用例执行记录
	rpc SearchUICaseRecord(SearchUICaseRecordReq) returns (SearchUICaseRecordResp);
	//GetUICaseRecord 获取UI用例执行记录
	rpc GetUICaseRecord(GetUICaseRecordReq) returns (GetUICaseRecordResp);
	//ListUICaseStep 获取UI用例执行步骤列表
	rpc ListUICaseStep(ListUICaseStepReq) returns (ListUICaseStepResp);
	//GetUICaseStep 获取UI用例执行步骤
	rpc GetUICaseStep(GetUICaseStepReq) returns (GetUICaseStepResp);
	//SearchUIDeviceRecord 搜索UI计划执行记录下的设备记录
	rpc SearchUIDeviceRecord(SearchUIDeviceRecordReq) returns (SearchUIDeviceRecordResp);

	//SaveUIDevicePerfData 保存UI测试设备性能数据
	rpc SaveUIDevicePerfData(SaveUIDevicePerfDataReq) returns (SaveUIDevicePerfDataResp);
	//GetUIDevicePerfData 获取UI测试设备性能数据
	rpc GetUIDevicePerfData(GetUIDevicePerfDataReq) returns (GetUIDevicePerfDataResp);
}

message PutUICaseRecordRequest {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID，dispatcher生成，不可为空（不会更新它）
	string project_id = 2 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID，不可为空（不会更新它）
	string execute_id = 3 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^execute_id:.+?)"}]; // 执行ID，不传递则自动生成（不会更新它）
	string case_id = 4 [(validate.rules).string = {min_len: 1}]; // 用例ID，不可为空（不会更新它）
	string case_name = 5 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}]; // 用例名称（不会更新它）
	string suite_execute_id = 6 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 所属集合执行ID，必须传递（不会更新它）
	string status = 7;      // 执行状态（结果）
	string content = 8;     // 执行数据详情（更新时可为空）
	string executed_by = 9; // 执行人的用户ID，不可为空（不会更新它）
	int64 started_at = 10;  // 开始执行的时间戳，13位，精度为毫秒，（新增时不可为空）
	int64 ended_at = 11;    // 结束执行的时间戳，13位，精度为毫秒，（更新时不可为空）
	string callback = 12;   // callback日志（有传递新的值就覆盖，没传递就不覆盖）
	string udid = 13;       // 设备编号（有传值则更新，没有传值则不更新）
}
message CreateUICaseRecordResponse {
	string execute_id = 1; // 执行ID
}
message ModifyUICaseRecordResponse {
}

message PutUISuiteRecordRequest {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID，dispatcher生成，不可为空（不会更新它）
	string project_id = 2 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID，不可为空（不会更新它）
	string execute_id = 3 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^execute_id:.+?)"}]; // 执行ID，不传递则自动生成（不会更新它）
	string suite_id = 4 [(validate.rules).string = {min_len: 1}]; // 集合ID，不可为空（不会更新它）
	string suite_name = 5 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}]; // 集合名称（不会更新它）
	string plan_execute_id = 6 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 所属计划执行ID，不可为空（不会更新它）
	int64 total_case = 7;    // 包含的测试用例总数，必须大于0（不会更新它）
	int64 success_case = 8;  // 执行成功的测试用例数
	int64 failure_case = 9;  // 执行失败的测试用例数
	string status = 10;      // 执行状态（结果）
	string content = 11;     // 执行数据详情（更新时可为空）
	string executed_by = 12; // 执行人的用户ID（不会更新它）
	int64 started_at = 13;   // 开始执行的时间戳，13位，精度为毫秒，（新增时不可为空）
	int64 ended_at = 14;     // 结束执行的时间戳，13位，精度为毫秒，（更新时不可为空）
	int64 finished = 15;     // 计划是否执行完，0表示未执行完，1表示执行完
	string callback = 16;    // callback日志（有传递新的值就覆盖，没传递就不覆盖
}
message CreateUISuiteRecordResponse {
	string execute_id = 1; // 执行ID
}
message ModifyUISuiteRecordResponse {
}

message PutUIPlanRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID，不可为空（不会更新它）
	string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID，不可为空（不会更新它）
	string plan_name = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}]; // 计划名称（不会更新它）
	string task_id = 4 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID，dispatcher生成，不可为空（不会更新它）
	string execute_id = 5 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^execute_id:.+?)"}]; // 执行ID，不传递则自动生成（不会更新它）
	string status = 6; // 执行状态（结果）
	string executed_by = 7; // 执行人的用户ID，不可为空（不会更新它）
	int64 started_at = 8;  // 开始执行的时间戳，13位，精度为毫秒，（新增时不可为空）
	int64 ended_at = 9; // 结束执行的时间戳，13位，精度为毫秒，（更新时不可为空）
	int64 total_suite = 10; // 包含的测试集合总数，必须大于0（不会更新它）
	int64 finished_suite = 11; // 执行完结的测试集合数
	int64 success_suite = 12; // 执行成功的测试集合数
	int64 total_case = 13; // 包含的测试用例总数，必须大于0（不会更新它）
	int64 finished_case = 14; // 执行完结的测试用例数
	int64 success_case = 15; // 执行成功的测试用例数
	string content = 16; // 执行数据详情（更新时可为空）
	int64 finished = 17; // 计划是否执行完，0表示未执行完，1表示执行完
	string execute_data = 18; // 执行数据，用于追溯任务执行结果
	string trigger_mode = 19; // 触发模式
	string type = 20; // 触发模式，同trigger_mode
	common.ExecuteStatus execute_status = 21 [(validate.rules).enum.defined_only = true]; // 执行状态(0排队中,1执行中,2已完成,3已停止)
	common.PriorityType  priority_type = 22 [(validate.rules).enum.defined_only = true]; // 优先级策略(0 1 2 3 4 =》Default、Middle、High、Ultra、Low)
	//common.ExecutedResult executed_result = 23 [(validate.rules).enum.defined_only = true]; // 执行结果(0缺省,1成功,2失败,3异常)
	int64 wait_time = 24; // 排队耗时
	//int64 update_at = 25; // 更新时间
}
message CreateUIPlanRecordResponse {
	string execute_id = 1; // 执行ID
}
message ModifyUIPlanRecordResponse {
}

message ViewUIPlanRecordRequest {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string project_id = 2 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 计划执行ID
}
message ViewUIPlanRecordResponse {
	string project_id = 1;
	string plan_id = 2;
	string plan_name = 3;
	string task_id = 4;
	string execute_id = 5;
	string status = 6;
	int64 cost_time = 7;
	string executed_by = 8;
	int64 started_at = 9;
	int64 ended_at = 10;
	int64 total_suite = 11;
	int64 finished_suite = 12;
	int64 success_suite = 13;
	int64 total_case = 14;
	int64 finished_case = 15;
	int64 success_case = 16;
	string content = 17;
	int64 finished = 18;
	int64 cleaned = 19;
	string execute_data = 20;
	string trigger_mode = 21; // 触发模式
	string type = 22; // 触发模式，同trigger_mode
	// 可能有用
	common.ExecuteStatus execute_status = 24 [(validate.rules).enum.defined_only = true]; // 执行状态(0排队中,1执行中,2已完成,3已停止)
	common.PriorityType  priority_type = 25 [(validate.rules).enum.defined_only = true]; // 优先级策略(0 1 2 3 4 =》Default、Middle、High、Ultra、Low)
	common.ExecutedResult executed_result = 26 [(validate.rules).enum.defined_only = true]; // 执行结果(0缺省,1成功,2失败,3异常)
	int64 wait_time = 27; // 排队耗时
	int64 update_at = 28; // 更新时间
}

message ListUIPlanRecordRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
	sqlbuilder.Pagination pagination = 3; // 查询分页
	repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message ListUIPlanRecordResponse {
	message PlanRecord {
		string project_id = 1;
		string plan_id = 2;
		string plan_name = 3;
		string task_id = 4;
		string execute_id = 5;
		string status = 6;
		int64 cost_time = 7;
		string executed_by = 8;
		int64 started_at = 9;
		int64 ended_at = 10;
		int64 total_suite = 11;
		int64 finished_suite = 12;
		int64 success_suite = 13;
		int64 total_case = 14;
		int64 finished_case = 15;
		int64 success_case = 16;
		string content = 17;
		string report_view_url = 18;
		string report_download_url = 19;
		int64 finished = 20;
		int64 cleaned = 21;
		string trigger_mode = 22;
		string type = 23; // 触发模式，同trigger_mode
		common.ExecuteStatus execute_status = 24 [(validate.rules).enum.defined_only = true]; // 执行状态(0排队中,1执行中,2已完成,3已停止)
		common.PriorityType  priority_type = 25 [(validate.rules).enum.defined_only = true]; // 优先级策略(0 1 2 3 4 =》Default、Middle、High、Ultra、Low)
		common.ExecutedResult executed_result = 26 [(validate.rules).enum.defined_only = true]; // 执行结果(0缺省,1成功,2失败,3异常)
		int64 wait_time = 27; // 排队耗时
		int64 update_at = 28; // 更新时间
	}
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated PlanRecord items = 5;
}

message GetUIPlanCasesInfoRequest {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 计划执行ID
}
message GetUIPlanCasesInfoResponse {
	int64 total_case = 1; // 用例总数
	int64 finished_case = 2; // 完成用例数
	int64 success_case = 3; // 成功用例数
}

message GetUIPlanRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // UI计划执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
message GetUIPlanRecordResp {
	UIPlanRecord record = 1;
}

message SearchUISuiteRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // UI计划执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string udid = 4 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备编号

	sqlbuilder.Condition condition = 11; // 查询条件
	sqlbuilder.Pagination pagination = 12; // 查询分页
	repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchUISuiteRecordResp {
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated UISuiteRecord items = 5;
}

message SearchUICaseRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // UI集合执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string udid = 4 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备编号

	sqlbuilder.Condition condition = 11; // 查询条件
	sqlbuilder.Pagination pagination = 12; // 查询分页
	repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchUICaseRecordResp {
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated UICaseRecord items = 5;
}

message GetUICaseRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // UI用例执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
message GetUICaseRecordResp {
	UICaseRecord record = 1;
}

message ListUICaseStepReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // UI用例执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	bool with_content = 4; // 是否返回步骤内容
}
message ListUICaseStepResp {
	uint64 total_count = 1;
	repeated UICaseStep items = 2;
}

message GetUICaseStepReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string step_id = 2 [(validate.rules).string = {pattern: "(?:^step_id:.+?)"}]; // 步骤ID
}
message GetUICaseStepResp {
	UICaseStep step = 1;
}

message SearchUIDeviceRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // UI计划执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

	sqlbuilder.Condition condition = 11; // 查询条件
	sqlbuilder.Pagination pagination = 12; // 查询分页
	repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchUIDeviceRecordResp {
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;

	repeated UIDeviceRecord items = 11;
}

message SaveUIDevicePerfDataReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // UI用例执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

	string udid = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备编号
	common.PerfDataType data_type = 12 [(validate.rules).enum = {not_in: [0]}]; // 数据类型
	int64 interval = 13 [(validate.rules).int64 = {gt: 0}]; // 采集间隔，单位毫秒
	string series = 14 [(validate.rules).string = {min_len: 1, max_len: 16}]; // 指标名称
	string unit = 15 [(validate.rules).string = {min_len: 0, max_len: 8}]; // 单位
	string x = 16 [(validate.rules).string = {min_len: 1, max_len: 64}]; // X轴数据
	string y = 17 [(validate.rules).string = {min_len: 1, max_len: 64}]; // Y轴数据

	string executed_by = 21 [(validate.rules).string = {min_len:1, max_len: 64}]; // 执行者的用户ID
}
message SaveUIDevicePerfDataResp {}

message GetUIDevicePerfDataReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string project_id = 2 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

	string udid = 11 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备编号
	common.PerfDataType data_type = 12 [(validate.rules).enum = {not_in: [0]}]; // 数据类型
}
message GetUIDevicePerfDataResp {
	PerfData data = 1;
}


service PerfReporter {
	//CreatePerfCaseRecord 创建压测用例执行记录
	rpc CreatePerfCaseRecord(CreatePerfCaseRecordReq) returns (CreatePerfCaseRecordResp);
	//ModifyPerfCaseRecord 修改压测用例执行记录
	rpc ModifyPerfCaseRecord(ModifyPerfCaseRecordReq) returns (ModifyPerfCaseRecordResp);
	//GetPerfCaseRecord 获取压测用例执行记录
	rpc GetPerfCaseRecord(GetPerfCaseRecordReq) returns (GetPerfCaseRecordResp);

	//CreatePerfSuiteRecord 创建压测集合执行记录
	rpc CreatePerfSuiteRecord(CreatePerfSuiteRecordReq) returns (CreatePerfSuiteRecordResp);
	//ModifyPerfSuiteRecord 修改压测集合执行记录
	rpc ModifyPerfSuiteRecord(ModifyPerfSuiteRecordReq) returns (ModifyPerfSuiteRecordResp);
	//GetPerfSuiteRecord 获取压测集合执行记录
	rpc GetPerfSuiteRecord(GetPerfSuiteRecordReq) returns (GetPerfSuiteRecordResp);

	//CreatePerfPlanRecord 创建压测计划执行记录
	rpc CreatePerfPlanRecord(CreatePerfPlanRecordReq) returns (CreatePerfPlanRecordResp);
	//ModifyPerfPlanRecord 修改压测计划执行记录
	rpc ModifyPerfPlanRecord(ModifyPerfPlanRecordReq) returns (ModifyPerfPlanRecordResp);
	//SearchPerfPlanRecord 搜索压测计划执行记录
	rpc SearchPerfPlanRecord(SearchPerfPlanRecordReq) returns (SearchPerfPlanRecordResp);
	//GetPerfPlanRecord 获取压测计划执行记录
	rpc GetPerfPlanRecord(GetPerfPlanRecordReq) returns (GetPerfPlanRecordResp);
	//SearchPerfCaseRecord 搜索压测计划执行记录下的压测用例执行记录
	rpc SearchPerfCaseRecord(SearchPerfCaseRecordReq) returns (SearchPerfCaseRecordResp);
	//UpdateMonitorURLOfPerfPlanRecord 更新压测计划执行记录的`metric_url`字段
	rpc UpdateMonitorURLOfPerfPlanRecord(UpdateMonitorURLOfPerfPlanRecordReq) returns (UpdateMonitorURLOfPerfPlanRecordResp);
}

message CreatePerfCaseRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 用例执行ID
	string suite_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 集合执行ID

	string project_id = 11 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string case_id = 12 [(validate.rules).string = {pattern: "(?:^perf_case_id:.+?)", max_len: 64}]; // 用例ID
	string case_name = 13 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 用例名称
	repeated PerfCaseStepInfo steps = 14 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}]; // 用例步骤

	PerfDataInfo perf_data = 21 [(validate.rules).message = {required: true}]; // 压测数据
  common.LoadGenerator load_generator = 22 [(validate.rules).message = {required: true}]; // 施压机资源

	string status = 31 [(validate.rules).string = {max_len: 64}]; // 执行状态（结果）
	string executed_by = 32 [(validate.rules).string = {min_len:1, max_len: 64}]; // 执行者的用户ID
	google.protobuf.Timestamp started_at = 33 [(validate.rules).timestamp = {required: true}]; // 开始时间
}
message CreatePerfCaseRecordResp {}

message ModifyPerfCaseRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 用例执行ID
	string suite_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 集合执行ID

	string project_id = 11 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string case_id = 12 [(validate.rules).string = {pattern: "(?:^perf_case_id:.+?)", max_len: 64}]; // 用例ID

	string status = 31 [(validate.rules).string = {max_len: 64}]; // 执行状态（结果）
	google.protobuf.Timestamp ended_at = 32; // 结束时间

	repeated APIMetric api_metrics = 41 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 用例涉及的接口的指标信息
	ErrorMessage err_msg = 42; // 错误信息
}
message ModifyPerfCaseRecordResp {}

message GetPerfCaseRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 压测用例执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
}
message GetPerfCaseRecordResp {
	PerfCaseRecord record = 1;
}

message CreatePerfSuiteRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 集合执行ID
	string plan_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 计划执行ID

	string project_id = 11 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string suite_id = 12 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 集合ID
	string suite_name = 13 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 集合名称

	string status = 31 [(validate.rules).string = {max_len: 64}]; // 执行状态（结果）
	string executed_by = 32 [(validate.rules).string = {min_len:1, max_len: 64}]; // 执行者的用户ID
	google.protobuf.Timestamp started_at = 33 [(validate.rules).timestamp = {required: true}]; // 开始时间
}
message CreatePerfSuiteRecordResp {}

message ModifyPerfSuiteRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 集合执行ID
	string plan_execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 计划执行ID

	string project_id = 11 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string suite_id = 12 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 集合ID

	string status = 31 [(validate.rules).string = {max_len: 64}]; // 执行状态（结果）
	google.protobuf.Timestamp ended_at = 32; // 结束时间

	ErrorMessage err_msg = 42; // 错误信息
}
message ModifyPerfSuiteRecordResp {}

message GetPerfSuiteRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 压测集合执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
}
message GetPerfSuiteRecordResp {
	PerfSuiteRecord record = 1;
}

message CreatePerfPlanRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 计划执行ID

	string project_id = 11 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string plan_id = 12 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)", max_len: 64}]; // 计划ID
	string plan_name = 13 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 计划名称
	common.TriggerMode trigger_mode = 15 [(validate.rules).enum = {in: [1, 2]}]; // 触发模式
	int64 target_max_rps = 16 [(validate.rules).int64 = {gt: 0}]; // 目标最大的RPS
	uint32 target_duration = 17 [(validate.rules).uint32 = {gt: 0}]; // 目标压测持续时长（单位为秒）
	common.Protocol protocol = 18 [(validate.rules).enum = {not_in: [0]}]; // 协议
	common.TargetEnvironment target_env = 19 [(validate.rules).enum = {not_in: [0]}]; // 目标环境

	string status = 31 [(validate.rules).string = {max_len: 64}]; // 执行状态（结果）
	common.PerfTaskType task_type = 32 [(validate.rules).enum = {not_in: [0]}]; // 任务类型
	common.PerfTaskExecutionMode execution_mode = 33 [(validate.rules).enum = {not_in: [0]}]; // 执行方式
	repeated common.PerfServiceMetaData services = 34 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 压测相关的服务的元数据
	string executed_by = 35 [(validate.rules).string = {min_len:1, max_len: 64}]; // 执行者的用户ID
	google.protobuf.Timestamp started_at = 36 [(validate.rules).timestamp = {required: true}]; // 开始时间
}
message CreatePerfPlanRecordResp {}

message ModifyPerfPlanRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 计划执行ID

	string project_id = 11 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string plan_id = 12 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)", max_len: 64}]; // 计划ID

	string status = 31 [(validate.rules).string = {max_len: 64}]; // 执行状态（结果）
	google.protobuf.Timestamp ended_at = 32; // 结束时间

	repeated APIMetric api_metrics = 41 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 计划涉及的接口的指标信息
	ErrorMessage err_msg = 42; // 错误信息
}
message ModifyPerfPlanRecordResp{}

message SearchPerfPlanRecordReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string plan_id = 2; // 计划ID

	sqlbuilder.Condition condition = 11; // 查询条件
	sqlbuilder.Pagination pagination = 12; // 查询分页
	repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfPlanRecordResp {
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated SearchPerfPlanRecordItem items = 5;
}

message GetPerfPlanRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 压测计划执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
}
message GetPerfPlanRecordResp {
	PerfPlanRecord record = 1;
}

message SearchPerfCaseRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 压测计划执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID

	sqlbuilder.Condition condition = 11; // 查询条件
	sqlbuilder.Pagination pagination = 12; // 查询分页
	repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfCaseRecordResp {
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated PerfCaseRecord items = 5;
}

message UpdateMonitorURLOfPerfPlanRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 计划执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID

	repeated uint32 commands = 11 [(validate.rules).repeated = {ignore_empty: true, unique: true}]; // 命令号列表
	repeated string grpc_paths = 12 [(validate.rules).repeated = {ignore_empty: true, unique: true}]; // gRPC接口列表
	repeated string http_services = 13 [(validate.rules).repeated = {ignore_empty: true, unique: true}]; // HTTP服务列表
}
message UpdateMonitorURLOfPerfPlanRecordResp {
	repeated MonitorUrl monitor_urls = 1;
}

service StabilityReporter {
	// ListStabilityPlanRecord 获取稳测计划的执行记录
	rpc ListStabilityPlanRecord(ListStabilityPlanRecordReq) returns (ListStabilityPlanRecordResp);

	// SearchStabilityPlanRecord 搜索稳测的执行记录
	rpc SearchStabilityPlanRecord(SearchStabilityPlanRecordReq) returns (SearchStabilityPlanRecordResp);

	// GetStabilityPlanRecord 获取稳测执行报告的计划信息
	rpc GetStabilityPlanRecord(GetStabilityPlanRecordReq) returns (GetStabilityPlanRecordResp);

	// CreateStabilityPlanRecord 创建稳测的执行记录
	rpc CreateStabilityPlanRecord(PutStabilityPlanRecordReq) returns (CreateStabilityPlanRecordResp);

	// ModifyStabilityPlanRecord 修改稳测执行记录
	rpc ModifyStabilityPlanRecord(PutStabilityPlanRecordReq) returns (ModifyStabilityPlanRecordResp);

	// SearchStabilityDeviceRecord 搜索稳测执行报告的总览设备
	rpc SearchStabilityDeviceRecord(SearchStabilityDeviceRecordReq) returns (SearchStabilityDeviceRecordResp);

	// CreateStabilityDeviceRecord 创建稳测设备的执行记录
	rpc CreateStabilityDeviceRecord(PutStabilityDeviceRecordReq) returns (CreateStabilityDeviceRecordResp);

	// ModifyStabilityDeviceRecord 修改稳测设备的执行记录
	rpc ModifyStabilityDeviceRecord(PutStabilityDeviceRecordReq) returns (ModifyStabilityDeviceRecordResp);

	// ListStabilityDeviceStep 获取稳测执行报告的设备步骤日志
	rpc ListStabilityDeviceStep(ListStabilityDeviceStepReq) returns (ListStabilityDeviceStepResp);

	// GetStabilityDevicePerfData 获取稳测执行报告的设备性能数据
	rpc GetStabilityDevicePerfData(GetStabilityDevicePerfDataReq) returns (GetStabilityDevicePerfDataResp);

	// GetStabilityDeviceActivity 获取稳测执行报告的设备Activity统计
	rpc GetStabilityDeviceActivity(GetStabilityDeviceActivityReq) returns (GetStabilityDeviceActivityResp);
}

message ListStabilityPlanRecordReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID，不可为空（不会更新它）
	string plan_id = 2 [(validate.rules).string = {pattern: "(?:^stability_plan_id:.+?)"}]; // 计划ID，不可为空（不会更新它）

	sqlbuilder.Condition condition = 11; // 查询条件
	sqlbuilder.Pagination pagination = 12; // 查询分页
	repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message ListStabilityPlanRecordResp {
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated StabilityPlanRecordItem items = 5;
}

message SearchStabilityPlanRecordReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID，不可为空（不会更新它）
	
	sqlbuilder.Condition condition = 11; // 查询条件
	sqlbuilder.Pagination pagination = 12; // 查询分页
	repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchStabilityPlanRecordResp {
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated StabilityPlanRecordItem items = 5;
}

message GetStabilityPlanRecordReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行ID
}
message GetStabilityPlanRecordResp {
  StabilityPlanRecordItem item = 1;
  StabilityPlanMetaData meta_data = 2;
}

message PutStabilityPlanRecordReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string plan_id = 2 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^stability_plan_id:.+?)", max_len: 64}]; // 计划ID
	string plan_name = 3 [(validate.rules).string = {max_len: 64}]; // 计划名称
	string trigger_mode = 4; // 触发模式
	string task_id = 5 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 6 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 计划执行ID
	
	string status = 21 [(validate.rules).string = {max_len: 64}]; // 执行状态（结果）
	uint32 target_duration = 22 [(validate.rules).uint32 = {ignore_empty: true, gt: 0}]; // 目标运行时长（单位：分）
	string execute_data = 23; // 计划执行数据
	string executed_by = 24 [(validate.rules).string = {max_len: 64}]; // 执行者的用户ID
	uint32 total_device = 25 [(validate.rules).uint32 = {ignore_empty: true, gt: 0}]; // 测试设备总数
	google.protobuf.Timestamp started_at = 26; // 开始时间
	google.protobuf.Timestamp ended_at = 27; // 结束时间

	common.AppInfo app_info = 41; // APP信息

	string err_msg = 51; // 错误信息
}
message CreateStabilityPlanRecordResp {}
message ModifyStabilityPlanRecordResp {}

message SearchStabilityDeviceRecordReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行ID

	sqlbuilder.Condition condition = 11; // 查询条件
	sqlbuilder.Pagination pagination = 12; // 查询分页
	repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchStabilityDeviceRecordResp {
	uint64 current_page = 1;
	uint64 page_size = 2;
	uint64 total_count = 3;
	uint64 total_page = 4;
	repeated StabilityDeviceRecordItem items = 5;
}

message PutStabilityDeviceRecordReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)", max_len: 64}]; // 任务ID
	string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 设备执行ID
	string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)", max_len: 64}]; // 项目ID
	string plan_execute_id = 4 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^execute_id:.+?)", max_len: 64}]; // 计划执行ID
	string udid = 5; // 设备编号
	
	string device = 21; // 设备信息
	string status = 22 [(validate.rules).string = {max_len: 64}]; // 执行状态（结果）
	uint32 crash_count = 23; // crash数量
	uint32 anr_count = 24; // anr数量
	common.StabilityResult result = 25; // 执行结果
	string executed_by = 26 [(validate.rules).string = {max_len: 64}]; // 执行者的用户ID
	google.protobuf.Timestamp started_at = 27; // 开始时间
	google.protobuf.Timestamp ended_at = 28; // 结束时间

	string err_msg = 41; // 错误信息
}
message CreateStabilityDeviceRecordResp {}
message ModifyStabilityDeviceRecordResp {}

message ListStabilityDeviceStepReq {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string udid = 3 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备编号
	
	bool with_content = 11; // 是否返回步骤内容
}
message ListStabilityDeviceStepResp {
	uint64 total_count = 1;
	repeated StabilityDeviceStep items = 2;
}

message GetStabilityDevicePerfDataReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string project_id = 2 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

	string udid = 11 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备编号
	common.PerfDataType data_type = 12 [(validate.rules).enum = {not_in: [0]}]; // 数据类型
}
message GetStabilityDevicePerfDataResp {
	PerfData data = 1;
}

message GetStabilityDeviceActivityReq {
	string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string project_id = 2 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

	string udid = 11 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备编号
}
message GetStabilityDeviceActivityResp {	
	repeated DeviceActivity activities = 1;
}
