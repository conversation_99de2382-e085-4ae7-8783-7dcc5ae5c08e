package registry

import (
	"context"
	"sync"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/products"
)

type registry struct {
	cache map[string]products.NewBusinessLogicFunc

	lock sync.RWMutex
}

var r = &registry{
	cache: make(map[string]products.NewBusinessLogicFunc),
}

func Register(name string, fn products.NewBusinessLogicFunc) {
	r.lock.Lock()
	defer r.lock.Unlock()

	r.cache[name] = fn
}

func NewLogic(ctx context.Context, name string, device_ device.IDevice) (products.BusinessLogic, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	fn, ok := r.cache[name]
	if !ok {
		return nil, errors.Errorf("unsupported product type: %s", name)
	}

	return fn(ctx, device_)
}
