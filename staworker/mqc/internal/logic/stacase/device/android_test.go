package device

import (
	"bytes"
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

//func TestGUIA2(t *testing.T) {
//	driver, err := guia2.NewUSBDriver()
//	if err != nil {
//		t.Fatalf("failed to new driver, error: %+v", err)
//	}
//	defer func() {
//		_ = driver.Dispose()
//	}()
//
//	err = driver.AppLaunch("com.yiyou.ga", guia2.BySelector{ResourceIdID: "com.yiyou.ga:id/home_myself_rel"})
//	if err != nil {
//		t.Fatalf("failed to launch app, error: %+v", err)
//	}
//
//	deviceSize, err := driver.DeviceSize()
//	if err != nil {
//		t.Fatalf("failed to get device size, error: %+v", err)
//	}
//
//	var startX, startY, endX, endY int
//	startX = deviceSize.Width / 2
//	startY = deviceSize.Height / 2
//	endX = startX
//	endY = startY / 2
//	err = driver.Swipe(startX, startY, endX, endY)
//	if err != nil {
//		t.Fatalf("failed to swipe, error: %+v", err)
//	}
//}

func TestGU2(t *testing.T) {
	driver, err := gu2.NewDriver(nil)
	if err != nil {
		t.Fatalf("failed to new driver, error: %+v", err)
	}
	defer func() {
		_ = driver.Close()
	}()

	//if err = driver.StartUIAutomator(); err != nil {
	//	t.Fatalf("failed to start uiautomator, error: %+v", err)
	//}
	//defer func() {
	//	_ = driver.StopUIAutomator()
	//}()

	if err = driver.FindElement(gu2.NewSelector(gu2.ByResourceId("com.yiyou.ga:id/home_myself_rel"))).Click(gu2.WithTimeout(2 * time.Second)); err != nil {
		t.Fatalf("failed to click, error: %+v", err)
	}
}

func TestGetDisplayInfo(t *testing.T) {
	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new device, error: %+v", err)
	}
	defer func() {
		_ = device.Close()
	}()

	info, err := device.DisplayInfo()
	if err != nil {
		t.Fatalf("failed to get display info, error: %+v", err)
	}

	t.Logf("display info: %+v", info)
}

func TestGetOrientation(t *testing.T) {
	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new device, error: %+v", err)
	}
	defer func() {
		_ = device.Close()
	}()

	orientation, err := device.Orientation()
	if err != nil {
		t.Fatalf("failed to get orientation, error: %+v", err)
	}

	t.Logf("orientation: %d", orientation)
}

func TestGetTopActivity(t *testing.T) {
	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new device, error: %+v", err)
	}
	defer func() {
		_ = device.Close()
	}()

	activity, err := device.TopActivity()
	if err != nil {
		t.Fatalf("failed to get top activity, error: %+v", err)
	}

	t.Logf("activity: %+v", activity)
}

func TestIsScreenOn(t *testing.T) {
	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new device, error: %+v", err)
	}
	defer func() {
		_ = device.Close()
	}()

	state := device.IsScreenOn()
	t.Logf("screen state: %t", state)
}

func TestIsLocked(t *testing.T) {
	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new device, error: %+v", err)
	}
	defer func() {
		_ = device.Close()
	}()

	state := device.IsLocked()
	t.Logf("screen state: %t", state)
}

func TestUnlockTheScreen(t *testing.T) {
	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new device, error: %+v", err)
	}
	defer func() {
		_ = device.Close()
	}()

	device.Unlock()
	state1 := device.IsScreenOn()
	state2 := device.IsLocked()
	if !state1 || state2 {
		t.Error("failed to unlock the screen")
	} else {
		t.Log("succeed to unlock the screen")
	}
}

func TestWifi(t *testing.T) {
	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new device, error: %+v", err)
	}
	defer func() {
		_ = device.Close()
	}()

	on := device.IsWifiOn()
	t.Logf("wifi_on: %t", on)

	if err = device.SetWifi(!on); err != nil {
		t.Fatal(err)
	}

	time.Sleep(5 * time.Second)
	on = device.IsWifiOn()
	t.Logf("wifi_on: %t", on)

	if err = device.SetWifi(!on); err != nil {
		t.Fatal(err)
	}

	time.Sleep(5 * time.Second)
	on = device.IsWifiOn()
	t.Logf("wifi_on: %t", on)
}

func TestIME(t *testing.T) {
	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new device, error: %+v", err)
	}
	defer func() {
		_ = device.Close()
	}()

	method, shown, err := device.CurrentIME()
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("current ime: %s, shown: %t", method, shown)

	fastInputIME := "com.github.uiautomator/.FastInputIME"
	if err = device.SetIME(fastInputIME, true); err != nil {
		t.Fatal(err)
	}

	time.Sleep(time.Second)
	method, shown, err = device.CurrentIME()
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("current ime: %s, shown: %t", method, shown)

	if err = device.SetIME(fastInputIME, false); err != nil {
		t.Fatal(err)
	}

	time.Sleep(time.Second)
	method, shown, err = device.CurrentIME()
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("current ime: %s, shown: %t", method, shown)
}

//func TestGetCPUInfo(t *testing.T) {
//	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
//	if err != nil {
//		t.Fatalf("failed to new device, error: %+v", err)
//	}
//	defer func() {
//		_ = device.Close()
//	}()
//
//	usage, err := device.GetCUPUsage("com.yiyou.ga")
//	if err != nil {
//		t.Fatalf("failed to get cpu usage, error: %+v", err)
//	}
//	t.Logf("cpu usage: %f", usage)
//}
//
//func TestGetMemoryUsage(t *testing.T) {
//	device, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
//	if err != nil {
//		t.Fatalf("failed to new device, error: %+v", err)
//	}
//	defer func() {
//		_ = device.Close()
//	}()
//
//	usage, err := device.GetMemoryUsage("com.yiyou.ga")
//	if err != nil {
//		t.Fatalf("failed to get memory usage, error: %+v", err)
//	}
//	t.Logf("memory usage: %d", usage)
//}

func TestAndroidDevice_MonkeyTest(t *testing.T) {
	currentPath, _ := os.Getwd()

	type fields struct {
		ctx           context.Context
		deviceType    commonpb.DeviceType
		serial        string
		remoteAddress string
	}
	type args struct {
		key         string
		packageName string
		duration    int64
		timeout     time.Duration
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "monkey test 3 mins",
			fields: fields{
				ctx:           context.Background(),
				deviceType:    commonpb.DeviceType_REAL_PHONE,
				serial:        "",
				remoteAddress: "",
			},
			args: args{
				key:         "task_id_1",
				packageName: "com.yiyou.ga",
				duration:    3, // 3 mins
				timeout:     4 * time.Minute,
			},
			wantErr: false,
		},
		{
			name: "monkey test stopped before finished",
			fields: fields{
				ctx:           context.Background(),
				deviceType:    commonpb.DeviceType_REAL_PHONE,
				serial:        "",
				remoteAddress: "",
			},
			args: args{
				key:         "task_id_2",
				packageName: "com.yiyou.ga",
				duration:    3, // 3 mins
				timeout:     2 * time.Minute,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				ctx, cancel := context.WithTimeout(tt.fields.ctx, tt.args.timeout)
				defer cancel()

				reportPath := filepath.Join(currentPath, tt.args.key)
				_ = os.RemoveAll(reportPath)
				_ = os.MkdirAll(reportPath, 0o755)

				d, err := NewAndroidDevice(ctx, tt.fields.deviceType, tt.fields.serial, tt.fields.remoteAddress)
				if err != nil {
					t.Fatalf("failed to new android device, error: %+v", err)
				}
				defer func(d *AndroidDevice) {
					if d != nil {
						_ = d.Close()
					}
				}(d)

				result, err := d.MonkeyTest(ctx, tt.args.key, tt.args.packageName, tt.args.duration, reportPath)
				if (err != nil) != tt.wantErr {
					t.Errorf("MonkeyTest() error = %+v, wantErr %v", err, tt.wantErr)
				}

				t.Logf("result: %s", jsonx.MarshalIgnoreError(result))
			},
		)
	}
}

func TestAndroidDevice_Screenshot(t *testing.T) {
	type fields struct {
		ctx           context.Context
		deviceType    commonpb.DeviceType
		serial        string
		remoteAddress string
	}
	type args struct {
		remotePath string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "",
			fields: fields{
				ctx:           context.Background(),
				deviceType:    commonpb.DeviceType_REAL_PHONE,
				serial:        "",
				remoteAddress: "",
			},
			args: args{
				remotePath: "/sdcard/fastbot/screenshot/测试截图-1.png",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				d, err := NewAndroidDevice(
					tt.fields.ctx, tt.fields.deviceType, tt.fields.serial, tt.fields.remoteAddress,
				)
				if err != nil {
					t.Fatalf("failed to new android device, error: %+v", err)
				}
				defer func(d *AndroidDevice) {
					if d != nil {
						_ = d.Close()
					}
				}(d)

				if err = d.Screenshot(tt.args.remotePath); (err != nil) != tt.wantErr {
					t.Errorf("Screenshot() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func TestScreenCapToLocal(t *testing.T) {
	d, err := NewAndroidDevice(context.Background(), commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatal(err)
	}

	s, err := d.device.NewSession()
	if err != nil {
		t.Fatal(err)
	}

	file, err := os.OpenFile("./cap.png", os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0o644)
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		_ = file.Close()
	}()
	s.Stdout = file

	buf := new(bytes.Buffer)
	s.Stderr = buf

	err = s.Run("screencap -p")
	if err != nil {
		t.Fatal(err)
	} else if buf.Len() > 0 {
		t.Fatal(buf.String())
	}

	fi, err := file.Stat()
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("file: %s, %d", fi.Name(), fi.Size())
}
