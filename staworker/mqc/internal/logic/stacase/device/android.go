package device

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/dlclark/regexp2"
	"github.com/electricbubble/gadb"
	"github.com/pkg/errors"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"
	"go.uber.org/multierr"

	// gu2 "github.com/electricbubble/guia2" // client package for https://github.com/appium/appium-uiautomator2-server
	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2" // client package for https://github.com/openatx/android-uiautomator-server
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/collector"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

const (
	processNameOfFastbot = "com.android.commands.monkey"
	basePathOnAndroid    = "/data/local/tmp"
	sdcardPathOnAndroid  = "/sdcard"

	dirNameOfFastbot    = "fastbot"
	dirNameOfScreenshot = "screenshot"

	groupNameOfANRCount     = "anr_count"
	groupNameOfCrashCount   = "crash_count"
	groupNameOfX            = "x"
	groupNameOfY            = "y"
	groupNameOfWidth        = "width"
	groupNameOfHeight       = "height"
	groupNameOfDensity      = "density"
	groupNameOfOrientation  = "orientation"
	groupNameOfPackageName  = "package_name"
	groupNameOfActivityName = "activity_name"
	groupNameOfPid          = "pid"
	groupNameOfMethodID     = "method_id"

	strOfMonkeyAborted         = "monkey aborted"
	strOfNoSuchFileOrDirectory = "No such file or directory"
	strOfTrue                  = "true"
	strOfScreenStateOn         = "SCREEN_STATE_ON"

	commandOfGetWindowSize     = "wm size"
	commandOfGetDisplay        = "dumpsys display"
	commandOfGetWindow         = "dumpsys window"
	commandOfGetSurfaceFlinger = "dumpsys SurfaceFlinger"
	commandOfGetInput          = "dumpsys input"
	commandOfGetWindowDisplays = "dumpsys window displays"
	commandOfInstallApp        = "pm install -r -t %s"
	commandOfUninstallApp      = "pm uninstall %s"
	commandOfGetVersion        = "dumpsys package %s | grep versionName"
	commandOfLaunchApp         = "monkey -p %s -c android.intent.category.LAUNCHER 1"
	commandOfStopApp           = "am force-stop %s"
	commandOfGetTopActivity    = "dumpsys activity top"
	commandOfGetWindowPolicy   = "dumpsys window policy"
	commandOfGetWifiOn         = "settings get global wifi_on"
	commandOfEnableWifi        = "svc wifi enable"
	commandOfDisableWifi       = "svc wifi disable"
	commandOfGetInputMethod    = "dumpsys input_method"
	commandOfEnableIME         = "ime enable %s"
	commandOfDisableIME        = "ime disable %s"
	commandOfSetIME            = "ime set %s"
	commandOfResetIME          = "ime reset"
	commandOfListPackages      = "pm list packages"
	commandOfScreenshotByADB   = "screencap -p %s"
	commandOfMonkeyTest        = `CLASSPATH=/sdcard/monkeyq.jar:/sdcard/framework.jar:/sdcard/fastbot-thirdpart.jar exec app_process /system/bin com.android.commands.monkey.Monkey -p %s --agent reuseq --running-minutes %d --throttle %d --bugreport --output-directory %s -v -v`

	commandOfBackKeyEvent   = "input keyevent 4"   // https://developer.android.com/reference/android/view/KeyEvent.html#KEYCODE_BACK
	commandOfPowerKeyEvent  = "input keyevent 26"  // https://developer.android.com/reference/android/view/KeyEvent.html#KEYCODE_POWER
	commandOfMenuKeyEvent   = "input keyevent 82"  // https://developer.android.com/reference/android/view/KeyEvent.html#KEYCODE_MENU
	commandOfWakeupKeyEvent = "input keyevent 224" // https://developer.android.com/reference/android/view/KeyEvent.html#KEYCODE_WAKEUP

	filenameOfMaxValidStrings          = "max.valid.strings"
	filenameOfActivityWhiteListStrings = "awl.strings"
	filenameOfMaxActivityStatisticsLog = "max.activity.statistics.log"
	filenameOfFastbotLog               = "fastbot.log"
	filenameOfCrashDumpLog             = "crash-dump.log"
	filenameOfOOMTracesLog             = "oom-traces.log"

	timeoutOfPushMaxValidStrings          = 10 * time.Second
	timeoutOfPushActivityWhiteListStrings = 10 * time.Second
)

var (
	fastbotANRCountAndCrashCountRE = regexp2.MustCompile(
		fmt.Sprintf(`// App appears (?<%s>\d+) crash, (?<%s>\d+) anr`, groupNameOfCrashCount, groupNameOfANRCount),
		regexp2.None,
	)
	windowSizeRE = regexp2.MustCompile(
		fmt.Sprintf(`(?<%s>\d+)x(?<%s>\d+)\s*$`, groupNameOfWidth, groupNameOfHeight), regexp2.None,
	)
	physicalDisplayRE = regexp2.MustCompile(
		fmt.Sprintf(
			`.*PhysicalDisplayInfo{(?<%s>\d+) x (?<%s>\d+), .*, density (?<%s>[\d.]+).*`,
			groupNameOfWidth, groupNameOfHeight, groupNameOfDensity,
		),
		regexp2.None,
	)
	unrestrictedScreenRE = regexp2.MustCompile(
		fmt.Sprintf(
			`\s*mUnrestrictedScreen=\((?<%s>\d+),(?<%s>\d+)\) (?<%s>\d+)x(?<%s>\d+)`,
			groupNameOfX, groupNameOfY, groupNameOfWidth, groupNameOfHeight,
		), regexp2.None,
	)
	displayWHRE = regexp2.MustCompile(
		fmt.Sprintf(`\s*DisplayWidth=(?<%s>\d+) *DisplayHeight=(?<%s>\d+)`, groupNameOfWidth, groupNameOfHeight),
		regexp2.None,
	)
	orientationRE = regexp2.MustCompile(
		fmt.Sprintf(`orientation=(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	surfaceOrientationRE = regexp2.MustCompile(
		fmt.Sprintf(`SurfaceOrientation:\s+(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	displayFramesRE = regexp2.MustCompile(
		fmt.Sprintf(`DisplayFrames.*r=(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	activityRE = regexp2.MustCompile(
		fmt.Sprintf(
			`\s*ACTIVITY (?<%s>[A-Za-z0-9_.$]+)/(?<%s>[A-Za-z0-9_.$]+) \w+ pid=(?<%s>\d+)`,
			groupNameOfPackageName, groupNameOfActivityName, groupNameOfPid,
		), regexp2.None,
	)
	screenOnRE    = regexp2.MustCompile("mScreenOnFully=(true|false)", regexp2.None)
	screenStateRE = regexp2.MustCompile("screenState=(SCREEN_STATE_ON|SCREEN_STATE_OFF)", regexp2.None)
	lockScreenRE  = regexp2.MustCompile(
		"(?:mShowingLockscreen|isStatusBarKeyguard|showing)=(true|false)", regexp2.None,
	)
	currentMethodIdRE = regexp2.MustCompile(
		fmt.Sprintf("mCurMethodId=(?<%s>[-_./\\w]+)", groupNameOfMethodID), regexp2.None,
	)
	methodShownRE           = regexp2.MustCompile("mInputShown=(true|false)", regexp2.None)
	manualConfirmationTexts = []string{"允许安装", "继续安装", "安装", "确定", "完成"}
	artifactLogMapping      = map[string]string{
		filenameOfFastbotLog:   "Fastbot日志",
		filenameOfCrashDumpLog: "应用Crash日志",
		filenameOfOOMTracesLog: "系统ANR日志",
	}

	_ IDevice = (*AndroidDevice)(nil)
)

type (
	AndroidDeviceOption  func(*androidDeviceOptions)
	androidDeviceOptions struct {
		apkPath    string
		activities []string
		throttle   int64
	}

	AndroidDevice struct {
		logx.Logger
		ctx context.Context

		deviceType            commonpb.DeviceType
		serial, remoteAddress string
		androidDeviceOptions

		client *gadb.Client
		device *gadb.Device

		displayInfo *DisplayInfo
	}
)

func WithAPKPath(apkPath string) AndroidDeviceOption {
	return func(o *androidDeviceOptions) {
		o.apkPath = apkPath
	}
}

func WithActivities(activities ...string) AndroidDeviceOption {
	return func(o *androidDeviceOptions) {
		o.activities = activities
	}
}

func WithThrottle(throttle int64) AndroidDeviceOption {
	return func(o *androidDeviceOptions) {
		o.throttle = throttle
	}
}

func NewAndroidDevice(
	ctx context.Context, deviceType commonpb.DeviceType, serial, remoteAddress string, opts ...AndroidDeviceOption,
) (*AndroidDevice, error) {
	client, device, err := utils.ADBConnect(serial, remoteAddress)
	if err != nil {
		return nil, err
	}

	if serial == "" {
		serial = device.Serial()
	}

	o := androidDeviceOptions{}
	for _, opt := range opts {
		opt(&o)
	}

	d := &AndroidDevice{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		deviceType:           deviceType,
		serial:               serial,
		remoteAddress:        remoteAddress,
		androidDeviceOptions: o,

		client: client,
		device: device,
	}

	d.displayInfo, err = d.DisplayInfo()
	if err != nil {
		return nil, err
	}

	return d, nil
}

func (d *AndroidDevice) Close() (err error) {
	return err
}

func (d *AndroidDevice) DeviceType() commonpb.DeviceType {
	return d.deviceType
}

func (d *AndroidDevice) PlatformType() commonpb.PlatformType {
	return commonpb.PlatformType_ANDROID
}

func (d *AndroidDevice) UDID() string {
	return d.serial
}

func (d *AndroidDevice) RemoteAddress() string {
	return d.remoteAddress
}

func (d *AndroidDevice) ADB() *gadb.Device {
	return d.device
}

func (d *AndroidDevice) DisplayInfo() (*DisplayInfo, error) {
	var err error
	for _, item := range []*findAndMatch{
		{
			cmd: commandOfGetWindowSize,
			re:  windowSizeRE,
		},
		{
			cmd: commandOfGetDisplay,
			re:  physicalDisplayRE,
		},
		{
			cmd: commandOfGetWindow,
			re:  unrestrictedScreenRE,
		},
		{
			cmd: commandOfGetWindow,
			re:  displayWHRE,
		},
	} {
		di, e := d.getDisplayInfo(item)
		if e != nil {
			err = multierr.Append(err, e)
			continue
		}

		return di, nil
	}

	return nil, err
}

func (d *AndroidDevice) getDisplayInfo(fm *findAndMatch) (*DisplayInfo, error) {
	output, err := d.device.RunShellCommand(fm.cmd)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the display info, serial: %s", d.serial)
	}

	match, err := fm.re.FindStringMatch(output)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to search for the Regexp, serial: %s, output: %s", d.serial, output)
	} else if match == nil {
		return nil, errors.Errorf("no content matching the Regexp was found, serial: %s, output: %s", d.serial, output)
	}

	group := match.GroupByName(groupNameOfWidth)
	if group == nil {
		return nil, errors.Errorf("failed to get the width, serial: %s, match: %s", d.serial, match.String())
	}
	width, err := strconv.Atoi(group.String())
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to convert the width to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	group = match.GroupByName(groupNameOfHeight)
	if group == nil {
		return nil, errors.Errorf("failed to get the height, serial: %s, match: %s", d.serial, match.String())
	}
	height, err := strconv.Atoi(group.String())
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to convert the height to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	return &DisplayInfo{
		Width:  width,
		Height: height,
	}, nil
}

func (d *AndroidDevice) Orientation() (int, error) {
	var err error
	for _, item := range []*findAndMatch{
		{
			cmd: commandOfGetSurfaceFlinger,
			re:  orientationRE,
		},
		{
			cmd: commandOfGetInput,
			re:  surfaceOrientationRE,
		},
		{
			cmd: commandOfGetWindowDisplays,
			re:  displayFramesRE,
		},
	} {
		ori, e := d.getOrientation(item)
		if e != nil {
			err = multierr.Append(err, e)
			continue
		}

		return ori, nil
	}

	return 0, err
}

func (d *AndroidDevice) getOrientation(fm *findAndMatch) (int, error) {
	output, err := d.device.RunShellCommand(fm.cmd)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to get the orientation, serial: %s", d.serial)
	}

	match, err := fm.re.FindStringMatch(output)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to search for the Regexp, serial: %s, output: %s", d.serial, output)
	} else if match == nil {
		return 0, errors.Errorf("no content matching the Regexp was found, serial: %s, output: %s", d.serial, output)
	}

	group := match.GroupByName(groupNameOfOrientation)
	if group == nil {
		return 0, errors.Errorf("failed to get the orientation, serial: %s, match: %s", d.serial, match.String())
	}
	orientation, err := strconv.Atoi(group.String())
	if err != nil {
		return 0, errors.Wrapf(
			err, "failed to convert the orientation to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	return orientation, nil
}

func (d *AndroidDevice) IsExists(filepath string) bool {
	output, err := d.device.RunShellCommand("ls", filepath)
	if err != nil {
		return false
	}

	return !strings.Contains(output, strOfNoSuchFileOrDirectory)
}

func (d *AndroidDevice) AppInstall(appPath string) error {
	var (
		needToReinstall, needToInstall bool

		driver *gu2.Driver
		exitCh chan lang.PlaceholderType
	)

	defer func() {
		if exitCh != nil {
			close(exitCh)
		}
		if driver != nil {
			_ = driver.Close()
		}
	}()

	info, err := apk.OpenFile(appPath)
	if err != nil {
		return errors.Wrapf(err, "failed to open the apk file, serial: %s, file: %s", d.serial, appPath)
	}

	packageName := info.PackageName()
	packages, err := d.ListPackages(WithFilterOfPackageName(packageName))
	if err != nil {
		return err
	}

	var cmd, output string
	if packages.Size() != 0 {
		cmd = fmt.Sprintf(commandOfGetVersion, packageName)
		output, err = d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(
				err, "failed to get the version of the app, serial: %s, package_name: %s", d.serial, packageName,
			)
		}

		target := strings.TrimSpace(output)
		target = strings.TrimPrefix(target, "versionName=")
		source := info.Manifest().VersionName

		if !strings.EqualFold(source, target) {
			d.Infof(
				"need to reinstall the app, serial: %s, file: %s, package_name: %s, version: %s(%s => %s)",
				d.serial, appPath, info.PackageName(), source, target,
			)
			needToInstall = true
			needToReinstall = true
		}
	} else {
		needToInstall = true
	}

	if needToInstall {
		if needToReinstall {
			cmd = fmt.Sprintf(commandOfUninstallApp, packageName)
			if _, err = d.device.RunShellCommand(cmd); err != nil {
				return errors.Wrapf(
					err, "failed to uninstall the app, serial: %s, package_name: %s", d.serial, packageName,
				)
			}
		}

		pathOnDevice := filepath.Join(basePathOnAndroid, fmt.Sprintf("%s.apk", info.PackageName()))
		if err = d.device.PushFile(appPath, pathOnDevice); err != nil {
			return errors.Wrapf(
				err,
				"failed to push apk file to the device, serial: %s, file: %s, path: %s",
				d.serial, appPath, pathOnDevice,
			)
		}

		// if the device is an Android real phone, then try to check whether
		// it requires manual confirmation when installing an app.
		if d.deviceType == commonpb.DeviceType_REAL_PHONE {
			driver, err = gu2.NewDriver(d.device)
			if err != nil {
				d.Errorf("failed to new uiautomator2 driver, serial: %s, error: %+v", d.serial, err)
			} else {
				exitCh = make(chan lang.PlaceholderType)
				threading.GoSafeCtx(
					d.ctx, func() {
						d.appInstallWithManualConfirmation(driver, exitCh)
					},
				)
			}
		}

		cmd = fmt.Sprintf(commandOfInstallApp, pathOnDevice)
		output, err = d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(err, "failed to run the shell command, serial: %s, command: %q", d.serial, cmd)
		}

		if d.deviceType == commonpb.DeviceType_REAL_PHONE {
			// sleep for 3 seconds to avoid missing the pop-up window after installing the app.
			time.Sleep(3 * time.Second)
		}
		d.Infof(
			"succeed to install the app, serial: %s, file: %s, package_name: %s, version: %s, command: %q, result: %s",
			d.serial, appPath, info.PackageName(), info.Manifest().VersionName, cmd, output,
		)
	} else {
		d.Debugf(
			"the app has been installed, serial: %s, file: %s, package_name: %s, version: %s",
			d.serial, appPath, info.PackageName(), info.Manifest().VersionName,
		)
	}

	return nil
}

func (d *AndroidDevice) appInstallWithManualConfirmation(driver *gu2.Driver, exitCh <-chan lang.PlaceholderType) {
	for {
		select {
		case <-d.ctx.Done():
			d.Errorf("got a done signal while installing the app, serial: %s, error: %+v", d.serial, d.ctx.Err())
			return
		case <-exitCh:
			d.Debugf("got an exit signal while installing the app, serial: %s", d.serial)
			return
		default:
			for _, text := range manualConfirmationTexts {
				element := driver.FindElementBySelectorOptions(gu2.ByText(text))
				if element == nil {
					continue
				} else if ok, err := element.Exist(); err != nil || !ok {
					continue
				} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
					continue
				}

				d.Debugf("click th element successfully, serial: %s, text: %s", d.serial, text)
				break
			}

			time.Sleep(time.Second)
		}
	}
}

func (d *AndroidDevice) AppUninstall(packageName string) error {
	packages, err := d.ListPackages(WithFilterOfPackageName(packageName))
	if err != nil {
		return err
	}

	if packages.Size() != 0 {
		cmd := fmt.Sprintf(commandOfUninstallApp, packageName)
		output, err := d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(err, "failed to uninstall the app, serial: %s, package_name: %s", d.serial, packageName)
		}

		d.Infof("succeed to uninstall the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output)
	} else {
		d.Debugf("the app has not been installed, serial: %s, package_name: %s", d.serial, packageName)
	}

	return nil
}

func (d *AndroidDevice) AppLaunch(packageName string) error {
	cmd := fmt.Sprintf(commandOfLaunchApp, packageName)
	output, err := d.device.RunShellCommand(cmd)
	if err != nil {
		return errors.Wrapf(err, "failed to launch the app, serial: %s, package_name: %s", d.serial, packageName)
	}

	output = strings.TrimSpace(output)
	if strings.Contains(output, strOfMonkeyAborted) {
		return errors.Errorf(
			"failed to launch the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output,
		)
	}

	d.Infof("succeed to launch the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output)
	return nil
}

func (d *AndroidDevice) AppStop(packageName string) error {
	cmd := fmt.Sprintf(commandOfStopApp, packageName)
	output, err := d.device.RunShellCommand(cmd)
	if err != nil {
		return errors.Wrapf(err, "failed to stop the app, serial: %s, package_name: %s", d.serial, packageName)
	}

	d.Infof("succeed to stop the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output)
	return nil
}

func (d *AndroidDevice) TopActivity() (*Activity, error) {
	output, err := d.device.RunShellCommand(commandOfGetTopActivity)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the top activity, serial: %s", d.serial)
	}

	match, err := activityRE.FindStringMatch(output)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to match the top activity, serial: %s, output: %s", d.serial, output)
	}

	activities := make([]*Activity, 0, constants.ConstDefaultMakeSliceSize)
	fn := func() error {
		defer func() {
			match, _ = activityRE.FindNextMatch(match)
		}()

		group := match.GroupByName(groupNameOfPackageName)
		if group == nil {
			return errors.Errorf(
				"failed to get the package name from the top activity, serial: %s, match: %s", d.serial, match.String(),
			)
		}
		packageName := group.String()

		group = match.GroupByName(groupNameOfActivityName)
		if group == nil {
			return errors.Errorf(
				"failed to get the activity name from the top activity, serial: %s, match: %s",
				d.serial, match.String(),
			)
		}
		activityName := group.String()

		group = match.GroupByName(groupNameOfPid)
		if group == nil {
			return errors.Errorf(
				"failed to get the pid from the top activity, serial: %s, match: %s", d.serial, match.String(),
			)
		}
		pid, err := strconv.ParseInt(group.String(), 10, 64)
		if err != nil {
			return errors.Wrapf(
				err,
				"failed to parse the pid from the top activity, serial: %s, match: %s, group: %s",
				d.serial, match.String(), group.String(),
			)
		}

		activities = append(
			activities, &Activity{
				PackageName:  packageName,
				ActivityName: activityName,
				Pid:          pid,
			},
		)
		return nil
	}
	for match != nil {
		if err = fn(); err != nil {
			break
		}
	}
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the top activity, serial: %s, output: %s", d.serial, output)
	} else if len(activities) == 0 {
		return nil, errors.Errorf("not found any activities, serial: %s, output: %s", d.serial, output)
	}

	return activities[len(activities)-1], nil
}

func (d *AndroidDevice) PressPower() {
	_, _ = d.device.RunShellCommand(commandOfPowerKeyEvent)
}

func (d *AndroidDevice) PressBack() {
	_, _ = d.device.RunShellCommand(commandOfBackKeyEvent)
}

func (d *AndroidDevice) IsScreenOn() bool {
	output, err := d.device.RunShellCommand(commandOfGetWindowPolicy)
	if err != nil {
		return false
	}

	match, err := screenOnRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfTrue
		}
	}

	match, err = screenStateRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfScreenStateOn
		}
	}

	return false
}

func (d *AndroidDevice) Wakeup() {
	_, _ = d.device.RunShellCommand(commandOfWakeupKeyEvent)
}

func (d *AndroidDevice) IsLocked() bool {
	output, err := d.device.RunShellCommand(commandOfGetWindowPolicy)
	if err != nil {
		return false
	}

	match, err := lockScreenRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfTrue
		}
	}

	return false
}

// Unlock the screen
// NOTE: might not work on all devices
func (d *AndroidDevice) Unlock() {
	_, _ = d.device.RunShellCommand(commandOfMenuKeyEvent)
}

func (d *AndroidDevice) IsWifiOn() bool {
	output, err := d.device.RunShellCommand(commandOfGetWifiOn)
	if err != nil {
		return false
	}

	return strings.TrimSpace(output) == "1"
}

func (d *AndroidDevice) SetWifi(enable bool) error {
	var (
		action, output string
		err            error
	)

	if enable {
		action = "enable"
		output, err = d.device.RunShellCommand(commandOfEnableWifi)
	} else {
		action = "disable"
		output, err = d.device.RunShellCommand(commandOfDisableWifi)
	}
	if err != nil {
		return errors.Wrapf(err, "failed to set wifi to %s, serial: %s, output: %s", action, d.serial, output)
	}

	return nil
}

func (d *AndroidDevice) CurrentIME() (method string, shown bool, err error) {
	output, err := d.device.RunShellCommand(commandOfGetInputMethod)
	if err != nil {
		return "", false, errors.Wrapf(err, "failed to get the input method, serial: %s", d.serial)
	}

	match, err := currentMethodIdRE.FindStringMatch(output)
	if err != nil {
		return "", false, errors.Wrapf(
			err, "failed to match the current method id, serial: %s, output: %s", d.serial, output,
		)
	}

	group := match.GroupByName(groupNameOfMethodID)
	if group == nil {
		return "", false, errors.Errorf(
			"failed to get the current method id from the input method, serial: %s, match: %s", d.serial,
			match.String(),
		)
	}
	method = group.String()

	match, err = methodShownRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group = match.GroupByNumber(1); group != nil {
			shown = group.String() == strOfTrue
		}
	}

	return method, shown, nil
}

func (d *AndroidDevice) SetIME(method string, enable bool) error {
	var (
		output string
		err    error
	)

	if enable {
		if output, err = d.device.RunShellCommand(fmt.Sprintf(commandOfEnableIME, method)); err != nil {
			return errors.Wrapf(
				err, "failed to enable the input method, serial: %s, method: %s, output: %s", d.serial, method, output,
			)
		}

		if output, err = d.device.RunShellCommand(fmt.Sprintf(commandOfSetIME, method)); err != nil {
			return errors.Wrapf(
				err, "failed to set the input method, serial: %s, method: %s, output: %s", d.serial, method, output,
			)
		}
	} else {
		if output, err = d.device.RunShellCommand(fmt.Sprintf(commandOfDisableIME, method)); err != nil {
			return errors.Wrapf(
				err, "failed to disable the input method, serial: %s, method: %s, output: %s", d.serial, method, output,
			)
		}

		if output, err = d.device.RunShellCommand(commandOfResetIME); err != nil {
			return errors.Wrapf(
				err, "failed to reset the input method, serial: %s, output: %s", d.serial, output,
			)
		}
	}

	return nil
}

type (
	ListPackagesOption func(*listPackagesOptions)

	listPackagesOptions struct {
		OnlySystemPackages     bool
		OnlyThirdPartyPackages bool
		FilterOfPackageName    string
	}
)

func WithOnlySystemPackages() ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.OnlySystemPackages = true
	}
}

func WithOnlyThirdPartyPackages() ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.OnlyThirdPartyPackages = true
	}
}

func WithFilterOfPackageName(filter string) ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.FilterOfPackageName = filter
	}
}

func (d *AndroidDevice) ListPackages(opts ...ListPackagesOption) (*set.Set[string], error) {
	o := &listPackagesOptions{}
	for _, opt := range opts {
		opt(o)
	}

	cmd := commandOfListPackages
	if o.OnlySystemPackages {
		cmd += " -s"
	}
	if o.OnlyThirdPartyPackages {
		cmd += " -3"
	}
	if o.FilterOfPackageName != "" {
		cmd += " " + o.FilterOfPackageName
	}

	output, err := d.device.RunShellCommand(cmd)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to list the packages, serial: %s", d.serial)
	}

	output = strings.TrimSpace(output)
	lines := strings.Split(output, "\n")
	packages := set.NewHashset(uint64(len(lines)), generic.Equals, generic.HashString)
	for _, line := range lines {
		if line == "" {
			continue
		}

		fields := strings.Split(line, ":")
		if len(fields) < 2 {
			continue
		}

		packages.Put(fields[1])
	}

	return &packages, nil
}

func (d *AndroidDevice) ListThirdPartyPackages() (*set.Set[string], error) {
	return d.ListPackages(WithOnlyThirdPartyPackages())
}

func (d *AndroidDevice) MonkeyTest(
	ctx context.Context, key, packageName string, duration int64, reportPath string,
) (Result, error) {
	opts := make([]FastbotOption, 0, 5)
	if duration > 0 {
		opts = append(opts, WithFastbotDuration(duration))
	}
	if reportPath != "" {
		opts = append(opts, WithFastbotReportPath(reportPath))
	}
	if d.apkPath != "" {
		opts = append(opts, WithFastbotAppPath(d.apkPath))
	}
	if len(d.activities) > 0 {
		opts = append(opts, WithFastbotActivities(d.activities...))
	}
	if d.throttle > 0 {
		opts = append(opts, WithFastbotThrottle(d.throttle))
	}
	return d.runFastbot(ctx, key, packageName, opts...)
}

func (d *AndroidDevice) CollectPerfData(
	ctx context.Context, packageName string, opts ...collector.Option,
) error {
	if d.deviceType != commonpb.DeviceType_REAL_PHONE {
		d.Infof(
			"no perf data collection is required for non-real devices, device_type: %s, serial: %s, package_name: %s",
			d.deviceType, d.serial, packageName,
		)
		return nil
	}

	c := collector.NewAndroidCollector(ctx, d.device, packageName, opts...)
	if err := c.Start(); err != nil {
		return err
	}
	defer func(c *collector.AndroidCollector) {
		if c != nil {
			_ = c.Stop()
		}
	}(c)

	<-ctx.Done()
	return nil
}

func (d *AndroidDevice) ScreenRecording(
	ctx context.Context, key, filenamePrefix string, interval time.Duration, reportPath string,
	artifactCh chan<- Artifact,
) error {
	var (
		localOutputPath, remoteOutputPath string
		count                             = 0
	)

	// 创建本地输出目录
	if !strings.HasSuffix(reportPath, d.serial) {
		localOutputPath = filepath.Join(reportPath, d.serial, dirNameOfScreenshot)
	} else {
		localOutputPath = filepath.Join(reportPath, dirNameOfScreenshot)
	}
	_ = os.MkdirAll(localOutputPath, 0o755)

	remoteOutputPath = filepath.Join(sdcardPathOnAndroid, dirNameOfScreenshot, key)
	d.removeScreenshotFiles(remoteOutputPath)
	defer func() {
		if e := d.saveScreenshotFiles(localOutputPath, remoteOutputPath); e != nil {
			d.Errorf("failed to save the screenshot files, serial: %s, error: %s", d.serial, e)
		} else {
			d.removeScreenshotFiles(remoteOutputPath)
		}
	}()

	if interval <= 0 || interval < minScreenshotInterval {
		interval = minScreenshotInterval
	}
	ticker := timewheel.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-d.ctx.Done():
			d.Debugf("got a done signal while screen recording, serial: %s, key: %s", d.serial, key)
			return nil
		case <-ctx.Done():
			d.Debugf("got a done signal while screen recording, serial: %s, key: %s", d.serial, key)
			return nil
		case <-ticker.C:
			name := fmt.Sprintf("%s-%d%s", filenamePrefix, count, constants.ConstSuffixOfPng)
			path := fmt.Sprintf("%s/%s", remoteOutputPath, name)
			if err := d.Screenshot(path); err != nil {
				d.Errorf("failed to take screenshot, serial: %s, path: %s, error: %+v", d.serial, path, err)
			} else {
				d.Debugf("take screenshot successfully, serial: %s, path: %s", d.serial, path)

				count++
				artifact := Artifact{
					Type:        commonpb.ArtifactType_ArtifactType_SCREENSHOT,
					FileName:    name,
					FilePath:    path,
					Description: "",
				}

				// 立即通过channel发送Artifact
				select {
				case artifactCh <- artifact:
					d.Debugf("sent artifact through channel, serial: %s, file: %s", d.serial, name)
				case <-d.ctx.Done():
					d.Debugf("context done while sending artifact, serial: %s, file: %s", d.serial, name)
					return nil
				case <-ctx.Done():
					d.Debugf("context done while sending artifact, serial: %s, file: %s", d.serial, name)
					return nil
				}
			}
		}
	}
}

func (d *AndroidDevice) RunShellCommand(cmd string, args ...string) (string, error) {
	return d.device.RunShellCommand(cmd, args...)
}

type (
	FastbotOption func(*fastbotOptions)

	fastbotOptions struct {
		duration   int64    // 运行时间，单位：分钟
		throttle   int64    // 时间频率
		reportPath string   // 报告地址
		appPath    string   // App路径，非空表示尝试添加限定词
		activities []string // Activity白名单配置
	}

	// outputHandler 输出处理器，封装输出相关的资源
	outputHandler struct {
		localPath   string             // 本地输出路径
		logPath     string             // 日志文件路径
		logFile     *os.File           // 日志文件句柄
		interceptor *StreamInterceptor // 流拦截器
	}
)

// Close 关闭输出处理器的资源
func (h *outputHandler) Close() {
	if h.interceptor != nil {
		_ = h.interceptor.Close()
	}
	if h.logFile != nil {
		_ = h.logFile.Close()
	}
}

func WithFastbotDuration(duration int64) FastbotOption {
	return func(o *fastbotOptions) {
		if duration < minDuration {
			duration = minDuration
		} else if duration > maxDuration {
			duration = maxDuration
		} else {
			o.duration = duration
		}
	}
}

func WithFastbotThrottle(throttle int64) FastbotOption {
	return func(o *fastbotOptions) {
		if throttle < minThrottle {
			throttle = minThrottle
		} else if throttle > maxThrottle {
			throttle = maxThrottle
		} else {
			o.throttle = throttle
		}
	}
}

func WithFastbotReportPath(reportPath string) FastbotOption {
	return func(o *fastbotOptions) {
		o.reportPath = reportPath
	}
}

func WithFastbotAppPath(appPath string) FastbotOption {
	return func(o *fastbotOptions) {
		o.appPath = appPath
	}
}

func WithFastbotActivities(activities ...string) FastbotOption {
	return func(o *fastbotOptions) {
		o.activities = make([]string, 0, len(activities))
		for _, activity := range activities {
			if activity == "" {
				continue
			}
			o.activities = append(o.activities, activity)
		}
	}
}

func (d *AndroidDevice) runFastbot(ctx context.Context, key, packageName string, opts ...FastbotOption) (
	result Result, err error,
) {
	// 1. 处理传入的`fastbot`选项
	o := d.applyFastbotOptions(opts...)

	// 2. 推送必要文件到设备
	d.pushRequiredFiles(ctx, o)

	// 3. 设置输出处理
	output := d.setupOutputHandling(o, &result)
	defer output.Close()

	// 4. 设置远程输出路径
	outputPath := filepath.Join(sdcardPathOnAndroid, dirNameOfFastbot, key)

	// 5. 设置结果收集的延迟执行
	defer func() {
		d.collectAndSaveResults(&result, output.localPath, outputPath, output.logPath)
	}()

	// 6. 执行`fastbot`
	err = d.executeFastbotBySession(ctx, packageName, o, outputPath, output.interceptor)
	return result, err
}

// applyFastbotOptions 应用`fastbot`选项
func (d *AndroidDevice) applyFastbotOptions(opts ...FastbotOption) *fastbotOptions {
	o := &fastbotOptions{
		duration: defaultDuration,
		throttle: defaultThrottle,
	}
	for _, opt := range opts {
		opt(o)
	}

	return o
}

// pushRequiredFiles 推送必要文件到设备
func (d *AndroidDevice) pushRequiredFiles(ctx context.Context, o *fastbotOptions) {
	if o.appPath != "" {
		// 推送`max.valid.strings`文件
		if err := d.pushMaxValidStringsFile(ctx, o.appPath); err != nil {
			d.Warnf("failed to push the max valid strings file, serial: %s, error: %+v", d.serial, err)
		}
	}
	if len(o.activities) > 0 {
		// 推送`awl.strings`文件
		if err := d.pushActivityWhiteListFile(ctx, o.activities); err != nil {
			d.Warnf("failed to push the activity white list file, serial: %s, error: %+v", d.serial, err)
		}
	}
}

// setupOutputHandling 设置输出处理
func (d *AndroidDevice) setupOutputHandling(config *fastbotOptions, result *Result) *outputHandler {
	handler := &outputHandler{}
	writer := io.Discard

	if config.reportPath != "" {
		// 创建本地输出目录
		handler.localPath = config.reportPath
		if !strings.HasSuffix(handler.localPath, d.serial) {
			handler.localPath = filepath.Join(config.reportPath, d.serial)
		}
		_ = os.MkdirAll(handler.localPath, 0o755)

		// 创建`fastbot`日志文件
		handler.logPath = filepath.Join(handler.localPath, filenameOfFastbotLog)
		logFile, err := os.OpenFile(handler.logPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0o644)
		if err != nil {
			d.Errorf("failed to create log file, serial: %s, file: %s, error: %+v", d.serial, handler.logPath, err)
		} else {
			handler.logFile = logFile
			writer = logFile
		}
	}

	// 创建流拦截器
	handler.interceptor = NewStreamInterceptor(
		writer, func(line string) {
			if anrCount, crashCount, err := d.parseFastbotLogLineContent(line); err == nil {
				result.ANRCount = anrCount
				result.CrashCount = crashCount
			}
		},
	)

	return handler
}

// collectAndSaveResults 收集和保存`fastbot`执行结果
func (d *AndroidDevice) collectAndSaveResults(result *Result, localPath, remotePath, logPath string) {
	var err error
	if result == nil {
		result = &Result{}
	}

	// 收集`fastbot`结果
	if result.ActivityStatistics, err = d.collectFastbotResult(remotePath); err != nil {
		d.Errorf("failed to collect the fastbot result, serial: %s, error: %s", d.serial, err)
	}

	// 保存`fastbot`相关文件到本地
	if result.Artifacts, err = d.saveFastbotFiles(localPath, remotePath); err != nil {
		// 保存失败，则保留设备上的`fastbot`相关文件
		d.Errorf("failed to save the fastbot files, serial: %s, error: %s", d.serial, err)
	} else {
		// 保存成功，则删除设备上的`fastbot`相关文件
		d.removeFastbotFiles(remotePath)
	}

	// 添加日志文件到`artifacts`
	if logPath != "" {
		result.Artifacts = append(
			result.Artifacts, Artifact{
				Type:        commonpb.ArtifactType_ArtifactType_LOG,
				FileName:    filenameOfFastbotLog,
				FilePath:    logPath,
				Description: artifactLogMapping[filenameOfFastbotLog],
			},
		)
	}
}

// executeFastbotBySession 通过会话的方式执行`fastbot`
func (d *AndroidDevice) executeFastbotBySession(
	ctx context.Context, packageName string, o *fastbotOptions, outputPath string, interceptor *StreamInterceptor,
) error {
	// 创建会话
	session, err := d.device.NewSession()
	if err != nil {
		return errors.Wrapf(err, "failed to create the session, serial: %s", d.serial)
	}

	var (
		closeOnce sync.Once
		closeFn   = func() {
			if session != nil {
				_ = session.Close()
			}
		}
	)
	defer func() {
		closeOnce.Do(closeFn)
	}()

	// 设置会话输出
	if interceptor != nil {
		session.Stdout = interceptor
		session.Stderr = NewStderrWriter(interceptor)
	}

	// 执行前，移除`fastbot`相关文件
	d.removeFastbotFiles(outputPath)

	// 构建并启动命令
	cmd := d.buildFastbotCommand(packageName, o, outputPath)
	d.Infof("start to run the monkey test, serial: %s, command: %q", d.serial, cmd)
	if err = session.Start(cmd); err != nil {
		return errors.Wrapf(err, "failed to start the monkey test, serial: %s, command: %q", d.serial, cmd)
	}

	// 等待`fastbot`运行结束
	resultCh := make(chan error, 1)
	threading.GoSafeCtx(
		ctx, func() {
			defer close(resultCh)
			resultCh <- session.Wait()
		},
	)
	defer func() {
		closeOnce.Do(closeFn)

		// 等待协程完全退出，避免泄漏，但设置超时保护
		timer := timewheel.NewTimer(5 * time.Second)
		defer timer.Stop()

		select {
		case <-resultCh: // 协程正常退出
		case <-timer.C: // 超时保护：如果5秒内协程还没退出，记录警告但不再等待
			d.Warnf("fastbot session wait goroutine did not exit within timeout, serial: %s", d.serial)
		}
	}()

	// 尝试停止`fastbot`进程，让其优雅退出
	defer d.tryToKillFastbotProcess()

	// 等待执行结果
	timer := timewheel.NewTimer(time.Duration(o.duration)*time.Minute + 10*time.Second)
	defer timer.Stop()

	select {
	case <-d.ctx.Done():
		d.Infof(
			"got a done signal while running the monkey test, serial: %s, package_name: %s, duration: %d",
			d.serial, packageName, o.duration,
		)
	case <-ctx.Done():
		d.Infof(
			"got a done signal while running the monkey test, serial: %s, package_name: %s, duration: %d",
			d.serial, packageName, o.duration,
		)
	case <-timer.C:
		d.Warnf(
			"wait fot the result of monkey test timeout, serial: %s, package_name: %s, duration: %d",
			d.serial, packageName, o.duration,
		)
	case err := <-resultCh:
		// 正常完成，让defer处理session的关闭
		return err
	}

	return nil
}

// buildFastbotCommand 构建`fastbot`执行命令
func (d *AndroidDevice) buildFastbotCommand(packageName string, o *fastbotOptions, outputPath string) string {
	cmd := fmt.Sprintf(commandOfMonkeyTest, packageName, o.duration, o.throttle, outputPath)
	if len(o.activities) > 0 {
		cmd += fmt.Sprintf(
			" --act-whitelist-file %s", filepath.Join(sdcardPathOnAndroid, filenameOfActivityWhiteListStrings),
		)
	}

	return cmd
}

// tryToKillFastbotProcess 尝试停止`fastbot`进程
func (d *AndroidDevice) tryToKillFastbotProcess() {
	// 注：`session`关闭的时候，`adb shell service`会发`SIGHUP`信号给`fastbot`进程，并关闭进程相关的文件句柄；
	// `fastbot`进程捕获到信号后会优雅退出，输出执行结果；
	// 但由于我们采用拦截器对标准输出流进行日志分析，如果`session`关闭，则会导致部分日志无法截取（即提取不到`anr`和`crash`数量）；
	// 因此这里主动发`SIGTERM`信号给`fastbot`进程，让`fastbot`进程优雅退出，拦截器可以完整地处理所有日志数据；
	if pid, err := d.getFastbotPid(); err == nil && pid != 0 {
		_, _ = d.device.RunShellCommand("kill", "-TERM", strconv.Itoa(pid))
		time.Sleep(3 * time.Second)
	}
}

func (d *AndroidDevice) getFastbotPid() (int, error) {
	return collector.NewAndroidRunningProcess(d.device, processNameOfFastbot).GetPid()
}

func (d *AndroidDevice) removeFastbotFiles(remotePath string) {
	// rm -rf /sdcard/fastbot/{short_task_id}
	if _, err := d.device.RunShellCommand("rm", "-rf", remotePath); err != nil {
		d.Errorf("failed to remove the fastbot files, serial: %s, path: %s, error: %+v", d.serial, remotePath, err)
	}

	// rm -f /sdcard/max.valid.strings
	if _, err := d.device.RunShellCommand(
		"rm", "-f", filepath.Join(sdcardPathOnAndroid, filenameOfMaxValidStrings),
	); err != nil {
		d.Errorf("failed to remove the max valid strings file, serial: %s, error: %+v", d.serial, err)
	}

	// rm -f /sdcard/awl.strings
	if _, err := d.device.RunShellCommand(
		"rm", "-f", filepath.Join(sdcardPathOnAndroid, filenameOfActivityWhiteListStrings),
	); err != nil {
		d.Errorf("failed to remove the activity white list file, serial: %s, error: %+v", d.serial, err)
	}

	if _, err := d.device.RunShellCommand(
		"rm", "-f", filepath.Join(sdcardPathOnAndroid, filenameOfCrashDumpLog),
	); err != nil {
		d.Errorf("failed to remove the crash dump log file, serial: %s, error: %+v", d.serial, err)
	}
}

func (d *AndroidDevice) pushMaxValidStringsFile(ctx context.Context, appPath string) error {
	ctx, cancel := context.WithTimeout(ctx, timeoutOfPushMaxValidStrings)
	defer cancel()

	var (
		remotePath = filepath.Join(sdcardPathOnAndroid, filenameOfMaxValidStrings)
		doneCh     = make(chan lang.PlaceholderType)

		err error
	)

	threading.GoSafeCtx(
		ctx, func() {
			defer close(doneCh)

			var output []byte
			cmd := qetutils.CommandContext(ctx, "aapt2", "dump", "strings", appPath)
			cmd.WaitDelay = time.Second
			output, err = cmd.Output()
			if err != nil {
				err = errors.Wrapf(err, "failed to execute the command, serial: %s, command: %q", d.serial, cmd)
				return
			}

			err = d.device.PushByReader(bytes.NewReader(output), remotePath)
			if err != nil {
				err = errors.Wrapf(
					err, "failed to push %q to the device, serial: %s, path: %s",
					filenameOfMaxValidStrings, d.serial, remotePath,
				)
			}
		},
	)

	select {
	case <-ctx.Done():
		return errors.Errorf(
			"pushing %q to the device timed out, serial: %s, path: %s",
			filenameOfMaxValidStrings, d.serial, remotePath,
		)
	case <-doneCh:
		return err
	}
}

func (d *AndroidDevice) pushActivityWhiteListFile(ctx context.Context, activities []string) error {
	ctx, cancel := context.WithTimeout(ctx, timeoutOfPushActivityWhiteListStrings)
	defer cancel()

	var (
		remotePath = filepath.Join(sdcardPathOnAndroid, filenameOfActivityWhiteListStrings)
		doneCh     = make(chan lang.PlaceholderType)

		err error
	)

	threading.GoSafeCtx(
		ctx, func() {
			defer close(doneCh)

			buf := new(bytes.Buffer)
			for _, activity := range activities {
				_, _ = buf.WriteString(activity + "\n")
			}

			err = d.device.PushByReader(buf, remotePath)
			if err != nil {
				err = errors.Wrapf(
					err, "failed to push %q to the device, serial: %s, path: %s",
					filenameOfActivityWhiteListStrings, d.serial, remotePath,
				)
			}
		},
	)

	select {
	case <-ctx.Done():
		return errors.Errorf(
			"pushing %q to the device timed out, serial: %s, path: %s",
			filenameOfActivityWhiteListStrings, d.serial, remotePath,
		)
	case <-doneCh:
		return err
	}
}

func (d *AndroidDevice) parseFastbotLogLineContent(line string) (anrCount, crashCount int, err error) {
	pattern := fastbotANRCountAndCrashCountRE.String()
	match, err := fastbotANRCountAndCrashCountRE.FindStringMatch(line)
	if err != nil {
		return 0, 0, errors.Wrapf(
			err, "failed to find string match, serial: %s, pattern: %s, line: %s", d.serial, pattern, line,
		)
	} else if match == nil {
		return 0, 0, errors.Errorf(
			"no content matching the Regexp was found, serial: %s, pattern: %s, line: %s", d.serial, pattern, line,
		)
	}

	if group := match.GroupByName(groupNameOfANRCount); group != nil {
		anrCount, _ = strconv.Atoi(group.String())
	}
	if group := match.GroupByName(groupNameOfCrashCount); group != nil {
		crashCount, _ = strconv.Atoi(group.String())
	}

	return anrCount, crashCount, nil
}

func (d *AndroidDevice) collectFastbotResult(remotePath string) (stats ActivityStatistics, err error) {
	if remotePath == "" {
		return stats, nil
	}

	path := filepath.Join(remotePath, filenameOfMaxActivityStatisticsLog)
	if !d.device.Exists(path) {
		return stats, errors.Errorf("the file doesn't exist, serial: %s, path: %s", d.serial, path)
	}

	buf := new(bytes.Buffer)
	if err = d.device.ReadFile(path, buf); err != nil {
		return stats, errors.Wrapf(err, "failed to read the file, serial: %s, path: %s", d.serial, path)
	}

	if err = json.NewDecoder(buf).Decode(&stats); err != nil {
		return stats, errors.Wrapf(err, "failed to decode the file, serial: %s, path: %s", d.serial, path)
	}

	return stats, nil
}

func (d *AndroidDevice) saveFastbotFiles(localPath, remotePath string) ([]Artifact, error) {
	if localPath == "" || remotePath == "" {
		return nil, nil
	}

	remoteCrashLogPath := filepath.Join(sdcardPathOnAndroid, filenameOfCrashDumpLog)
	if d.device.Exists(remoteCrashLogPath) {
		if _, err := d.device.RunShellCommand("mv", remoteCrashLogPath, remotePath); err != nil {
			d.Errorf(
				"failed to move the crash log file, serial: %s, path: %s, error: %+v",
				d.serial, remoteCrashLogPath, err,
			)
		}
	}

	if err := d.device.Pull(remotePath, localPath); err != nil {
		return nil, err
	}

	// 注：暂时只针对指定的两个文件做记录，不遍历`localPath`目录来获取全部文件信息
	artifacts := make([]Artifact, 0, constants.ConstDefaultMakeSliceSize)
	localCrashLogPath := filepath.Join(localPath, filenameOfCrashDumpLog)
	if qetutils.Exists(localCrashLogPath) {
		artifacts = append(
			artifacts, Artifact{
				Type:        commonpb.ArtifactType_ArtifactType_LOG,
				FileName:    filenameOfCrashDumpLog,
				FilePath:    localCrashLogPath,
				Description: artifactLogMapping[filenameOfCrashDumpLog],
			},
		)
	}
	localOOMTracesLogPath := filepath.Join(localPath, filenameOfOOMTracesLog)
	if qetutils.Exists(localOOMTracesLogPath) {
		artifacts = append(
			artifacts, Artifact{
				Type:        commonpb.ArtifactType_ArtifactType_LOG,
				FileName:    filenameOfOOMTracesLog,
				FilePath:    localOOMTracesLogPath,
				Description: artifactLogMapping[filenameOfOOMTracesLog],
			},
		)
	}

	return artifacts, nil
}

func (d *AndroidDevice) removeScreenshotFiles(remotePath string) {
	// rm -rf /sdcard/screenshot/{short_task_id}
	if _, err := d.device.RunShellCommand("rm", "-rf", remotePath); err != nil {
		d.Errorf("failed to remove the screenshot files, serial: %s, path: %s, error: %+v", d.serial, remotePath, err)
	}
}

func (d *AndroidDevice) saveScreenshotFiles(localPath, remotePath string) error {
	if localPath == "" || remotePath == "" {
		return nil
	}

	return d.device.Pull(remotePath, localPath)
}

func (d *AndroidDevice) Screenshot(remotePath string) error {
	return d.screenshotByADB(remotePath)
}

func (d *AndroidDevice) screenshotByADB(remotePath string) error {
	if ext := filepath.Ext(remotePath); ext == "" {
		remotePath += constants.ConstSuffixOfPng
	}

	if _, err := d.device.RunShellCommand("rm", "-f", remotePath); err != nil {
		d.Errorf("failed to remove the remote file, serial: %s, path: %s, error: %+v", d.serial, remotePath, err)
	}

	parentPath := filepath.Dir(remotePath)
	if _, err := d.device.RunShellCommand("mkdir", "-p", parentPath); err != nil {
		d.Errorf("failed to create directory, serial: %s, path: %s, error: %+v", d.serial, parentPath, err)
	}

	if _, err := d.device.RunShellCommand(fmt.Sprintf(commandOfScreenshotByADB, remotePath)); err != nil {
		return errors.Wrapf(err, "failed to take screenshot, serial: %s, path: %s", d.serial, remotePath)
	}

	return nil
}
