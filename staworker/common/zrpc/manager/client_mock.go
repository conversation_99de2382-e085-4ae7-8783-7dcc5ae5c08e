package manager

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var _ IClient = (*mockClient)(nil)

type mockClient struct{}

func NewMockClient() IClient {
	return &mockClient{}
}

func (c *mockClient) AcquireProjectDevice(
	ctx context.Context, in *managerpb.AcquireProjectDeviceReq, opts ...grpc.CallOption,
) (*managerpb.AcquireProjectDeviceResp, error) {
	logx.WithContext(ctx).Infof("mockClient AcquireProjectDevice: %s", protobuf.MarshalJSONIgnoreError(in))
	now := time.Now().UnixMilli()
	return &managerpb.AcquireProjectDeviceResp{
		Devices: []*managerpb.ProjectDevice{
			{
				Device: &devicehubpb.Device{
					Udid:          "", // LZYTYLZT9HFI6DLN
					Name:          "PJG110",
					Type:          commonpb.DeviceType_REAL_PHONE,
					Platform:      commonpb.PlatformType_ANDROID,
					Brand:         "OPPO",
					Model:         "PJG110",
					Version:       "13",
					Serial:        "", // LZYTYLZT9HFI6DLN
					Provider:      "http://************:3500",
					ProviderType:  devicehubpb.ProviderType_ATX,
					RemoteAddress: "", // ************:20004
					State:         devicehubpb.DeviceState_IN_USE,
					Token:         "", // LZYTYLZT9HFI6DLN
					StartedAt:     now,
					Duration:      0,
					CreatedBy:     "probe-system",
					UpdatedBy:     "probe-system",
					CreatedAt:     1716275752000,
					UpdatedAt:     now,
				},
				ProjectId: in.GetProjectId(),
				Usage:     commonpb.DeviceUsage_STABILITY_TESTING,
			},
		},
	}, nil
}

func (c *mockClient) ReleaseProjectDevice(
	ctx context.Context, in *managerpb.ReleaseProjectDeviceReq, opts ...grpc.CallOption,
) (*managerpb.ReleaseProjectDeviceResp, error) {
	logx.WithContext(ctx).Infof("mockClient ReleaseProjectDevice: %s", protobuf.MarshalJSONIgnoreError(in))
	return &managerpb.ReleaseProjectDeviceResp{}, nil
}

func (c *mockClient) GetProjectDevice(
	ctx context.Context, in *managerpb.GetProjectDeviceReq, opts ...grpc.CallOption,
) (*managerpb.GetProjectDeviceResp, error) {
	logx.WithContext(ctx).Infof("mockClient GetProjectDevice: %s", protobuf.MarshalJSONIgnoreError(in))
	now := time.Now().UnixMilli()
	return &managerpb.GetProjectDeviceResp{
		Device: &managerpb.ProjectDevice{
			Device: &devicehubpb.Device{
				Udid:          "", // LZYTYLZT9HFI6DLN
				Name:          "PJG110",
				Type:          commonpb.DeviceType_REAL_PHONE,
				Platform:      commonpb.PlatformType_ANDROID,
				Brand:         "OPPO",
				Model:         "PJG110",
				Version:       "13",
				Serial:        "", // LZYTYLZT9HFI6DLN
				Provider:      "http://************:3500",
				ProviderType:  devicehubpb.ProviderType_ATX,
				RemoteAddress: "", // ************:20004
				State:         devicehubpb.DeviceState_IN_USE,
				Token:         "", // LZYTYLZT9HFI6DLN
				StartedAt:     now,
				Duration:      0,
				CreatedBy:     "probe-system",
				UpdatedBy:     "probe-system",
				CreatedAt:     1716275752000,
				UpdatedAt:     now,
			},
			ProjectId: in.GetProjectId(),
			Usage:     in.GetUsage(),
		},
	}, nil
}
