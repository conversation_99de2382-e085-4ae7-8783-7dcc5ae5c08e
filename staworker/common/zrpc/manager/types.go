package manager

import (
	"context"

	"google.golang.org/grpc"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type IClient interface {
	AcquireProjectDevice(
		ctx context.Context, in *managerpb.AcquireProjectDeviceReq, opts ...grpc.CallOption,
	) (*managerpb.AcquireProjectDeviceResp, error)
	ReleaseProjectDevice(
		ctx context.Context, in *managerpb.ReleaseProjectDeviceReq, opts ...grpc.CallOption,
	) (*managerpb.ReleaseProjectDeviceResp, error)
	GetProjectDevice(
		ctx context.Context, in *managerpb.GetProjectDeviceReq, opts ...grpc.CallOption,
	) (*managerpb.GetProjectDeviceResp, error)
}
