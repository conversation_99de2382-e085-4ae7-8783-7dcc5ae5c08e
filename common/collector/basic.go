package collector

import (
	"context"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
)

func WithDataTypes(dataTypes ...DataType) Option {
	return func(c *basicCollector) {
		if len(dataTypes) == 0 {
			return
		}

		s := set.NewHashset[DataType](uint64(len(dataTypes)), generic.Equals[DataType], Hashes)
		for _, dataType := range dataTypes {
			if defaultDataTypes.Has(dataType) {
				s.Put(dataType)
			}
		}

		if s.<PERSON>ze() > 0 {
			c.dataTypes = s
		}
	}
}

func WithIntervalOfCPU(interval time.Duration) Option {
	return func(c *basicCollector) {
		if interval < minIntervalOfCPU {
			c.intervalOfCPU = minIntervalOfCPU
		} else {
			c.intervalOfCPU = interval
		}
	}
}

func WithIntervalOfMemory(interval time.Duration) Option {
	return func(c *basicCollector) {
		if interval < minIntervalOfMemory {
			c.intervalOfMemory = minIntervalOfMemory
		} else {
			c.intervalOfMemory = interval
		}
	}
}

func WithIntervalOfFPS(interval time.Duration) Option {
	return func(c *basicCollector) {
		if interval < minIntervalOfFPS {
			c.intervalOfFPS = minIntervalOfFPS
		} else {
			c.intervalOfFPS = interval
		}
	}
}

func WithWorkers(workers int) Option {
	return func(c *basicCollector) {
		if workers < minWorkers {
			c.workers = minWorkers
		} else {
			c.workers = workers
		}
	}
}

func WithCallback(fn CallBackFunc) Option {
	return func(c *basicCollector) {
		if fn != nil {
			c.callback = fn
		}
	}
}

type (
	Option func(builder *basicCollector)

	basicCollector struct {
		logx.Logger
		ctx context.Context

		dataTypes        set.Set[DataType]
		intervalOfCPU    time.Duration
		intervalOfMemory time.Duration
		intervalOfFPS    time.Duration
		workers          int
		callback         CallBackFunc

		startOnce, stopOnce sync.Once
		stopCh              chan lang.PlaceholderType
		dataCh              chan *MetricData
	}
)

func (c *basicCollector) Interval(dataType DataType) time.Duration {
	switch dataType {
	case CPU:
		return c.intervalOfCPU
	case MEMORY:
		return c.intervalOfMemory
	case FPS:
		return c.intervalOfFPS
	default:
		return 0
	}
}

func (c *basicCollector) Unit(dataType DataType) Unit {
	switch dataType {
	case CPU:
		return UnitOfPercentage
	case MEMORY:
		return UnitOfMegaByte
	case FPS:
		return UnitOfEmpty
	default:
		return UnitOfEmpty
	}
}

func (c *basicCollector) log(dataType DataType, interval time.Duration, points []PointData) {
	if len(points) == 0 {
		return
	}

	c.Infof("data_type: %s, interval: %s, points: %s", dataType, interval, jsonx.MarshalIgnoreError(points))
}

func (c *basicCollector) fixupInterval(interval time.Duration) {
	now := time.Now()
	timer := timewheel.NewTimer(now.Add(interval).Truncate(interval).Sub(now))
	defer timer.Stop()

	select {
	case <-c.ctx.Done():
	case <-c.stopCh:
	case <-timer.C:
	}
}
