package collector

import (
	"context"
	"fmt"
	"math"
	"slices"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/electricbubble/gadb"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
)

const (
	commandOfGetPid              = `ps -e -o USER:12,PID,PPID,ARGS | grep %s | grep -v grep`
	commandOfNProc               = `nproc`
	commandOfCountProcessors     = `cat /proc/cpuinfo | grep ^processor | wc -l`
	commandOfGetStat             = `cat /proc/stat`
	commandOfGetPidStat          = `cat /proc/%d/stat`
	commandOfGetCPUInfo          = `dumpsys cpuinfo | grep "%d" | grep -v grep`
	commandOfTopN1               = `top -n 1 -p %d`
	commandOfGetMemInfo          = `dumpsys meminfo %d`
	commandOfClearSurfaceFlinger = `dumpsys SurfaceFlinger --latency-clear`
	commandOfListSurfaceFlinger  = `dumpsys SurfaceFlinger --list | grep "^%s"`
	commandOfGetSurfaceFlinger   = `dumpsys SurfaceFlinger --latency "%s"`
	commandOfResetGfxInfo        = `dumpsys gfxinfo %s reset`
	commandOfGetGfxInfo          = `dumpsys gfxinfo %d framestats`
	commandOfGetSerial           = `getprop ro.serialno`

	noProcessFoundOfGetGfxInfo = "No process found"
	drawOfGetGfxInfo           = "Draw"
	processOfGetGfxInfo        = "Process"
	executeOfGetGfxInfo        = "Execute"
	flagsOfGetGfxInfo          = "Flags"
	intendedVsyncOfGetGfxInfo  = "IntendedVsync"
	vsyncOfGetGfxInfo          = "Vsync"
	frameCompletedOfGetGfxInfo = "FrameCompleted"

	indexOfPid  = 1
	indexOfArgs = 3

	pidUpdateDuration = 5 * time.Second
)

var (
	_ Collector = (*AndroidCollector)(nil)

	errOfTryAnotherGetCPUMethod = errors.New("try to get cpu by another method")
)

type AndroidRunningProcess struct {
	device      *gadb.Device
	packageName string

	lastPid        int
	nextUpdateTime time.Time
	mutex          sync.Mutex
}

func NewAndroidRunningProcess(device *gadb.Device, packageName string) *AndroidRunningProcess {
	return &AndroidRunningProcess{
		device:      device,
		packageName: packageName,
	}
}

func (p *AndroidRunningProcess) PackageName() string {
	return p.packageName
}

func (p *AndroidRunningProcess) GetPid() (int, error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	now := time.Now()
	if p.lastPid != 0 && now.Before(p.nextUpdateTime) {
		return p.lastPid, nil
	}

	serial := p.device.Serial()
	output, err := p.device.RunShellCommand(fmt.Sprintf(commandOfGetPid, p.packageName))
	if err != nil {
		return 0, errors.Wrapf(err, "failed to get pid, serial: %s, package_name: %s", serial, p.packageName)
	}

	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) < indexOfArgs+1 {
			continue
		}

		if fields[indexOfArgs] == p.packageName {
			pid, err := strconv.Atoi(fields[indexOfPid])
			if err != nil {
				return 0, err
			}

			p.lastPid = pid
			p.nextUpdateTime = now.Add(pidUpdateDuration)
			return p.lastPid, nil
		}
	}

	return 0, errors.Errorf("not found pid, serial: %s, package_name: %s", serial, p.packageName)
}

type AndroidCollector struct {
	*basicCollector

	device      *gadb.Device
	serial      string
	packageName string
	processors  int

	process         *AndroidRunningProcess
	lastTimestamp   *atomic.Int64
	lastCPUStat     *cpuStat
	lastCPUProcStat *cpuProcStat

	onceOfDeviceID sync.Once
}

func NewAndroidCollector(
	ctx context.Context, device *gadb.Device, packageName string, options ...Option,
) *AndroidCollector {
	bc := &basicCollector{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		dataTypes:        defaultDataTypes.Clone(),
		intervalOfCPU:    defaultIntervalOfCPU,
		intervalOfMemory: defaultIntervalOfMemory,
		intervalOfFPS:    defaultIntervalOfFPS,
		workers:          defaultWorkers,

		stopCh: make(chan lang.PlaceholderType),
		dataCh: make(chan *MetricData, defaultDataTypes.Size()),
	}
	bc.callback = bc.log
	for _, option := range options {
		option(bc)
	}

	c := &AndroidCollector{
		basicCollector: bc,

		device:      device,
		serial:      device.Serial(),
		packageName: packageName,

		process:       NewAndroidRunningProcess(device, packageName),
		lastTimestamp: new(atomic.Int64),
	}
	c.processors = c.getNumberOfProcessors()
	return c
}

func (c *AndroidCollector) Start() error {
	var err error

	c.startOnce.Do(
		func() {
			if c.dataTypes.Has(FPS) {
				_ = c.clearSurfaceFlinger()
				_ = c.resetGfxInfo()
			}

			proc.AddShutdownListener(
				func() {
					_ = c.Stop()
				},
			)

			threading.GoSafeCtx(c.ctx, c.collect)
			threading.GoSafeCtx(c.ctx, c.handleData)
		},
	)

	return err
}

func (c *AndroidCollector) Stop() error {
	c.stopOnce.Do(
		func() {
			close(c.stopCh)
			time.Sleep(time.Second)
		},
	)

	return nil
}

func (c *AndroidCollector) Finished() <-chan lang.PlaceholderType {
	return c.stopCh
}

func (c *AndroidCollector) DeviceID() string {
	c.onceOfDeviceID.Do(
		func() {
			// 远程连接的设备需要通过`adb shell getprop ro.serialno`获取设备编号
			if strings.Contains(c.serial, ":") {
				output, err := c.device.RunShellCommand(commandOfGetSerial)
				if err != nil {
					c.Errorf("failed to get the serial, error: %+v", err)
					return
				}

				c.serial = strings.TrimSpace(output)
			}
		},
	)

	return c.serial
}

func (c *AndroidCollector) collect() {
	var (
		serial      = c.device.Serial()
		packageName = c.packageName
	)

	defer func() {
		close(c.dataCh)
		_ = c.Stop()
	}()

	for _, dataType := range c.dataTypes.Keys() {
		switch dataType {
		case CPU:
			threading.GoSafeCtx(c.ctx, c.collectCPU)
		case MEMORY:
			threading.GoSafeCtx(c.ctx, c.collectMemory)
		case FPS:
			threading.GoSafeCtx(c.ctx, c.collectFPS)
		default:
			c.Warnf("unknown data type: %s", dataType)
		}
	}

	select {
	case <-c.ctx.Done():
		c.Infof("got a done signal while collecting metric data, serial: %s, package_name: %s", serial, packageName)
		break
	case <-c.stopCh:
		c.Infof("got a stop signal while collecting metric data, serial: %s, package_name: %s", serial, packageName)
		break
	}
}

func (c *AndroidCollector) collectCPU() {
	var (
		serial      = c.device.Serial()
		packageName = c.packageName
	)

	fn := func() {
		data, err := c.getCPU()
		if err != nil {
			c.Errorf("failed to get cpu data, serial: %s, package_name: %s, error: %s", serial, packageName, err)
			return
		}

		c.dataCh <- &MetricData{
			Type:     CPU,
			Interval: c.intervalOfCPU,
			Unit:     UnitOfPercentage,
			Data:     data,
		}
	}

	// c.fixupInterval(c.intervalOfCPU)
	threading.RunSafe(fn)

	ticker := timewheel.NewTicker(c.intervalOfCPU)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			c.Debugf(
				"got a done signal while collecting cpu, serial: %s, package_name: %s",
				serial, packageName,
			)
			return
		case <-c.stopCh:
			c.Debugf(
				"got a stop signal while collecting cpu, serial: %s, package_name: %s",
				serial, packageName,
			)
			return
		case <-ticker.C:
			threading.RunSafe(fn)
		}
	}
}

func (c *AndroidCollector) collectMemory() {
	var (
		serial      = c.device.Serial()
		packageName = c.packageName
	)

	fn := func() {
		data, err := c.getMemory()
		if err != nil {
			c.Errorf("failed to get memory data, serial: %s, package_name: %s, error: %s", serial, packageName, err)
			return
		}

		c.dataCh <- &MetricData{
			Type:     MEMORY,
			Interval: c.intervalOfMemory,
			Unit:     UnitOfKiloByte,
			Data:     data,
		}
	}

	// c.fixupInterval(c.intervalOfMemory)
	threading.RunSafe(fn)

	ticker := timewheel.NewTicker(c.intervalOfMemory)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			c.Debugf(
				"got a done signal while collecting memory, serial: %s, package_name: %s",
				serial, packageName,
			)
			return
		case <-c.stopCh:
			c.Debugf(
				"got a stop signal while collecting memory, serial: %s, package_name: %s",
				serial, packageName,
			)
			return
		case <-ticker.C:
			threading.RunSafe(fn)
		}
	}
}

func (c *AndroidCollector) collectFPS() {
	var (
		serial      = c.device.Serial()
		packageName = c.packageName
	)

	fn := func() {
		data, err := c.getFPS()
		if err != nil {
			c.Errorf("failed to get fps data, serial: %s, package_name: %s, error: %s", serial, packageName, err)
			return
		}

		c.dataCh <- &MetricData{
			Type:     FPS,
			Interval: c.intervalOfFPS,
			Unit:     UnitOfEmpty,
			Data:     data,
		}
	}

	// c.fixupInterval(c.intervalOfFPS)
	threading.RunSafe(fn)

	ticker := timewheel.NewTicker(c.intervalOfFPS)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			c.Debugf(
				"got a done signal while collecting fps, serial: %s, package_name: %s",
				serial, packageName,
			)
			return
		case <-c.stopCh:
			c.Debugf(
				"got a stop signal while collecting fps, serial: %s, package_name: %s",
				serial, packageName,
			)
			return
		case <-ticker.C:
			threading.RunSafe(fn)
		}
	}
}

func (c *AndroidCollector) handleData() {
	var (
		serial      = c.device.Serial()
		packageName = c.packageName

		wg sync.WaitGroup
	)

	defer func() {
		wg.Wait()
		drain(c.dataCh)
	}()

	pool := make(chan lang.PlaceholderType, c.workers)
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-c.stopCh:
			return
		case pool <- lang.Placeholder:
			data, ok := <-c.dataCh
			if !ok {
				<-pool
				return
			}

			wg.Add(1)
			threading.GoSafe(
				func() {
					defer func() {
						if r := recover(); r != nil {
							c.Errorf(
								"failed to handle data by callback function, serial: %s, package_name: %s, data: %s, error: %+v",
								serial, packageName, jsonx.MarshalIgnoreError(data), r,
							)
						}

						wg.Done()
						<-pool
					}()

					// NOTE: async callback execution prevents `wg.Wait()` block caused by long-running callbacks
					done := make(chan lang.PlaceholderType)
					threading.GoSafe(
						func() {
							defer close(done)

							c.callback(data.Type, data.Interval, data.Data.ToPoints())
						},
					)

					select {
					case <-c.ctx.Done():
						return
					case <-c.stopCh:
						return
					case <-done:
						return
					}
				},
			)
		}
	}
}

func (c *AndroidCollector) getNumberOfProcessors() int {
	number, err := c.getProcessorsByNProc()
	if err == nil {
		return number
	}

	number, err = c.getProcessorsByCPUInfo()
	if err == nil {
		return number
	}

	return 1
}

func (c *AndroidCollector) getProcessorsByNProc() (int, error) {
	output, err := c.device.RunShellCommand(commandOfNProc)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to get data of `nproc`, serial: %s", c.serial)
	}

	output = strings.TrimSpace(output)
	number, err := strconv.Atoi(output)
	if err != nil {
		return 0, errors.Wrapf(
			err, "failed to convert output of `nproc` to int, serial: %s, output: %s", c.serial, output,
		)
	}

	return number, nil
}

func (c *AndroidCollector) getProcessorsByCPUInfo() (int, error) {
	output, err := c.device.RunShellCommand(commandOfCountProcessors)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to get data of `/proc/cpuinfo`, serial: %s", c.serial)
	}

	output = strings.TrimSpace(output)
	number, err := strconv.Atoi(output)
	if err != nil {
		return 0, errors.Wrapf(
			err, "failed to convert output of `/proc/cpuinfo` to int, serial: %s, output: %s", c.serial, output,
		)
	}

	return number, nil
}

func (c *AndroidCollector) getCPU() (*CPUData, error) {
	pid, err := c.process.GetPid()
	if err != nil {
		return nil, err
	}

	// proc -> dumpsys -> top
	data, err := c.getCPUByProcStat(pid)
	if err == nil {
		return data, nil
	}

	data, err = c.getCPUByCPUInfo(pid)
	if err == nil {
		return data, nil
	}

	return c.getCPUByTop(pid)
}

func (c *AndroidCollector) getCPUByProcStat(pid int) (*CPUData, error) {
	stat, err := c.getProcStat()
	if err != nil {
		return nil, err
	}
	procStat, err := c.getProcPidStat(pid)
	if err != nil {
		return nil, err
	}
	defer func() {
		if stat != nil && procStat != nil {
			c.lastCPUStat = stat
			c.lastCPUProcStat = procStat
		}
	}()

	if c.lastCPUStat == nil || c.lastCPUProcStat == nil {
		return nil, errOfTryAnotherGetCPUMethod
	}

	usage := float64(procStat.cpuTime()-c.lastCPUProcStat.cpuTime()) / float64(stat.cpuTime()-c.lastCPUStat.cpuTime()) * 100 * float64(c.processors)
	if usage < 0 {
		return nil, errOfTryAnotherGetCPUMethod
	}

	return &CPUData{
		Timestamp: time.Now(),
		Usage:     usage,
	}, nil
}

type cpuStat struct {
	user    int64 // 从系统启动开始累计到当前时刻，用户态的CPU时间（单位：jiffies），不包含nice值为负进程
	nice    int64 // 从系统启动开始累计到当前时刻，nice值为负的进程所占用的CPU时间（单位：jiffies）
	system  int64 // 从系统启动开始累计到当前时刻，核心时间（单位：jiffies）
	idle    int64 // 从系统启动开始累计到当前时刻，除硬盘IO等待时间以外其它等待时间（单位：jiffies）
	iowait  int64 // 从系统启动开始累计到当前时刻，硬盘IO等待时间（单位：jiffies）
	irq     int64 // 从系统启动开始累计到当前时刻，硬中断时间（单位：jiffies）
	softirq int64 // 从系统启动开始累计到当前时刻，软中断时间（单位：jiffies）
}

func (s *cpuStat) cpuTime() int64 {
	return s.user + s.nice + s.system + s.idle + s.iowait + s.irq + s.softirq
}

func (s *cpuStat) output() string {
	return fmt.Sprintf(
		"total: %d, user: %d, nice: %d, system: %d, idle: %d, iowait: %d, irq: %d, softirq: %d",
		s.cpuTime(), s.user, s.nice, s.system, s.idle, s.iowait, s.irq, s.softirq,
	)
}

func (c *AndroidCollector) getProcStat() (*cpuStat, error) {
	output, err := c.device.RunShellCommand(commandOfGetStat)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get data of /proc/stat, serial: %s, package_name: %s", c.serial, c.packageName,
		)
	}

	lines := strings.Split(strings.TrimSpace(output), "\n")
	if len(lines) < 1 {
		return nil, errors.Errorf("empty of /proc/stat, serial: %s, package_name: %s", c.serial, c.packageName)
	}

	fields := strings.Fields(lines[0])
	if len(fields) < 8 {
		return nil, errors.Errorf(
			"invalid fields of /proc/stat, serial: %s, package_name: %s, line: %s", c.serial, c.packageName, lines[0],
		)
	}

	return &cpuStat{
		user:    cast.ToInt64(fields[1]),
		nice:    cast.ToInt64(fields[2]),
		system:  cast.ToInt64(fields[3]),
		idle:    cast.ToInt64(fields[4]),
		iowait:  cast.ToInt64(fields[5]),
		irq:     cast.ToInt64(fields[6]),
		softirq: cast.ToInt64(fields[7]),
	}, nil
}

type cpuProcStat struct {
	utime  int64 // 当前进程处于用户态运行的时间（单位：jiffies）
	stime  int64 // 当前进程处于内核态运行的时间（单位：jiffies）
	cutime int64 // 当前进程的子进程处于用户态运行的时间（单位：jiffies）
	cstime int64 // 当前进程的子进程处于内核态运行的时间（单位：jiffies）
}

func (s *cpuProcStat) cpuTime() int64 {
	return s.utime + s.stime + s.cutime + s.cstime
}

func (s *cpuProcStat) output() string {
	return fmt.Sprintf(
		"total: %d, utime: %d, stime: %d, cutime: %d, cstime: %d",
		s.cpuTime(), s.utime, s.stime, s.cutime, s.cstime,
	)
}

func (c *AndroidCollector) getProcPidStat(pid int) (*cpuProcStat, error) {
	output, err := c.device.RunShellCommand(fmt.Sprintf(commandOfGetPidStat, pid))
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to get data of /proc/{pid}/stat, serial: %s, package_name: %s, pid: %d",
			c.serial, c.packageName, pid,
		)
	}

	fields := strings.Fields(strings.TrimSpace(output))
	if len(fields) < 17 {
		return nil, errors.Errorf(
			"invalid fields of /proc/{pid}/stat, serial: %s, package_name: %s, pid: %d, line: %s",
			c.serial, c.packageName, pid, output,
		)
	}

	return &cpuProcStat{
		utime:  cast.ToInt64(fields[13]),
		stime:  cast.ToInt64(fields[14]),
		cutime: cast.ToInt64(fields[15]),
		cstime: cast.ToInt64(fields[16]),
	}, nil
}

func (c *AndroidCollector) getCPUByCPUInfo(pid int) (*CPUData, error) {
	// adb shell dumpsys cpuinfo | grep "${pid}" | grep -v grep
	//   117% 9166/com.yiyou.ga: 90% user + 26% kernel / faults: 654042 minor
	output, err := c.device.RunShellCommand(fmt.Sprintf(commandOfGetCPUInfo, pid))
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get cpu info, serial: %s, package_name: %s, pid: %d", c.serial, c.packageName, pid,
		)
	}

	timestamp := time.Now()
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) < 2 {
			continue
		}

		field1 := strings.TrimSuffix(fields[1], ":")
		tmp := strings.Split(field1, "/")
		if len(tmp) != 2 {
			continue
		} else if cast.ToInt(tmp[0]) != pid {
			continue
		} else if tmp[1] != c.packageName {
			continue
		}

		field0 := strings.TrimSuffix(fields[0], "%")
		usage, err := strconv.ParseFloat(field0, 64)
		if err != nil {
			continue
		}

		return &CPUData{
			Timestamp: timestamp,
			Usage:     usage,
		}, nil
	}

	return nil, errors.Errorf(
		"not found cpu usage from dumpsys info, serial: %s, package_name: %s, pid: %d, output: %s",
		c.serial, c.packageName, pid, output,
	)
}

func (c *AndroidCollector) getCPUByTop(pid int) (*CPUData, error) {
	// adb shell top -n 1 -p ${pid}
	// Tasks: 1 total,   0 running,   1 sleeping,   0 stopped,   0 zombie
	//  Mem:  7830844K total,  6946576K used,   884268K free,     8648K buffers
	// Swap:  6291452K total,   691196K used,  5600256K free,  3496164K cached
	// 800%cpu 155%user   0%nice  86%sys 538%idle   0%iow  14%irq   7%sirq   0%host
	//  PID USER         PR  NI VIRT  RES  SHR S[%CPU] %MEM     TIME+ ARGS
	// 9166 u0_a332      10 -10  45G 221M 221M S  110   2.8 439:01.93 com.yiyou.ga
	output, err := c.device.RunShellCommand(fmt.Sprintf(commandOfTopN1, pid))
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get top info, serial: %s, package_name: %s, pid: %d", c.serial, c.packageName, pid,
		)
	}

	timestamp := time.Now()
	sPid := strconv.Itoa(pid)
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		if !strings.HasPrefix(line, sPid) {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 9 {
			continue
		}

		field0 := strings.TrimSuffix(fields[8], "%")
		usage, err := strconv.ParseFloat(field0, 64)
		if err != nil {
			continue
		}

		return &CPUData{
			Timestamp: timestamp,
			Usage:     usage,
		}, nil
	}

	return nil, errors.Errorf(
		"not found cpu usage from top info, serial: %s, package_name: %s, pid: %d, output: %s",
		c.serial, c.packageName, pid, output,
	)
}

func (c *AndroidCollector) getMemory() (*MemoryDataOfAndroid, error) {
	pid, err := c.process.GetPid()
	if err != nil {
		return nil, err
	}

	output, err := c.device.RunShellCommand(fmt.Sprintf(commandOfGetMemInfo, pid))
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get memory info, serial: %s, package_name: %s, pid: %d", c.serial, c.packageName, pid,
		)
	}

	timestamp := time.Now()
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "TOTAL PSS:") {
			// TOTAL PSS:   726577            TOTAL RSS:   690380       TOTAL SWAP PSS:   216930
			fields := strings.Fields(line)
			if len(fields) >= 10 {
				return &MemoryDataOfAndroid{
					Timestamp: timestamp,
					PSS:       cast.ToInt64(fields[2]), // TOTAL PSS
					RSS:       cast.ToInt64(fields[5]), // TOTAL RSS
					Swap:      cast.ToInt64(fields[9]), // TOTAL SWAP PSS
				}, nil
			}
		}
		if strings.HasPrefix(line, "TOTAL:") && strings.Contains(line, "TOTAL SWAP PSS:") {
			// TOTAL:   890584       TOTAL SWAP PSS:   412430
			fields := strings.Fields(line)
			if len(fields) >= 6 {
				return &MemoryDataOfAndroid{
					Timestamp: timestamp,
					PSS:       cast.ToInt64(fields[1]), // TOTAL
					RSS:       0,
					Swap:      cast.ToInt64(fields[5]), // TOTAL SWAP PSS
				}, nil
			}
		}
		if strings.HasPrefix(line, "TOTAL") {
			// TOTAL   726577   352128   135428   216930   690380   289814   253157    36656
			fields := strings.Fields(line)
			switch len(fields) {
			case 8:
				//            Pss  Private  Private  SwapPss     Heap     Heap     Heap
				//          Total    Dirty    Clean    Dirty     Size    Alloc     Free
				//         ------   ------   ------   ------   ------   ------   ------
				// TOTAL   890584   427466    39816   412430   836556   486743   349812
				return &MemoryDataOfAndroid{
					Timestamp: timestamp,
					PSS:       cast.ToInt64(fields[1]), // Pss Total
					RSS:       0,
					Swap:      cast.ToInt64(fields[4]), // SwapPss Dirty
				}, nil
			case 9:
				//            Pss  Private  Private     Swap      Rss     Heap     Heap     Heap
				//          Total    Dirty    Clean    Dirty    Total     Size    Alloc     Free
				//         ------   ------   ------   ------   ------   ------   ------   ------
				// TOTAL   559625   374804    25764       96   878844    96662    59446    31944
				return &MemoryDataOfAndroid{
					Timestamp: timestamp,
					PSS:       cast.ToInt64(fields[1]), // Pss Total
					RSS:       cast.ToInt64(fields[5]), // Rss Total
					Swap:      cast.ToInt64(fields[4]), // SwapPss Dirty
				}, nil
			}
		}
	}

	return nil, errors.Errorf(
		"not found memory info from dumpsys info, serial: %s, package_name: %s, pid: %d, output: %s",
		c.serial, c.packageName, pid, output,
	)
}

func (c *AndroidCollector) getFPS() (*FPSData, error) {
	layers, err := c.getLayers()
	if err != nil {
		return nil, err
	}

	data := &FPSData{}
	for _, layer := range layers {
		v, err := c.getFPSBySurface(layer)
		if err != nil || v == nil || v.FPS == 0 {
			continue
		}

		if v.FPS > data.FPS {
			data = v
		}
	}
	if data.FPS != 0 {
		return data, nil
	}

	pid, err := c.process.GetPid()
	if err != nil {
		return nil, err
	}

	return c.getFPSByGfxInfo(pid)
}

func (c *AndroidCollector) getFPSBySurface(layerName string) (*FPSData, error) {
	/*
		Command `dumpsys SurfaceFlinger --latency <window name>`
		prints some information about the last 128 frames displayed in that window.

		The data returned looks like this:
		16666666
		86835771183435	86835803244946	86835782291404
		86835787777550	86835820190258	86835798828435
		86835803962758	86835836804529	86835815275206
		86835823815154	86835852965675	86835835038487
		86835837929737	86835869953852	86835849458435
		...

		The first line is the refresh period (here 16.95 ms), it is followed
		by 128 lines w/ 3 timestamps in nanosecond each:
		A) when the app started to draw
		B) the vsync immediately preceding SF submitting the frame to the h/w
		C) timestamp immediately after SF submitted that frame to the h/w

		The difference between the 1st and 3rd timestamp is the frame-latency.
		Interesting data is when the frame latency crosses a refresh period boundary,
		this can be calculated this way:
		ceil((C - A) / refresh-period)

		(each time the number above changes, we have a "jank").
		If this happens a lot during an animation, the animation appears
		janky, even if it runs at 60 fps in average.
	*/

	output, err := c.device.RunShellCommand(fmt.Sprintf(commandOfGetSurfaceFlinger, layerName))
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get surface flinger, serial: %s, package_name: %s, layer_name: %s",
			c.serial, c.packageName, layerName,
		)
	}

	var firstVsync, lastVsync, count int64

	timestamp := time.Now()
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		fields := strings.Fields(strings.TrimSpace(line))
		if len(fields) < 3 {
			continue
		}

		vsync := cast.ToInt64(fields[1])
		if vsync == 0 || vsync == math.MaxInt64 || vsync <= c.lastTimestamp.Load() {
			continue
		}

		if firstVsync == 0 {
			firstVsync = vsync
		} else {
			lastVsync = vsync
		}
		count += 1
	}

	if firstVsync > 0 && lastVsync > firstVsync && count > 0 {
		c.lastTimestamp.Store(lastVsync)

		return &FPSData{
			Timestamp: timestamp,
			FPS:       float64((count-1)*1e9) / float64(lastVsync-firstVsync),
		}, nil
	}

	return nil, errors.Errorf(
		"not found surface flinger info, serial: %s, package_name: %s, layer_name: %s, output: %s",
		c.serial, c.packageName, layerName, output,
	)
}

func (c *AndroidCollector) getLayers() ([]string, error) {
	output, err := c.device.RunShellCommand(fmt.Sprintf(commandOfListSurfaceFlinger, c.packageName))
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get layers, serial: %s, package_name: %s", c.serial, c.packageName)
	}

	lines := strings.Split(strings.TrimSpace(output), "\n")
	layers := make([]string, 0, len(lines))
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		layers = append(layers, line)
	}

	return layers, nil
}

func (c *AndroidCollector) getFPSByGfxInfo(pid int) (*FPSData, error) {
	output, err := c.device.RunShellCommand(fmt.Sprintf(commandOfGetGfxInfo, pid))
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get gfx info, serial: %s, package_name: %s, pid: %d", c.serial, c.packageName, pid,
		)
	} else if strings.Contains(output, noProcessFoundOfGetGfxInfo) {
		return nil, errors.Errorf(
			"not found process, serial: %s, package_name: %s, pid: %d, output: %s",
			c.serial, c.packageName, pid, output,
		)
	}

	var fps float64

	timestamp := time.Now()
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for i := 0; i < len(lines); {
		line := strings.TrimSpace(lines[i])

		switch {
		case isProfileDataInMSHeader(line):
			v, processed := c.processProfileDataInMS(lines[i:])
			fps = max(fps, v)
			i += processed
		case isProfileDataInNSHeader(line):
			v, processed := c.processProfileDataInNS(lines[i:])
			fps = max(fps, v)
			i += processed
		default:
			i += 1
		}
	}

	return &FPSData{
		Timestamp: timestamp,
		FPS:       fps,
	}, nil
}

func (c *AndroidCollector) processProfileDataInMS(lines []string) (float64, int) {
	var (
		total, count float64

		headers   = strings.Fields(lines[0])
		processed = 1
	)

	for _, line := range lines[1:] {
		line = strings.TrimSpace(line)
		processed += 1

		// 块结束标识
		if line == "" || strings.HasPrefix(line, "---") {
			break
		}

		if v, err := c.handleProfileDataInMS(headers, line); err == nil && v > 0 {
			total += v
			count += 1
		}
	}

	if count == 0 {
		return 0, processed
	}

	return total / count, processed
}

func (c *AndroidCollector) processProfileDataInNS(lines []string) (float64, int) {
	var (
		firstVsync, lastVsync, count int64

		headers   = strings.Split(lines[0], ",")
		processed = 1
	)

	for _, line := range lines[1:] {
		line = strings.TrimSpace(line)
		processed += 1

		// 块结束标识
		if line == "" || strings.HasPrefix(line, "---") {
			break
		}

		if v, err := c.handleProfileDataInNS(headers, line); err == nil && v > 0 {
			if firstVsync == 0 {
				firstVsync = v
			}
			lastVsync = v
			count += 1
		}
	}

	if lastVsync <= firstVsync || count < 2 {
		return 0, processed
	}

	return float64(count-1) * 1e9 / float64(lastVsync-firstVsync), processed
}

func (c *AndroidCollector) handleProfileDataInMS(headers []string, content string) (float64, error) {
	fields := strings.Fields(content)
	if len(fields) != len(headers) {
		return 0.0, errors.Errorf("invalid content of profile data in ms, content: %s", content)
	}

	var total float64
	for i, field := range fields {
		v, err := cast.ToFloat64E(field)
		if err != nil {
			return 0.0, errors.Wrapf(err, "failed to cast field to float64, field: %s, value: %s", headers[i], field)
		}

		total += v
	}

	return total, nil
}

func (c *AndroidCollector) handleProfileDataInNS(headers []string, content string) (int64, error) {
	fields := strings.Split(content, ",")
	if len(fields) < len(headers) {
		return 0, errors.Errorf("invalid content of profile data in ns, content: %s", content)
	} else if fields[0] != "0" {
		return 0, errors.Errorf("invalid content of profile data in ns, content: %s", content)
	}

	index := slices.Index(headers, vsyncOfGetGfxInfo)
	if index == -1 {
		return 0, errors.Errorf("not found %q in headers, headers: %v", vsyncOfGetGfxInfo, headers)
	}

	v, err := cast.ToInt64E(fields[index])
	if err != nil {
		return 0, errors.Wrapf(
			err, "failed to cast field to int64, field: %s, value: %s", vsyncOfGetGfxInfo, fields[index],
		)
	} else if v == 0 || v == math.MaxInt64 {
		return 0, nil
	}

	return v, nil
}

func (c *AndroidCollector) clearSurfaceFlinger() error {
	output, err := c.device.RunShellCommand(commandOfClearSurfaceFlinger)
	output = strings.TrimSpace(output)
	if output != "" {
		c.Debugf("the output of clear surface flinger: %s", output)
	}

	return err
}

func (c *AndroidCollector) resetGfxInfo() error {
	_, err := c.device.RunShellCommand(fmt.Sprintf(commandOfResetGfxInfo, c.packageName))
	return err
}

func isProfileDataInMSHeader(content string) bool {
	return strings.HasPrefix(content, drawOfGetGfxInfo) &&
		strings.Contains(content, processOfGetGfxInfo) &&
		strings.Contains(content, executeOfGetGfxInfo)
}

func isProfileDataInNSHeader(content string) bool {
	return strings.HasPrefix(content, flagsOfGetGfxInfo) &&
		strings.Contains(content, intendedVsyncOfGetGfxInfo) &&
		strings.Contains(content, vsyncOfGetGfxInfo) &&
		strings.Contains(content, frameCompletedOfGetGfxInfo)
}
