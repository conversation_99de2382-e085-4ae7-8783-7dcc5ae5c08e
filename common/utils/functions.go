package utils

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/csv"
	"io"
	"math/big"
	"reflect"
	"sort"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jszwec/csvutil"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/structpb"
	"gopkg.in/yaml.v3"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	dot   = "."
	slash = "/"
)

// AddTableNameToFields add table name to each field in slice
// Example: `name` => table.`name`
func AddTableNameToFields(table string, fields []string) []string {
	if table == "" {
		return fields
	}

	out := make([]string, len(fields))

	for i, field := range fields {
		if !strings.HasPrefix(field, table+dot) {
			field = table + dot + field
		}
		out[i] = field
	}

	return out
}

func FindRows(
	ctx context.Context, conn sqlx.SqlConn, selectBuilder squirrel.SelectBuilder, createRespFunc func() any,
) error {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return err
	}

	resp := createRespFunc()
	err = conn.QueryRowsCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func RandomInt(start, end int) int {
	gap := new(big.Int).SetInt64(int64(end - start))
	if i, err := rand.Int(rand.Reader, gap); err != nil {
		return start
	} else {
		return int(i.Int64() + int64(start))
	}
}

func RandomChoose(options []any) any {
	if len(options) == 0 {
		return nil
	}

	return options[RandomInt(0, len(options))]
}

func GetPerfCaseFromReader(reader io.Reader) (*pb.PerfCaseContent, error) {
	var content pb.PerfCaseContent
	decoder := yaml.NewDecoder(reader)
	err := decoder.Decode(&content)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to decode the perf case, error: %+v",
			err,
		)
	}

	if err = content.ValidateAll(); err != nil {
		return &content, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"the content of perf case does not pass parameter validation, content: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(&content), err,
		)
	}

	if len(content.GetSerialSteps()) == 0 && len(content.GetParallelSteps()) == 0 {
		return &content, errorx.Err(
			errorx.ValidateParamError,
			"the number of serial steps and parallel steps in the perf case cannot both be zero",
		)
	}

	if err = validateSerialSteps(content.GetSerialSteps()); err != nil {
		return &content, err
	}
	if err = validateParallelSteps(content.GetParallelSteps()); err != nil {
		return &content, err
	}

	return &content, nil
}

func validateSerialSteps(steps []*pb.PerfCaseStep) error {
	number := len(steps)

	if number > constants.ConstMaxPerfTestSerialSteps {
		return errorx.Errorf(
			errorx.ValidateParamError,
			"the number of serial steps exceeds the maximum value, number: %d, maximum: %d",
			number, constants.ConstMaxPerfTestSerialSteps,
		)
	}

	for _, step := range steps {
		name := step.GetName()
		sleep := step.GetSleep()
		if sleep == "" {
			continue
		}

		sleepDuration, err := time.ParseDuration(sleep)
		if err != nil {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the format of the sleep time for the serial step is incorrect, step: %s, sleep: %s, error: %+v",
				name, sleep, err,
			)
		}

		if sleepDuration > constants.ConstMaxPerfTestStepSleepTime {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the sleep time for the serial step exceeds the maximum value, step: %s, sleep: %s, maximum: %s",
				name, sleep, constants.ConstMaxPerfTestStepSleepTime,
			)
		} else if step.GetRateLimit().GetTargetRps() > constants.ConstDividePerfTestStepRPS &&
			sleepDuration > constants.ConstMaxPerfTestStepWithDivRPSSleepTime {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the sleep time for the serial step with high rps exceeds the maximum value, step: %s, rps: %d, sleep: %s, maximum: %s",
				name, constants.ConstDividePerfTestStepRPS, sleep, constants.ConstMaxPerfTestStepWithDivRPSSleepTime,
			)
		}
	}

	return nil
}

func validateParallelSteps(steps []*pb.PerfCaseStep) error {
	number := len(steps)

	if number > constants.ConstMaxPerfTestParallelSteps {
		return errorx.Errorf(
			errorx.ValidateParamError,
			"the number of parallel steps exceeds the maximum value, number: %d, maximum: %d",
			number, constants.ConstMaxPerfTestParallelSteps,
		)
	}

	return nil
}

func GetPerfDataFromReader(reader io.Reader) (*pb.PerfDataContent, error) {
	csvReader := csv.NewReader(reader)
	csvReader.Comma = constants.ConstDefaultCSVDelimiter
	csvReader.LazyQuotes = true
	csvReader.TrimLeadingSpace = true
	csvReader.ReuseRecord = true

	decoder, err := csvutil.NewDecoder(csvReader)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.NewObjectFailure, err.Error()),
			"failed to new a csv decoder, error: %+v",
			err,
		)
	}
	decoder.WithUnmarshalers(CSVUnmarshalers)

	var content pb.PerfDataContent
	cache_ := make(map[string]int)
	for {
		var line pb.PerfDataContent_Line
		err = decoder.Decode(&line)
		if err != nil {
			if err != io.EOF {
				return nil, errors.Wrapf(
					errorx.Err(errorx.SerializationError, err.Error()),
					"failed to decode the perf data, error: %+v",
					err,
				)
			}
			break
		}

		key := getKeyFromAuthData(line.GetAuthData())
		if key == "" {
			continue
		}

		lenOfValues := len(line.GetRequestData().GetValues())

		index, ok := cache_[key]
		if !ok {
			cache_[key] = len(content.Lines)
			content.Lines = append(content.Lines, &line)
		} else if lenOfValues > 0 {
			if content.Lines[index].GetRequestData() == nil {
				content.Lines[index].RequestData = &structpb.ListValue{Values: make([]*structpb.Value, 0, lenOfValues)}
			}

			content.Lines[index].RequestData.Values = append(
				content.Lines[index].RequestData.Values, line.GetRequestData().GetValues()...,
			)
		}
	}

	if err = content.ValidateAll(); err != nil {
		return &content, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"the content of perf data does not pass parameter validation, content: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(&content), err,
		)
	}

	return &content, nil
}

func getKeyFromAuthData(data *structpb.Struct) string {
	if data == nil {
		return ""
	}

	m := data.AsMap()
	keys := make([]string, 0, len(m))
	for key := range m {
		if key == "" {
			continue
		}

		keys = append(keys, key)
	}
	sort.Strings(keys)

	values := make([]string, 0, len(keys))
	for _, key := range keys {
		values = append(values, cast.ToString(m[key]))
	}

	return strings.Join(values, string(constants.ConstDefaultCSVDelimiter))
}

func GetMaxRPSFromSerialStepsAndParallelSteps(serial, parallel []*pb.PerfCaseStep) (*pb.RateLimit, error) {
	var rl, srl, prl *pb.RateLimit
	srl = GetMinRPSFromSteps(serial)   // min(serial steps)
	prl = GetMaxRPSFromSteps(parallel) // max(parallel steps)
	if srl == nil && prl == nil {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"cannot found any rate limit from serial steps nor parallel steps in the perf case",
		)
	} else if srl == nil || srl.GetTargetRps() < prl.GetTargetRps() {
		rl = prl
	} else {
		rl = srl
	}

	return rl, nil
}

func GetMinRPSFromSteps(steps []*pb.PerfCaseStep) *pb.RateLimit {
	var rl *pb.RateLimit
	for _, step := range steps {
		l := step.GetRateLimit()
		if l == nil {
			continue
		}

		if rl == nil || rl.GetTargetRps() > l.GetTargetRps() {
			rl = &pb.RateLimit{
				TargetRps:    l.GetTargetRps(),
				InitialRps:   l.GetInitialRps(),
				StepHeight:   l.GetStepHeight(),
				StepDuration: l.GetStepDuration(),
			}
		}
	}

	return rl
}

func GetMaxRPSFromSteps(steps []*pb.PerfCaseStep) *pb.RateLimit {
	var rl *pb.RateLimit
	for _, step := range steps {
		l := step.GetRateLimit()
		if l == nil {
			continue
		}

		if rl == nil || rl.GetTargetRps() < l.GetTargetRps() {
			rl = &pb.RateLimit{
				TargetRps:    l.GetTargetRps(),
				InitialRps:   l.GetInitialRps(),
				StepHeight:   l.GetStepHeight(),
				StepDuration: l.GetStepDuration(),
			}
		}
	}

	return rl
}

func GetMaxSleepFromSteps(steps []*pb.PerfCaseStep) time.Duration {
	var sleep time.Duration
	for _, step := range steps {
		d := cache.Parse(step.GetSleep())
		if d > sleep {
			sleep = d
		}
	}

	return sleep
}

func GetMaxRPSFromSerialStepsAndParallelStepsV2(serial, parallel []*pb.PerfCaseStepV2) int64 {
	sRPS := GetMinRPSFromStepsV2(serial)   // min(serial steps)
	pRPS := GetMaxRPSFromStepsV2(parallel) // max(parallel steps)
	return utils.Max(sRPS, pRPS)
}

func GetMinRPSFromStepsV2(steps []*pb.PerfCaseStepV2) int64 {
	var rps int64
	for _, step := range steps {
		tmp := GetMaxRPSFromRateLimits(step.GetRateLimits())
		if rps == 0 || rps > tmp {
			rps = tmp
		}
	}

	return rps
}

func GetMaxRPSFromStepsV2(steps []*pb.PerfCaseStepV2) int64 {
	var rps int64
	for _, step := range steps {
		tmp := GetMaxRPSFromRateLimits(step.GetRateLimits())
		if rps == 0 || rps < tmp {
			rps = tmp
		}
	}

	return rps
}

func GetMaxRPSFromRateLimits(rateLimits []*pb.RateLimitV2) int64 {
	var rps int64
	for _, rl := range rateLimits {
		if rl.GetTargetRps() > rps {
			rps = rl.GetTargetRps()
		}
	}

	return rps
}

func GetMaxSleepFromStepsV2(steps []*pb.PerfCaseStepV2) time.Duration {
	var sleep time.Duration
	for _, step := range steps {
		d := cache.Parse(step.GetSleep())
		if d > sleep {
			sleep = d
		}
	}

	return sleep
}

func GetGRPCFullMethodName(method string) string {
	if len(method) == 0 {
		return method
	}

	if !strings.Contains(method, slash) {
		index := strings.LastIndex(method, dot)
		if index == -1 || index >= len(method)-1 {
			return method
		}

		method = method[:index] + slash + method[index+1:]
	}

	if !strings.HasPrefix(method, slash) {
		method = slash + method
	}

	return method
}

type (
	SrcHandler func(any) (string, error)
	DstCreator func() any
)

var (
	defaultApiSrcHandler = jsonx.MarshalToString
	defaultRpcSrcHandler = func(src any) (string, error) {
		rt := reflect.TypeOf(src)
		if rt.Kind() == reflect.Pointer {
			rt = rt.Elem()
		}

		if rt.Kind() == reflect.Slice {
			return protobuf.MarshalJSONWithMessagesToString(src)
		} else if m, ok := src.(proto.Message); ok {
			return protobuf.MarshalJSONToString(m)
		} else {
			return "", errors.Errorf(
				"the type of value[%T] is neither a proto.Message nor a slice of proto.Message", src,
			)
		}
	}
)

func ApiStructToRpcMessage(
	srcType, dstType any, srcHandler SrcHandler, dstCreator DstCreator,
) utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: srcType,
		DstType: dstType,
		Fn: func(src any) (any, error) {
			if srcHandler == nil {
				srcHandler = defaultApiSrcHandler
			}
			s, err := srcHandler(src)
			if err != nil {
				return nil, err
			}

			return handleSrcToDst(s, dstType, dstCreator)
		},
	}
}

func RpcMessageToApiStruct(
	srcType, dstType any, srcHandler SrcHandler, dstCreator DstCreator,
) utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: srcType,
		DstType: dstType,
		Fn: func(src any) (dst any, err error) {
			if srcHandler == nil {
				srcHandler = defaultRpcSrcHandler
			}
			s, err := srcHandler(src)
			if err != nil {
				return nil, err
			}

			return handleSrcToDst(s, dstType, dstCreator)
		},
	}
}

func StringToApiStructOrRpcMessage(dstType any, dstCreator DstCreator) utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: utils.String,
		DstType: dstType,
		Fn: func(src any) (any, error) {
			return handleSrcToDst(src, dstType, dstCreator)
		},
	}
}

func NullStringToApiStructOrRpcMessage(dstType any, dstCreator DstCreator) utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: utils.SQLNullString,
		DstType: dstType,
		Fn: func(src any) (any, error) {
			s, ok := src.(sql.NullString)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not a SQL null string", src)
			}

			if !s.Valid || s.String == "" {
				return nil, nil
			}

			return handleSrcToDst(s.String, dstType, dstCreator)
		},
	}
}

func handleSrcToDst(src, typ any, creator DstCreator) (dst any, err error) {
	rt := reflect.TypeOf(typ)
	if creator == nil {
		// 无`dst`创建器，
		if rt.Kind() == reflect.Pointer {
			dst = reflect.New(rt.Elem()).Interface()
		} else {
			obj := reflect.New(rt)
			dst = obj.Interface()
			defer func() {
				dst = obj.Elem().Interface()
			}()
		}
	} else {
		dst = creator()
		drv := reflect.ValueOf(dst)
		if drv.Kind() != reflect.Pointer {
			if rt.Kind() != reflect.Pointer {
				defer func() {
					dst = drv.Interface()
				}()
			}
			dst = drv.Addr().Interface()
		} else if rt.Kind() != reflect.Ptr {
			defer func() {
				dst = drv.Elem().Interface()
			}()
		}
	}

	// 这里的`dst`必需是指针
	err = utils.StringToAny(src, dst)

	// 这里的`dst`需要根据`dstType`来确定返回的是指针还是非指针
	return dst, err
}

func NewTimeoutContext(current context.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)

	spanCtx := trace.SpanContextFromContext(current)
	if spanCtx.HasTraceID() {
		ctx = trace.ContextWithSpanContext(ctx, spanCtx)
	}

	if ui := userinfo.FromContext(current); ui != nil {
		ctx = userinfo.WithContext(ctx, ui)
	}

	return ctx, cancel
}
