package utils

import (
	"context"
	"testing"
	"time"
)

func TestRandomChoose(t *testing.T) {
	var (
		intOpts = []any{1, 22, 333}
		strOpts = []any{"allen", "bob", "charles"}
	)

	type args struct {
		options []any
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "random choose int",
			args: args{
				options: intOpts,
			},
		},
		{
			name: "random choose string",
			args: args{
				options: strOpts,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				for i := 0; i < 10; i++ {
					option := RandomChoose(tt.args.options)
					t.Logf("RandomChoose: %[1]v(%[1]T)", option)
				}
			},
		)
	}
}

func Test_GetGRPCFullMethodName(t *testing.T) {
	type args struct {
		method string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2",
			args: args{
				method: "ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2",
			},
			want: "/ga.api.channel_core.ChannelCoreLogic/ChannelEnterV2",
		},
		{
			name: "/ga.api.channel_core.ChannelCoreLogic/ChannelEnterV2",
			args: args{
				method: "/ga.api.channel_core.ChannelCoreLogic/ChannelEnterV2",
			},
			want: "/ga.api.channel_core.ChannelCoreLogic/ChannelEnterV2",
		},
		{
			name: "ga.api.channel_core.ChannelCoreLogic/ChannelEnterV2",
			args: args{
				method: "ga.api.channel_core.ChannelCoreLogic/ChannelEnterV2",
			},
			want: "/ga.api.channel_core.ChannelCoreLogic/ChannelEnterV2",
		},
		{
			name: "manager.ApiExecutionService.GetApiExecutionData",
			args: args{
				method: "manager.ApiExecutionService.GetApiExecutionData",
			},
			want: "/manager.ApiExecutionService/GetApiExecutionData",
		},
		{
			name: "/manager.ApiExecutionService/GetApiExecutionData",
			args: args{
				method: "/manager.ApiExecutionService/GetApiExecutionData",
			},
			want: "/manager.ApiExecutionService/GetApiExecutionData",
		},
		{
			name: "POST",
			args: args{
				method: "POST",
			},
			want: "POST",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetGRPCFullMethodName(tt.args.method); got != tt.want {
					t.Errorf("requestMethod() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_NewTimeoutContext(t *testing.T) {
	bg := context.Background()
	ctx, cancel := context.WithCancel(bg)
	defer cancel()

	ctx1, cancel1 := NewTimeoutContext(ctx, 5 * time.Second)
	defer cancel1()

	ctx2, cancel2 := NewTimeoutContext(ctx, 3 * time.Second)
	defer cancel2()

	timeout := 6 * time.Second
	timer := time.NewTimer(timeout)
	defer timer.Stop()

	var b1, b2 bool
	for {
		select {
		case <-bg.Done():
			t.Logf("bg: %v", bg.Err())
			return
		case <-ctx.Done():
			t.Logf("ctx: %v", ctx.Err())
			return
		case <-ctx1.Done():
			if !b1 {
				t.Logf("ctx1: %v", ctx1.Err())
				b1 = true
			}
		case <-ctx2.Done():
			if !b2 {
				t.Logf("ctx2: %v", ctx2.Err())
				b2 = true
			}
		case <-timer.C:
			t.Logf("timer: %s", timeout.String())
			return
		}
	}
}
