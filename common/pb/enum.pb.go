// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/enum.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TriggerMode 触发模式
type TriggerMode int32

const (
	TriggerMode_NULL      TriggerMode = 0 // NULL
	TriggerMode_MANUAL    TriggerMode = 1 // 手动触发
	TriggerMode_SCHEDULE  TriggerMode = 2 // 定时器触发
	TriggerMode_INTERFACE TriggerMode = 3 // 接口触发
)

// Enum value maps for TriggerMode.
var (
	TriggerMode_name = map[int32]string{
		0: "NULL",
		1: "MANUAL",
		2: "SCHEDULE",
		3: "INTERFACE",
	}
	TriggerMode_value = map[string]int32{
		"NULL":      0,
		"MANUAL":    1,
		"SCHEDULE":  2,
		"INTERFACE": 3,
	}
)

func (x TriggerMode) Enum() *TriggerMode {
	p := new(TriggerMode)
	*p = x
	return p
}

func (x TriggerMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TriggerMode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[0].Descriptor()
}

func (TriggerMode) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[0]
}

func (x TriggerMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TriggerMode.Descriptor instead.
func (TriggerMode) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{0}
}

// PurposeType 计划用途
type PurposeType int32

const (
	PurposeType_UNDEFINED         PurposeType = 0 // 未定义
	PurposeType_NORMAL            PurposeType = 1 // 常规
	PurposeType_PRECISION_TESTING PurposeType = 2 // 精准测试
)

// Enum value maps for PurposeType.
var (
	PurposeType_name = map[int32]string{
		0: "UNDEFINED",
		1: "NORMAL",
		2: "PRECISION_TESTING",
	}
	PurposeType_value = map[string]int32{
		"UNDEFINED":         0,
		"NORMAL":            1,
		"PRECISION_TESTING": 2,
	}
)

func (x PurposeType) Enum() *PurposeType {
	p := new(PurposeType)
	*p = x
	return p
}

func (x PurposeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PurposeType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[1].Descriptor()
}

func (PurposeType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[1]
}

func (x PurposeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PurposeType.Descriptor instead.
func (PurposeType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{1}
}

// DeviceType 设备类型
type DeviceType int32

const (
	DeviceType_DT_NULL     DeviceType = 0 // NULL
	DeviceType_REAL_PHONE  DeviceType = 1 // 真机
	DeviceType_CLOUD_PHONE DeviceType = 2 // 云手机
)

// Enum value maps for DeviceType.
var (
	DeviceType_name = map[int32]string{
		0: "DT_NULL",
		1: "REAL_PHONE",
		2: "CLOUD_PHONE",
	}
	DeviceType_value = map[string]int32{
		"DT_NULL":     0,
		"REAL_PHONE":  1,
		"CLOUD_PHONE": 2,
	}
)

func (x DeviceType) Enum() *DeviceType {
	p := new(DeviceType)
	*p = x
	return p
}

func (x DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[2].Descriptor()
}

func (DeviceType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[2]
}

func (x DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceType.Descriptor instead.
func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{2}
}

// PlatformType 平台类型
type PlatformType int32

const (
	PlatformType_PT_NULL   PlatformType = 0 // NULL
	PlatformType_ANDROID   PlatformType = 1 // 安卓
	PlatformType_IOS       PlatformType = 2 // IOS
	PlatformType_HarmonyOS PlatformType = 3 // HarmonyOS
)

// Enum value maps for PlatformType.
var (
	PlatformType_name = map[int32]string{
		0: "PT_NULL",
		1: "ANDROID",
		2: "IOS",
		3: "HarmonyOS",
	}
	PlatformType_value = map[string]int32{
		"PT_NULL":   0,
		"ANDROID":   1,
		"IOS":       2,
		"HarmonyOS": 3,
	}
)

func (x PlatformType) Enum() *PlatformType {
	p := new(PlatformType)
	*p = x
	return p
}

func (x PlatformType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlatformType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[3].Descriptor()
}

func (PlatformType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[3]
}

func (x PlatformType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlatformType.Descriptor instead.
func (PlatformType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{3}
}

// OperationType 操作类型
type OperationType int32

const (
	OperationType_OT_NULL OperationType = 0 // NULL
	OperationType_ADD     OperationType = 1 // 添加
	OperationType_REMOVE  OperationType = 2 // 删除
)

// Enum value maps for OperationType.
var (
	OperationType_name = map[int32]string{
		0: "OT_NULL",
		1: "ADD",
		2: "REMOVE",
	}
	OperationType_value = map[string]int32{
		"OT_NULL": 0,
		"ADD":     1,
		"REMOVE":  2,
	}
)

func (x OperationType) Enum() *OperationType {
	p := new(OperationType)
	*p = x
	return p
}

func (x OperationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperationType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[4].Descriptor()
}

func (OperationType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[4]
}

func (x OperationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperationType.Descriptor instead.
func (OperationType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{4}
}

// DeviceUsage 设备用途
type DeviceUsage int32

const (
	DeviceUsage_DU_NULL           DeviceUsage = 0 // NULL
	DeviceUsage_UI_TESTING        DeviceUsage = 1 // UI测试
	DeviceUsage_STABILITY_TESTING DeviceUsage = 2 // 稳定性测试
)

// Enum value maps for DeviceUsage.
var (
	DeviceUsage_name = map[int32]string{
		0: "DU_NULL",
		1: "UI_TESTING",
		2: "STABILITY_TESTING",
	}
	DeviceUsage_value = map[string]int32{
		"DU_NULL":           0,
		"UI_TESTING":        1,
		"STABILITY_TESTING": 2,
	}
)

func (x DeviceUsage) Enum() *DeviceUsage {
	p := new(DeviceUsage)
	*p = x
	return p
}

func (x DeviceUsage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceUsage) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[5].Descriptor()
}

func (DeviceUsage) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[5]
}

func (x DeviceUsage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceUsage.Descriptor instead.
func (DeviceUsage) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{5}
}

// FailRetry 失败重试
type FailRetry int32

const (
	FailRetry_ZERO FailRetry = 0 // 0次
	FailRetry_ONE  FailRetry = 1 // 1次
	FailRetry_TWO  FailRetry = 2 // 2次
)

// Enum value maps for FailRetry.
var (
	FailRetry_name = map[int32]string{
		0: "ZERO",
		1: "ONE",
		2: "TWO",
	}
	FailRetry_value = map[string]int32{
		"ZERO": 0,
		"ONE":  1,
		"TWO":  2,
	}
)

func (x FailRetry) Enum() *FailRetry {
	p := new(FailRetry)
	*p = x
	return p
}

func (x FailRetry) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FailRetry) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[6].Descriptor()
}

func (FailRetry) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[6]
}

func (x FailRetry) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FailRetry.Descriptor instead.
func (FailRetry) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{6}
}

// PlanType 计划类型
type PlanType int32

const (
	PlanType_API       PlanType = 0 // API测试计划
	PlanType_UI        PlanType = 1 // UI测试计划
	PlanType_PERF      PlanType = 2 // 压力测试计划
	PlanType_STABILITY PlanType = 3 // 稳定性测试计划
)

// Enum value maps for PlanType.
var (
	PlanType_name = map[int32]string{
		0: "API",
		1: "UI",
		2: "PERF",
		3: "STABILITY",
	}
	PlanType_value = map[string]int32{
		"API":       0,
		"UI":        1,
		"PERF":      2,
		"STABILITY": 3,
	}
)

func (x PlanType) Enum() *PlanType {
	p := new(PlanType)
	*p = x
	return p
}

func (x PlanType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlanType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[7].Descriptor()
}

func (PlanType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[7]
}

func (x PlanType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlanType.Descriptor instead.
func (PlanType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{7}
}

// 优先级类型
type PriorityType int32

const (
	PriorityType_Default PriorityType = 0 // 缺省
	PriorityType_Middle  PriorityType = 1 // 对照消息优先级默认:"default":10%
	PriorityType_High    PriorityType = 2 // 对照消息优先级默认:"high":38%
	PriorityType_Ultra   PriorityType = 3 // 对照消息优先级默认:"ultra":50%
	PriorityType_Low     PriorityType = 4 // 对照消息优先级默认:"low":7%
)

// Enum value maps for PriorityType.
var (
	PriorityType_name = map[int32]string{
		0: "Default",
		1: "Middle",
		2: "High",
		3: "Ultra",
		4: "Low",
	}
	PriorityType_value = map[string]int32{
		"Default": 0,
		"Middle":  1,
		"High":    2,
		"Ultra":   3,
		"Low":     4,
	}
)

func (x PriorityType) Enum() *PriorityType {
	p := new(PriorityType)
	*p = x
	return p
}

func (x PriorityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriorityType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[8].Descriptor()
}

func (PriorityType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[8]
}

func (x PriorityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriorityType.Descriptor instead.
func (PriorityType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{8}
}

// TestLanguage 测试语言
type TestLanguage int32

const (
	TestLanguage_TestLanguage_NULL   TestLanguage = 0
	TestLanguage_TestLanguage_PYTHON TestLanguage = 1 // Python
	TestLanguage_TestLanguage_GOLANG TestLanguage = 2 // Golang
)

// Enum value maps for TestLanguage.
var (
	TestLanguage_name = map[int32]string{
		0: "TestLanguage_NULL",
		1: "TestLanguage_PYTHON",
		2: "TestLanguage_GOLANG",
	}
	TestLanguage_value = map[string]int32{
		"TestLanguage_NULL":   0,
		"TestLanguage_PYTHON": 1,
		"TestLanguage_GOLANG": 2,
	}
)

func (x TestLanguage) Enum() *TestLanguage {
	p := new(TestLanguage)
	*p = x
	return p
}

func (x TestLanguage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestLanguage) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[9].Descriptor()
}

func (TestLanguage) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[9]
}

func (x TestLanguage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestLanguage.Descriptor instead.
func (TestLanguage) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{9}
}

// TestFramework 测试框架
type TestFramework int32

const (
	TestFramework_TestFramework_NULL   TestFramework = 0
	TestFramework_TestFramework_PYTEST TestFramework = 1 // pytest
)

// Enum value maps for TestFramework.
var (
	TestFramework_name = map[int32]string{
		0: "TestFramework_NULL",
		1: "TestFramework_PYTEST",
	}
	TestFramework_value = map[string]int32{
		"TestFramework_NULL":   0,
		"TestFramework_PYTEST": 1,
	}
)

func (x TestFramework) Enum() *TestFramework {
	p := new(TestFramework)
	*p = x
	return p
}

func (x TestFramework) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestFramework) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[10].Descriptor()
}

func (TestFramework) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[10]
}

func (x TestFramework) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestFramework.Descriptor instead.
func (TestFramework) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{10}
}

// ExecuteStatus 执行状态
type ExecuteStatus int32

const (
	ExecuteStatus_TES_INIT      ExecuteStatus = 0 // 排队中
	ExecuteStatus_TES_EXECUTING ExecuteStatus = 1 // 执行中
	ExecuteStatus_TES_FINISH    ExecuteStatus = 2 // 已完成(终态)
	ExecuteStatus_TES_STOP      ExecuteStatus = 3 // 已停止(终态)
)

// Enum value maps for ExecuteStatus.
var (
	ExecuteStatus_name = map[int32]string{
		0: "TES_INIT",
		1: "TES_EXECUTING",
		2: "TES_FINISH",
		3: "TES_STOP",
	}
	ExecuteStatus_value = map[string]int32{
		"TES_INIT":      0,
		"TES_EXECUTING": 1,
		"TES_FINISH":    2,
		"TES_STOP":      3,
	}
)

func (x ExecuteStatus) Enum() *ExecuteStatus {
	p := new(ExecuteStatus)
	*p = x
	return p
}

func (x ExecuteStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExecuteStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[11].Descriptor()
}

func (ExecuteStatus) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[11]
}

func (x ExecuteStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExecuteStatus.Descriptor instead.
func (ExecuteStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{11}
}

// ExecutedResult 执行结果
type ExecutedResult int32

const (
	ExecutedResult_TER_INIT    ExecutedResult = 0 // 缺省
	ExecutedResult_TER_SUCCESS ExecutedResult = 1 // 成功(终态)
	ExecutedResult_TER_FAILURE ExecutedResult = 2 // 失败(终态)
	ExecutedResult_TER_PANIC   ExecutedResult = 3 // 异常(终态)
)

// Enum value maps for ExecutedResult.
var (
	ExecutedResult_name = map[int32]string{
		0: "TER_INIT",
		1: "TER_SUCCESS",
		2: "TER_FAILURE",
		3: "TER_PANIC",
	}
	ExecutedResult_value = map[string]int32{
		"TER_INIT":    0,
		"TER_SUCCESS": 1,
		"TER_FAILURE": 2,
		"TER_PANIC":   3,
	}
)

func (x ExecutedResult) Enum() *ExecutedResult {
	p := new(ExecutedResult)
	*p = x
	return p
}

func (x ExecutedResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExecutedResult) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[12].Descriptor()
}

func (ExecutedResult) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[12]
}

func (x ExecutedResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExecutedResult.Descriptor instead.
func (ExecutedResult) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{12}
}

// TestStage 测试阶段
type TestStage int32

const (
	TestStage_TS_NULL  TestStage = 0 // NULL
	TestStage_SETUP    TestStage = 1 // 前置步骤
	TestStage_TEST     TestStage = 2 // 测试步骤
	TestStage_TEARDOWN TestStage = 3 // 后置步骤
)

// Enum value maps for TestStage.
var (
	TestStage_name = map[int32]string{
		0: "TS_NULL",
		1: "SETUP",
		2: "TEST",
		3: "TEARDOWN",
	}
	TestStage_value = map[string]int32{
		"TS_NULL":  0,
		"SETUP":    1,
		"TEST":     2,
		"TEARDOWN": 3,
	}
)

func (x TestStage) Enum() *TestStage {
	p := new(TestStage)
	*p = x
	return p
}

func (x TestStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestStage) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[13].Descriptor()
}

func (TestStage) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[13]
}

func (x TestStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestStage.Descriptor instead.
func (TestStage) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{13}
}

// Protocol 协议
type Protocol int32

const (
	Protocol_PROTOCOL_NULL Protocol = 0 // NULL
	// Common Protocol
	Protocol_PROTOCOL_HTTP Protocol = 1 // HTTP
	Protocol_PROTOCOL_GRPC Protocol = 2 // gRPC
	// Private Protocol
	Protocol_PROTOCOL_TT      Protocol = 21 // TT私有协议
	Protocol_PROTOCOL_TT_AUTH Protocol = 22 // TT登录压测场景专用
)

// Enum value maps for Protocol.
var (
	Protocol_name = map[int32]string{
		0:  "PROTOCOL_NULL",
		1:  "PROTOCOL_HTTP",
		2:  "PROTOCOL_GRPC",
		21: "PROTOCOL_TT",
		22: "PROTOCOL_TT_AUTH",
	}
	Protocol_value = map[string]int32{
		"PROTOCOL_NULL":    0,
		"PROTOCOL_HTTP":    1,
		"PROTOCOL_GRPC":    2,
		"PROTOCOL_TT":      21,
		"PROTOCOL_TT_AUTH": 22,
	}
)

func (x Protocol) Enum() *Protocol {
	p := new(Protocol)
	*p = x
	return p
}

func (x Protocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Protocol) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[14].Descriptor()
}

func (Protocol) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[14]
}

func (x Protocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Protocol.Descriptor instead.
func (Protocol) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{14}
}

// TargetEnvironment 目标环境
type TargetEnvironment int32

const (
	TargetEnvironment_TE_NULL        TargetEnvironment = 0 // NULL
	TargetEnvironment_TE_DEVELOPMENT TargetEnvironment = 1 // 开发环境
	TargetEnvironment_TE_TESTING     TargetEnvironment = 2 // 测试环境
	TargetEnvironment_TE_STAGING     TargetEnvironment = 3 // 预发布环境
	TargetEnvironment_TE_CANARY      TargetEnvironment = 4 // 灰度环境
	TargetEnvironment_TE_PRODUCTION  TargetEnvironment = 5 // 生产环境
)

// Enum value maps for TargetEnvironment.
var (
	TargetEnvironment_name = map[int32]string{
		0: "TE_NULL",
		1: "TE_DEVELOPMENT",
		2: "TE_TESTING",
		3: "TE_STAGING",
		4: "TE_CANARY",
		5: "TE_PRODUCTION",
	}
	TargetEnvironment_value = map[string]int32{
		"TE_NULL":        0,
		"TE_DEVELOPMENT": 1,
		"TE_TESTING":     2,
		"TE_STAGING":     3,
		"TE_CANARY":      4,
		"TE_PRODUCTION":  5,
	}
)

func (x TargetEnvironment) Enum() *TargetEnvironment {
	p := new(TargetEnvironment)
	*p = x
	return p
}

func (x TargetEnvironment) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetEnvironment) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[15].Descriptor()
}

func (TargetEnvironment) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[15]
}

func (x TargetEnvironment) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetEnvironment.Descriptor instead.
func (TargetEnvironment) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{15}
}

// PerfTaskType 压测任务类型
type PerfTaskType int32

const (
	PerfTaskType_PTT_NULL PerfTaskType = 0
	PerfTaskType_RUN      PerfTaskType = 1 // 执行
	PerfTaskType_DEBUG    PerfTaskType = 2 // 调试
)

// Enum value maps for PerfTaskType.
var (
	PerfTaskType_name = map[int32]string{
		0: "PTT_NULL",
		1: "RUN",
		2: "DEBUG",
	}
	PerfTaskType_value = map[string]int32{
		"PTT_NULL": 0,
		"RUN":      1,
		"DEBUG":    2,
	}
)

func (x PerfTaskType) Enum() *PerfTaskType {
	p := new(PerfTaskType)
	*p = x
	return p
}

func (x PerfTaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PerfTaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[16].Descriptor()
}

func (PerfTaskType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[16]
}

func (x PerfTaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PerfTaskType.Descriptor instead.
func (PerfTaskType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{16}
}

// PerfTaskExecutionMode 压测任务执行方式
type PerfTaskExecutionMode int32

const (
	PerfTaskExecutionMode_PTEM_NULL   PerfTaskExecutionMode = 0
	PerfTaskExecutionMode_BY_DURATION PerfTaskExecutionMode = 1 // 按时长
	PerfTaskExecutionMode_BY_TIMES    PerfTaskExecutionMode = 2 // 按次数
)

// Enum value maps for PerfTaskExecutionMode.
var (
	PerfTaskExecutionMode_name = map[int32]string{
		0: "PTEM_NULL",
		1: "BY_DURATION",
		2: "BY_TIMES",
	}
	PerfTaskExecutionMode_value = map[string]int32{
		"PTEM_NULL":   0,
		"BY_DURATION": 1,
		"BY_TIMES":    2,
	}
)

func (x PerfTaskExecutionMode) Enum() *PerfTaskExecutionMode {
	p := new(PerfTaskExecutionMode)
	*p = x
	return p
}

func (x PerfTaskExecutionMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PerfTaskExecutionMode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[17].Descriptor()
}

func (PerfTaskExecutionMode) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[17]
}

func (x PerfTaskExecutionMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PerfTaskExecutionMode.Descriptor instead.
func (PerfTaskExecutionMode) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{17}
}

// PerfCaseStepType 压测用例步骤类型
type PerfCaseStepType int32

const (
	PerfCaseStepType_PerfCaseStepType_NULL     PerfCaseStepType = 0 // NULL
	PerfCaseStepType_PerfCaseStepType_SETUP    PerfCaseStepType = 1 // 前置步骤
	PerfCaseStepType_PerfCaseStepType_SERIAL   PerfCaseStepType = 2 // 串行步骤
	PerfCaseStepType_PerfCaseStepType_PARALLEL PerfCaseStepType = 3 // 并行步骤
	PerfCaseStepType_PerfCaseStepType_TEARDOWN PerfCaseStepType = 4 // 后置步骤
)

// Enum value maps for PerfCaseStepType.
var (
	PerfCaseStepType_name = map[int32]string{
		0: "PerfCaseStepType_NULL",
		1: "PerfCaseStepType_SETUP",
		2: "PerfCaseStepType_SERIAL",
		3: "PerfCaseStepType_PARALLEL",
		4: "PerfCaseStepType_TEARDOWN",
	}
	PerfCaseStepType_value = map[string]int32{
		"PerfCaseStepType_NULL":     0,
		"PerfCaseStepType_SETUP":    1,
		"PerfCaseStepType_SERIAL":   2,
		"PerfCaseStepType_PARALLEL": 3,
		"PerfCaseStepType_TEARDOWN": 4,
	}
)

func (x PerfCaseStepType) Enum() *PerfCaseStepType {
	p := new(PerfCaseStepType)
	*p = x
	return p
}

func (x PerfCaseStepType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PerfCaseStepType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[18].Descriptor()
}

func (PerfCaseStepType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[18]
}

func (x PerfCaseStepType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PerfCaseStepType.Descriptor instead.
func (PerfCaseStepType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{18}
}

// MonitorUrlType 监控地址类型
type MonitorUrlType int32

const (
	MonitorUrlType_MUT_NULL        MonitorUrlType = 0
	MonitorUrlType_MUT_GRAFANA     MonitorUrlType = 1 // Grafana
	MonitorUrlType_MUT_APP_INSIGHT MonitorUrlType = 2 // 天相
)

// Enum value maps for MonitorUrlType.
var (
	MonitorUrlType_name = map[int32]string{
		0: "MUT_NULL",
		1: "MUT_GRAFANA",
		2: "MUT_APP_INSIGHT",
	}
	MonitorUrlType_value = map[string]int32{
		"MUT_NULL":        0,
		"MUT_GRAFANA":     1,
		"MUT_APP_INSIGHT": 2,
	}
)

func (x MonitorUrlType) Enum() *MonitorUrlType {
	p := new(MonitorUrlType)
	*p = x
	return p
}

func (x MonitorUrlType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MonitorUrlType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[19].Descriptor()
}

func (MonitorUrlType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[19]
}

func (x MonitorUrlType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MonitorUrlType.Descriptor instead.
func (MonitorUrlType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{19}
}

// MetricType 指标类型
type MetricType int32

const (
	MetricType_MetricType_NULL      MetricType = 0
	MetricType_MetricType_QPS       MetricType = 1 // qps
	MetricType_MetricType_FailRatio MetricType = 2 // 失败率
	MetricType_MetricType_P99       MetricType = 3 // P99
	MetricType_MetricType_P95       MetricType = 4 // P95
	MetricType_MetricType_P90       MetricType = 5 // P90
	MetricType_MetricType_P75       MetricType = 6 // P75
	MetricType_MetricType_P50       MetricType = 7 // P50
)

// Enum value maps for MetricType.
var (
	MetricType_name = map[int32]string{
		0: "MetricType_NULL",
		1: "MetricType_QPS",
		2: "MetricType_FailRatio",
		3: "MetricType_P99",
		4: "MetricType_P95",
		5: "MetricType_P90",
		6: "MetricType_P75",
		7: "MetricType_P50",
	}
	MetricType_value = map[string]int32{
		"MetricType_NULL":      0,
		"MetricType_QPS":       1,
		"MetricType_FailRatio": 2,
		"MetricType_P99":       3,
		"MetricType_P95":       4,
		"MetricType_P90":       5,
		"MetricType_P75":       6,
		"MetricType_P50":       7,
	}
)

func (x MetricType) Enum() *MetricType {
	p := new(MetricType)
	*p = x
	return p
}

func (x MetricType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MetricType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[20].Descriptor()
}

func (MetricType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[20]
}

func (x MetricType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MetricType.Descriptor instead.
func (MetricType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{20}
}

// PerfDataType 性能数据类型
type PerfDataType int32

const (
	PerfDataType_PerfDataType_NULL    PerfDataType = 0
	PerfDataType_PerfDataType_CPU     PerfDataType = 1 // CPU
	PerfDataType_PerfDataType_MEMORY  PerfDataType = 2 // 内存
	PerfDataType_PerfDataType_FPS     PerfDataType = 3 // 帧率
	PerfDataType_PerfDataType_DISK    PerfDataType = 4 // 磁盘
	PerfDataType_PerfDataType_NETWORK PerfDataType = 5 // 网络
)

// Enum value maps for PerfDataType.
var (
	PerfDataType_name = map[int32]string{
		0: "PerfDataType_NULL",
		1: "PerfDataType_CPU",
		2: "PerfDataType_MEMORY",
		3: "PerfDataType_FPS",
		4: "PerfDataType_DISK",
		5: "PerfDataType_NETWORK",
	}
	PerfDataType_value = map[string]int32{
		"PerfDataType_NULL":    0,
		"PerfDataType_CPU":     1,
		"PerfDataType_MEMORY":  2,
		"PerfDataType_FPS":     3,
		"PerfDataType_DISK":    4,
		"PerfDataType_NETWORK": 5,
	}
)

func (x PerfDataType) Enum() *PerfDataType {
	p := new(PerfDataType)
	*p = x
	return p
}

func (x PerfDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PerfDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[21].Descriptor()
}

func (PerfDataType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[21]
}

func (x PerfDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PerfDataType.Descriptor instead.
func (PerfDataType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{21}
}

// ArtifactType 产出物类型
type ArtifactType int32

const (
	ArtifactType_ArtifactType_NULL       ArtifactType = 0
	ArtifactType_ArtifactType_LOG        ArtifactType = 1 // 日志
	ArtifactType_ArtifactType_SCREENSHOT ArtifactType = 2 // 截图
)

// Enum value maps for ArtifactType.
var (
	ArtifactType_name = map[int32]string{
		0: "ArtifactType_NULL",
		1: "ArtifactType_LOG",
		2: "ArtifactType_SCREENSHOT",
	}
	ArtifactType_value = map[string]int32{
		"ArtifactType_NULL":       0,
		"ArtifactType_LOG":        1,
		"ArtifactType_SCREENSHOT": 2,
	}
)

func (x ArtifactType) Enum() *ArtifactType {
	p := new(ArtifactType)
	*p = x
	return p
}

func (x ArtifactType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ArtifactType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_enum_proto_enumTypes[22].Descriptor()
}

func (ArtifactType) Type() protoreflect.EnumType {
	return &file_common_enum_proto_enumTypes[22]
}

func (x ArtifactType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ArtifactType.Descriptor instead.
func (ArtifactType) EnumDescriptor() ([]byte, []int) {
	return file_common_enum_proto_rawDescGZIP(), []int{22}
}

var File_common_enum_proto protoreflect.FileDescriptor

var file_common_enum_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x16, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2a, 0x40, 0x0a, 0x0b, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x43, 0x48, 0x45,
	0x44, 0x55, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x46,
	0x41, 0x43, 0x45, 0x10, 0x03, 0x2a, 0x3f, 0x0a, 0x0b, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12,
	0x15, 0x0a, 0x11, 0x50, 0x52, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x45, 0x53,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x2a, 0x3a, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10,
	0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10,
	0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x4c, 0x4f, 0x55, 0x44, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45,
	0x10, 0x02, 0x2a, 0x65, 0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12,
	0x18, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x1a, 0x0b, 0x82, 0xb5,
	0x18, 0x07, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x49, 0x4f, 0x53,
	0x10, 0x02, 0x1a, 0x07, 0x82, 0xb5, 0x18, 0x03, 0x69, 0x4f, 0x53, 0x12, 0x1c, 0x0a, 0x09, 0x48,
	0x61, 0x72, 0x6d, 0x6f, 0x6e, 0x79, 0x4f, 0x53, 0x10, 0x03, 0x1a, 0x0d, 0x82, 0xb5, 0x18, 0x09,
	0x48, 0x61, 0x72, 0x6d, 0x6f, 0x6e, 0x79, 0x4f, 0x53, 0x2a, 0x31, 0x0a, 0x0d, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x54,
	0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x44, 0x44, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x10, 0x02, 0x2a, 0x58, 0x0a, 0x0b,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44,
	0x55, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x0a, 0x55, 0x49, 0x5f, 0x54,
	0x45, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x1a, 0x06, 0x82, 0xb5, 0x18, 0x02, 0x55, 0x49,
	0x12, 0x24, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x45,
	0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x1a, 0x0d, 0x82, 0xb5, 0x18, 0x09, 0x53, 0x54, 0x41,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x2a, 0x27, 0x0a, 0x09, 0x46, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x12, 0x07, 0x0a,
	0x03, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x57, 0x4f, 0x10, 0x02, 0x2a,
	0x34, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41,
	0x50, 0x49, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x55, 0x49, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04,
	0x50, 0x45, 0x52, 0x46, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x54, 0x41, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x10, 0x03, 0x2a, 0x45, 0x0a, 0x0c, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x48, 0x69, 0x67, 0x68, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x55, 0x6c, 0x74, 0x72,
	0x61, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x6f, 0x77, 0x10, 0x04, 0x2a, 0x57, 0x0a, 0x0c,
	0x54, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x15, 0x0a, 0x11,
	0x54, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x4e, 0x55, 0x4c,
	0x4c, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13,
	0x54, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x47, 0x4f, 0x4c,
	0x41, 0x4e, 0x47, 0x10, 0x02, 0x2a, 0x41, 0x0a, 0x0d, 0x54, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x65, 0x73, 0x74, 0x46, 0x72,
	0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x18,
	0x0a, 0x14, 0x54, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x50, 0x59, 0x54, 0x45, 0x53, 0x54, 0x10, 0x01, 0x2a, 0x4e, 0x0a, 0x0d, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45, 0x53,
	0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x45, 0x53, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45,
	0x53, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x10, 0x03, 0x2a, 0x4f, 0x0a, 0x0e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45,
	0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x45, 0x52, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x45, 0x52,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x45,
	0x52, 0x5f, 0x50, 0x41, 0x4e, 0x49, 0x43, 0x10, 0x03, 0x2a, 0x5e, 0x0a, 0x09, 0x54, 0x65, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x53, 0x5f, 0x4e, 0x55, 0x4c,
	0x4c, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x05, 0x53, 0x45, 0x54, 0x55, 0x50, 0x10, 0x01, 0x1a, 0x09,
	0x82, 0xb5, 0x18, 0x05, 0x73, 0x65, 0x74, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x45, 0x53,
	0x54, 0x10, 0x02, 0x1a, 0x08, 0x82, 0xb5, 0x18, 0x04, 0x74, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x54, 0x45, 0x41, 0x52, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x1a, 0x0c, 0x82, 0xb5, 0x18,
	0x08, 0x74, 0x65, 0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x2a, 0x93, 0x01, 0x0a, 0x08, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43,
	0x4f, 0x4c, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x0d, 0x50, 0x52, 0x4f,
	0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x48, 0x54, 0x54, 0x50, 0x10, 0x01, 0x1a, 0x08, 0x82, 0xb5,
	0x18, 0x04, 0x48, 0x54, 0x54, 0x50, 0x12, 0x1b, 0x0a, 0x0d, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43,
	0x4f, 0x4c, 0x5f, 0x47, 0x52, 0x50, 0x43, 0x10, 0x02, 0x1a, 0x08, 0x82, 0xb5, 0x18, 0x04, 0x67,
	0x52, 0x50, 0x43, 0x12, 0x17, 0x0a, 0x0b, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f,
	0x54, 0x54, 0x10, 0x15, 0x1a, 0x06, 0x82, 0xb5, 0x18, 0x02, 0x54, 0x54, 0x12, 0x21, 0x0a, 0x10,
	0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x54, 0x54, 0x5f, 0x41, 0x55, 0x54, 0x48,
	0x10, 0x16, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x54, 0x54, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x2a,
	0xbd, 0x01, 0x0a, 0x11, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f,
	0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x45, 0x5f, 0x4e, 0x55, 0x4c, 0x4c,
	0x10, 0x00, 0x12, 0x23, 0x0a, 0x0e, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x1a, 0x0f, 0x82, 0xb5, 0x18, 0x0b, 0x44, 0x45, 0x56, 0x45,
	0x4c, 0x4f, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x12, 0x1b, 0x0a, 0x0a, 0x54, 0x45, 0x5f, 0x54, 0x45,
	0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x54, 0x45, 0x53,
	0x54, 0x49, 0x4e, 0x47, 0x12, 0x1b, 0x0a, 0x0a, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x53, 0x54, 0x41, 0x47, 0x49, 0x4e,
	0x47, 0x12, 0x19, 0x0a, 0x09, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x41, 0x52, 0x59, 0x10, 0x04,
	0x1a, 0x0a, 0x82, 0xb5, 0x18, 0x06, 0x43, 0x41, 0x4e, 0x41, 0x52, 0x59, 0x12, 0x21, 0x0a, 0x0d,
	0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x1a,
	0x0e, 0x82, 0xb5, 0x18, 0x0a, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x2a,
	0x30, 0x0a, 0x0c, 0x50, 0x65, 0x72, 0x66, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0c, 0x0a, 0x08, 0x50, 0x54, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x07, 0x0a,
	0x03, 0x52, 0x55, 0x4e, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x45, 0x42, 0x55, 0x47, 0x10,
	0x02, 0x2a, 0x45, 0x0a, 0x15, 0x50, 0x65, 0x72, 0x66, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x54,
	0x45, 0x4d, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x59, 0x5f,
	0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x59,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x10, 0x02, 0x2a, 0xd7, 0x01, 0x0a, 0x10, 0x50, 0x65, 0x72,
	0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x15, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x16, 0x50, 0x65, 0x72, 0x66,
	0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x45, 0x54,
	0x55, 0x50, 0x10, 0x01, 0x1a, 0x09, 0x82, 0xb5, 0x18, 0x05, 0x53, 0x45, 0x54, 0x55, 0x50, 0x12,
	0x27, 0x0a, 0x17, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x53, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x10, 0x02, 0x1a, 0x0a, 0x82, 0xb5,
	0x18, 0x06, 0x53, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x12, 0x2b, 0x0a, 0x19, 0x50, 0x65, 0x72, 0x66,
	0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x41, 0x52,
	0x41, 0x4c, 0x4c, 0x45, 0x4c, 0x10, 0x03, 0x1a, 0x0c, 0x82, 0xb5, 0x18, 0x08, 0x50, 0x41, 0x52,
	0x41, 0x4c, 0x4c, 0x45, 0x4c, 0x12, 0x2b, 0x0a, 0x19, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73,
	0x65, 0x53, 0x74, 0x65, 0x70, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x45, 0x41, 0x52, 0x44, 0x4f,
	0x57, 0x4e, 0x10, 0x04, 0x1a, 0x0c, 0x82, 0xb5, 0x18, 0x08, 0x54, 0x45, 0x41, 0x52, 0x44, 0x4f,
	0x57, 0x4e, 0x2a, 0x61, 0x0a, 0x0e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x55, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x0b, 0x4d, 0x55, 0x54, 0x5f, 0x47, 0x52, 0x41, 0x46, 0x41, 0x4e,
	0x41, 0x10, 0x01, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x47, 0x72, 0x61, 0x66, 0x61, 0x6e, 0x61,
	0x12, 0x23, 0x0a, 0x0f, 0x4d, 0x55, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x4e, 0x53, 0x49,
	0x47, 0x48, 0x54, 0x10, 0x02, 0x1a, 0x0e, 0x82, 0xb5, 0x18, 0x0a, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x2a, 0xf9, 0x01, 0x0a, 0x0a, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x0e, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x51, 0x50, 0x53, 0x10, 0x01, 0x1a, 0x07, 0x82,
	0xb5, 0x18, 0x03, 0x71, 0x70, 0x73, 0x12, 0x28, 0x0a, 0x14, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x10, 0x02,
	0x1a, 0x0e, 0x82, 0xb5, 0x18, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x12, 0x1b, 0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50,
	0x39, 0x39, 0x10, 0x03, 0x1a, 0x07, 0x82, 0xb5, 0x18, 0x03, 0x70, 0x39, 0x39, 0x12, 0x1b, 0x0a,
	0x0e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x39, 0x35, 0x10,
	0x04, 0x1a, 0x07, 0x82, 0xb5, 0x18, 0x03, 0x70, 0x39, 0x35, 0x12, 0x1b, 0x0a, 0x0e, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x39, 0x30, 0x10, 0x05, 0x1a, 0x07,
	0x82, 0xb5, 0x18, 0x03, 0x70, 0x39, 0x30, 0x12, 0x1b, 0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x37, 0x35, 0x10, 0x06, 0x1a, 0x07, 0x82, 0xb5, 0x18,
	0x03, 0x70, 0x37, 0x35, 0x12, 0x1b, 0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x50, 0x35, 0x30, 0x10, 0x07, 0x1a, 0x07, 0x82, 0xb5, 0x18, 0x03, 0x70, 0x35,
	0x30, 0x2a, 0xd0, 0x01, 0x0a, 0x0c, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x10, 0x50, 0x65, 0x72,
	0x66, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x50, 0x55, 0x10, 0x01, 0x1a,
	0x07, 0x82, 0xb5, 0x18, 0x03, 0x43, 0x50, 0x55, 0x12, 0x23, 0x0a, 0x13, 0x50, 0x65, 0x72, 0x66,
	0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x10,
	0x02, 0x1a, 0x0a, 0x82, 0xb5, 0x18, 0x06, 0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x12, 0x1d, 0x0a,
	0x10, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x50,
	0x53, 0x10, 0x03, 0x1a, 0x07, 0x82, 0xb5, 0x18, 0x03, 0x46, 0x50, 0x53, 0x12, 0x1f, 0x0a, 0x11,
	0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x49, 0x53,
	0x4b, 0x10, 0x04, 0x1a, 0x08, 0x82, 0xb5, 0x18, 0x04, 0x44, 0x49, 0x53, 0x4b, 0x12, 0x25, 0x0a,
	0x14, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x45,
	0x54, 0x57, 0x4f, 0x52, 0x4b, 0x10, 0x05, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x4b, 0x2a, 0x71, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x10, 0x41,
	0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x4f, 0x47, 0x10,
	0x01, 0x1a, 0x07, 0x82, 0xb5, 0x18, 0x03, 0x4c, 0x4f, 0x47, 0x12, 0x2b, 0x0a, 0x17, 0x41, 0x72,
	0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x53, 0x48, 0x4f, 0x54, 0x10, 0x02, 0x1a, 0x0e, 0x82, 0xb5, 0x18, 0x0a, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x53, 0x48, 0x4f, 0x54, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65,
	0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72,
	0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_enum_proto_rawDescOnce sync.Once
	file_common_enum_proto_rawDescData = file_common_enum_proto_rawDesc
)

func file_common_enum_proto_rawDescGZIP() []byte {
	file_common_enum_proto_rawDescOnce.Do(func() {
		file_common_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_enum_proto_rawDescData)
	})
	return file_common_enum_proto_rawDescData
}

var file_common_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 23)
var file_common_enum_proto_goTypes = []any{
	(TriggerMode)(0),           // 0: common.TriggerMode
	(PurposeType)(0),           // 1: common.PurposeType
	(DeviceType)(0),            // 2: common.DeviceType
	(PlatformType)(0),          // 3: common.PlatformType
	(OperationType)(0),         // 4: common.OperationType
	(DeviceUsage)(0),           // 5: common.DeviceUsage
	(FailRetry)(0),             // 6: common.FailRetry
	(PlanType)(0),              // 7: common.PlanType
	(PriorityType)(0),          // 8: common.PriorityType
	(TestLanguage)(0),          // 9: common.TestLanguage
	(TestFramework)(0),         // 10: common.TestFramework
	(ExecuteStatus)(0),         // 11: common.ExecuteStatus
	(ExecutedResult)(0),        // 12: common.ExecutedResult
	(TestStage)(0),             // 13: common.TestStage
	(Protocol)(0),              // 14: common.Protocol
	(TargetEnvironment)(0),     // 15: common.TargetEnvironment
	(PerfTaskType)(0),          // 16: common.PerfTaskType
	(PerfTaskExecutionMode)(0), // 17: common.PerfTaskExecutionMode
	(PerfCaseStepType)(0),      // 18: common.PerfCaseStepType
	(MonitorUrlType)(0),        // 19: common.MonitorUrlType
	(MetricType)(0),            // 20: common.MetricType
	(PerfDataType)(0),          // 21: common.PerfDataType
	(ArtifactType)(0),          // 22: common.ArtifactType
}
var file_common_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_common_enum_proto_init() }
func file_common_enum_proto_init() {
	if File_common_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_enum_proto_rawDesc,
			NumEnums:      23,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_enum_proto_goTypes,
		DependencyIndexes: file_common_enum_proto_depIdxs,
		EnumInfos:         file_common_enum_proto_enumTypes,
	}.Build()
	File_common_enum_proto = out.File
	file_common_enum_proto_rawDesc = nil
	file_common_enum_proto_goTypes = nil
	file_common_enum_proto_depIdxs = nil
}
