package deviceservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type GetDeviceLogic struct {
	*BaseLogic
}

func NewGetDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeviceLogic {
	return &GetDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetDevice 获取设备（通过`udid`）
func (l *GetDeviceLogic) GetDevice(in *pb.GetDeviceReq) (out *pb.GetDeviceResp, err error) {
	device, err := l.checkDeviceByUDID(in.GetUdid())
	if err != nil {
		return nil, err
	}

	out = &pb.GetDeviceResp{Device: &pb.Device{}}
	if err = utils.Copy(out.Device, device, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy device to response, data: %s, error: %+v",
			jsonx.MarshalToStringIgnoreError(device), err,
		)
	}
	if out.GetDevice().GetToken() != "" {
		// hide the `token`
		out.Device.Token = constants.SensitiveWorld
	}

	return out, nil
}
