package zrpc

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/stabilityreporter"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

type StabilityReporterRpc struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  stabilityreporter.StabilityReporter
}

func NewStabilityReporterRpc(conf zrpc.RpcClientConf) *StabilityReporterRpc {
	cli := &StabilityReporterRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  stabilityreporter.NewStabilityReporter(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}

	return cli
}

func (cli *StabilityReporterRpc) CreateStabilityPlanRecord(
	ctx context.Context, req *reporter.PutStabilityPlanRecordReq,
) (*reporter.CreateStabilityPlanRecordResp, error) {
	return cli.Cli.CreateStabilityPlanRecord(ctx, req)
}

func (cli *StabilityReporterRpc) ModifyStabilityPlanRecord(
	ctx context.Context, req *reporter.PutStabilityPlanRecordReq,
) (*reporter.ModifyStabilityPlanRecordResp, error) {
	return cli.Cli.ModifyStabilityPlanRecord(ctx, req)
}

func (cli *StabilityReporterRpc) SearchStabilityPlanRecord(
	ctx context.Context, req *reporter.SearchStabilityPlanRecordReq,
) (*reporter.SearchStabilityPlanRecordResp, error) {
	return cli.Cli.SearchStabilityPlanRecord(ctx, req)
}

func (cli *StabilityReporterRpc) GetStabilityPlanRecord(
	ctx context.Context, req *reporter.GetStabilityPlanRecordReq,
) (*reporter.GetStabilityPlanRecordResp, error) {
	return cli.Cli.GetStabilityPlanRecord(ctx, req)
}

func (cli *StabilityReporterRpc) SearchStabilityDeviceRecord(
	ctx context.Context, req *reporter.SearchStabilityDeviceRecordReq,
) (*reporter.SearchStabilityDeviceRecordResp, error) {
	return cli.Cli.SearchStabilityDeviceRecord(ctx, req)
}
