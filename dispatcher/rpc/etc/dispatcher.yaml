Name: rpc.dispatcher
ListenOn: 127.0.0.1:20311
Timeout: 0

Log:
  ServiceName: rpc.dispatcher
  Encoding: plain
  Level: info
  Path: /app/logs/dispatcher

#Prometheus:
#  Host: 0.0.0.0
#  Port: 20322
#  Path: /metrics
#
#Telemetry:
#  Name: rpc.dispatcher
#  Endpoint: http://127.0.0.1:14268/api/traces
#  Sampler: 1.0
#  Batcher: jaeger
#
#DevServer:
#  Enabled: true
#  Port: 20332

DB:
  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/dispatcher?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Cache:
  - Host: 127.0.0.1:6379
    Pass:
    DB: 3

RedisConf:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 3

Manager:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.manager
  Endpoints:
    - 127.0.0.1:20211
  NonBlock: true
  Timeout: 0

Reporter:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.reporter
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

User:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.user
  Endpoints:
    - 127.0.0.1:10111
  NonBlock: true
  Timeout: 0

ApiWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:apiworker
  Db: 4

UIWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:uiworker
  Db: 4

PerfWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:perfworker
  Db: 4

DispatcherProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:dispatcher
  Db: 4

TaskConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mq:dispatcher_task
  ConsumerTag: rpc_dispatcher
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 5
  MaxWorker: 0

BeatConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mq:periodic_plan_task
  ConsumerTag: rpc_dispatcher
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 7
  MaxWorker: 0

WorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:worker
  Db: 14

StabilityWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:staworker
  Db: 23
