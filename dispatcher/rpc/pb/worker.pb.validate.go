// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/worker.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)

	_ = pb1.ApiExecutionDataType(0)
)

// Validate checks the field values on WorkerReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WorkerReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WorkerReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WorkerReqMultiError, or nil
// if none found.
func (m *WorkerReq) ValidateAll() error {
	return m.validate(true)
}

func (m *WorkerReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TriggerMode

	// no validation rules for TriggerRule

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for ExecuteType

	// no validation rules for WorkerType

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WorkerReqValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WorkerReqValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WorkerReqValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkerReqValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for User

	// no validation rules for UserId

	if all {
		switch v := interface{}(m.GetNodeData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WorkerReqValidationError{
					field:  "NodeData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WorkerReqValidationError{
					field:  "NodeData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WorkerReqValidationError{
				field:  "NodeData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PurposeType

	// no validation rules for PriorityType

	// no validation rules for Debug

	switch v := m.Data.(type) {
	case *WorkerReq_ComponentGroup:
		if v == nil {
			err := WorkerReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetComponentGroup()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "ComponentGroup",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "ComponentGroup",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetComponentGroup()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkerReqValidationError{
					field:  "ComponentGroup",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WorkerReq_Case:
		if v == nil {
			err := WorkerReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "Case",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "Case",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkerReqValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WorkerReq_InterfaceCase:
		if v == nil {
			err := WorkerReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInterfaceCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "InterfaceCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "InterfaceCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInterfaceCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkerReqValidationError{
					field:  "InterfaceCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WorkerReq_UiCase:
		if v == nil {
			err := WorkerReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "UiCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "UiCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkerReqValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WorkerReq_PerfCase:
		if v == nil {
			err := WorkerReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPerfCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "PerfCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "PerfCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPerfCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkerReqValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WorkerReq_StabilityCase:
		if v == nil {
			err := WorkerReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStabilityCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "StabilityCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "StabilityCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStabilityCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkerReqValidationError{
					field:  "StabilityCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WorkerReq_StabilityPlan:
		if v == nil {
			err := WorkerReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStabilityPlan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "StabilityPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkerReqValidationError{
						field:  "StabilityPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStabilityPlan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkerReqValidationError{
					field:  "StabilityPlan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return WorkerReqMultiError(errors)
	}

	return nil
}

// WorkerReqMultiError is an error wrapping multiple validation errors returned
// by WorkerReq.ValidateAll() if the designated constraints aren't met.
type WorkerReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WorkerReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WorkerReqMultiError) AllErrors() []error { return m }

// WorkerReqValidationError is the validation error returned by
// WorkerReq.Validate if the designated constraints aren't met.
type WorkerReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WorkerReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WorkerReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WorkerReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WorkerReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WorkerReqValidationError) ErrorName() string { return "WorkerReqValidationError" }

// Error satisfies the builtin error interface
func (e WorkerReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWorkerReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WorkerReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WorkerReqValidationError{}

// Validate checks the field values on ComponentGroupWorkerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ComponentGroupWorkerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComponentGroupWorkerInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComponentGroupWorkerInfoMultiError, or nil if none found.
func (m *ComponentGroupWorkerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ComponentGroupWorkerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentGroupId

	// no validation rules for ComponentGroupExecuteId

	// no validation rules for Version

	if len(errors) > 0 {
		return ComponentGroupWorkerInfoMultiError(errors)
	}

	return nil
}

// ComponentGroupWorkerInfoMultiError is an error wrapping multiple validation
// errors returned by ComponentGroupWorkerInfo.ValidateAll() if the designated
// constraints aren't met.
type ComponentGroupWorkerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComponentGroupWorkerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComponentGroupWorkerInfoMultiError) AllErrors() []error { return m }

// ComponentGroupWorkerInfoValidationError is the validation error returned by
// ComponentGroupWorkerInfo.Validate if the designated constraints aren't met.
type ComponentGroupWorkerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComponentGroupWorkerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComponentGroupWorkerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComponentGroupWorkerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComponentGroupWorkerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComponentGroupWorkerInfoValidationError) ErrorName() string {
	return "ComponentGroupWorkerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ComponentGroupWorkerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComponentGroupWorkerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComponentGroupWorkerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComponentGroupWorkerInfoValidationError{}

// Validate checks the field values on CaseWorkerInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CaseWorkerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseWorkerInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CaseWorkerInfoMultiError,
// or nil if none found.
func (m *CaseWorkerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseWorkerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseId

	// no validation rules for CaseExecuteId

	// no validation rules for SuiteId

	// no validation rules for SuiteExecuteId

	// no validation rules for Version

	// no validation rules for SuiteName

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for PlanName

	if len(errors) > 0 {
		return CaseWorkerInfoMultiError(errors)
	}

	return nil
}

// CaseWorkerInfoMultiError is an error wrapping multiple validation errors
// returned by CaseWorkerInfo.ValidateAll() if the designated constraints
// aren't met.
type CaseWorkerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseWorkerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseWorkerInfoMultiError) AllErrors() []error { return m }

// CaseWorkerInfoValidationError is the validation error returned by
// CaseWorkerInfo.Validate if the designated constraints aren't met.
type CaseWorkerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseWorkerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseWorkerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseWorkerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseWorkerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseWorkerInfoValidationError) ErrorName() string { return "CaseWorkerInfoValidationError" }

// Error satisfies the builtin error interface
func (e CaseWorkerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseWorkerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseWorkerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseWorkerInfoValidationError{}

// Validate checks the field values on InterfaceCaseWorkerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceCaseWorkerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceCaseWorkerInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceCaseWorkerInfoMultiError, or nil if none found.
func (m *InterfaceCaseWorkerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceCaseWorkerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InterfaceCaseId

	// no validation rules for InterfaceCaseExecuteId

	// no validation rules for InterfaceId

	// no validation rules for InterfaceExecuteId

	// no validation rules for Version

	// no validation rules for DocumentId

	// no validation rules for DocumentName

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for PlanName

	if len(errors) > 0 {
		return InterfaceCaseWorkerInfoMultiError(errors)
	}

	return nil
}

// InterfaceCaseWorkerInfoMultiError is an error wrapping multiple validation
// errors returned by InterfaceCaseWorkerInfo.ValidateAll() if the designated
// constraints aren't met.
type InterfaceCaseWorkerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceCaseWorkerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceCaseWorkerInfoMultiError) AllErrors() []error { return m }

// InterfaceCaseWorkerInfoValidationError is the validation error returned by
// InterfaceCaseWorkerInfo.Validate if the designated constraints aren't met.
type InterfaceCaseWorkerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceCaseWorkerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceCaseWorkerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceCaseWorkerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceCaseWorkerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceCaseWorkerInfoValidationError) ErrorName() string {
	return "InterfaceCaseWorkerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceCaseWorkerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceCaseWorkerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceCaseWorkerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceCaseWorkerInfoValidationError{}

// Validate checks the field values on PlanMonitorReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PlanMonitorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlanMonitorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PlanMonitorReqMultiError,
// or nil if none found.
func (m *PlanMonitorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PlanMonitorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CallbackUrl

	// no validation rules for Timeout

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for PlanExecuteId

	if all {
		switch v := interface{}(m.GetTestInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlanMonitorReqValidationError{
					field:  "TestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlanMonitorReqValidationError{
					field:  "TestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTestInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlanMonitorReqValidationError{
				field:  "TestInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PlanMonitorReqMultiError(errors)
	}

	return nil
}

// PlanMonitorReqMultiError is an error wrapping multiple validation errors
// returned by PlanMonitorReq.ValidateAll() if the designated constraints
// aren't met.
type PlanMonitorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlanMonitorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlanMonitorReqMultiError) AllErrors() []error { return m }

// PlanMonitorReqValidationError is the validation error returned by
// PlanMonitorReq.Validate if the designated constraints aren't met.
type PlanMonitorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlanMonitorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlanMonitorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlanMonitorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlanMonitorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlanMonitorReqValidationError) ErrorName() string { return "PlanMonitorReqValidationError" }

// Error satisfies the builtin error interface
func (e PlanMonitorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlanMonitorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlanMonitorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlanMonitorReqValidationError{}

// Validate checks the field values on UICaseWorkerInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UICaseWorkerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UICaseWorkerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UICaseWorkerInfoMultiError, or nil if none found.
func (m *UICaseWorkerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UICaseWorkerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UiCaseId

	// no validation rules for UiCaseExecuteId

	// no validation rules for UiSuiteId

	// no validation rules for UiSuiteExecuteId

	// no validation rules for UiPlanId

	// no validation rules for UiPlanExecuteId

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UICaseWorkerInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UICaseWorkerInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UICaseWorkerInfoValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UICaseWorkerInfoMultiError(errors)
	}

	return nil
}

// UICaseWorkerInfoMultiError is an error wrapping multiple validation errors
// returned by UICaseWorkerInfo.ValidateAll() if the designated constraints
// aren't met.
type UICaseWorkerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UICaseWorkerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UICaseWorkerInfoMultiError) AllErrors() []error { return m }

// UICaseWorkerInfoValidationError is the validation error returned by
// UICaseWorkerInfo.Validate if the designated constraints aren't met.
type UICaseWorkerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UICaseWorkerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UICaseWorkerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UICaseWorkerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UICaseWorkerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UICaseWorkerInfoValidationError) ErrorName() string { return "UICaseWorkerInfoValidationError" }

// Error satisfies the builtin error interface
func (e UICaseWorkerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUICaseWorkerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UICaseWorkerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UICaseWorkerInfoValidationError{}

// Validate checks the field values on PerfCaseWorkerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfCaseWorkerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseWorkerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCaseWorkerInfoMultiError, or nil if none found.
func (m *PerfCaseWorkerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseWorkerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerfCaseId

	// no validation rules for PerfCaseExecuteId

	// no validation rules for PerfSuiteId

	// no validation rules for PerfSuiteExecuteId

	// no validation rules for PerfPlanId

	// no validation rules for PerfPlanExecuteId

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseWorkerInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseWorkerInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseWorkerInfoValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPerfPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseWorkerInfoValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseWorkerInfoValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseWorkerInfoValidationError{
				field:  "PerfPlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfCaseWorkerInfoMultiError(errors)
	}

	return nil
}

// PerfCaseWorkerInfoMultiError is an error wrapping multiple validation errors
// returned by PerfCaseWorkerInfo.ValidateAll() if the designated constraints
// aren't met.
type PerfCaseWorkerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseWorkerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseWorkerInfoMultiError) AllErrors() []error { return m }

// PerfCaseWorkerInfoValidationError is the validation error returned by
// PerfCaseWorkerInfo.Validate if the designated constraints aren't met.
type PerfCaseWorkerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseWorkerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseWorkerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseWorkerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseWorkerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseWorkerInfoValidationError) ErrorName() string {
	return "PerfCaseWorkerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PerfCaseWorkerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseWorkerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseWorkerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseWorkerInfoValidationError{}

// Validate checks the field values on StabilityCaseWorkerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityCaseWorkerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityCaseWorkerInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityCaseWorkerInfoMultiError, or nil if none found.
func (m *StabilityCaseWorkerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityCaseWorkerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StabilityCaseId

	// no validation rules for StabilityCaseExecuteId

	// no validation rules for StabilityPlanId

	// no validation rules for StabilityPlanExecuteId

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityCaseWorkerInfoValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityCaseWorkerInfoValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityCaseWorkerInfoValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceType

	// no validation rules for PlatformType

	// no validation rules for Udid

	// no validation rules for PackageName

	// no validation rules for AppDownloadLink

	if all {
		switch v := interface{}(m.GetCustomScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityCaseWorkerInfoValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityCaseWorkerInfoValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityCaseWorkerInfoValidationError{
				field:  "CustomScript",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Duration

	if len(errors) > 0 {
		return StabilityCaseWorkerInfoMultiError(errors)
	}

	return nil
}

// StabilityCaseWorkerInfoMultiError is an error wrapping multiple validation
// errors returned by StabilityCaseWorkerInfo.ValidateAll() if the designated
// constraints aren't met.
type StabilityCaseWorkerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityCaseWorkerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityCaseWorkerInfoMultiError) AllErrors() []error { return m }

// StabilityCaseWorkerInfoValidationError is the validation error returned by
// StabilityCaseWorkerInfo.Validate if the designated constraints aren't met.
type StabilityCaseWorkerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityCaseWorkerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityCaseWorkerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityCaseWorkerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityCaseWorkerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityCaseWorkerInfoValidationError) ErrorName() string {
	return "StabilityCaseWorkerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityCaseWorkerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityCaseWorkerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityCaseWorkerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityCaseWorkerInfoValidationError{}

// Validate checks the field values on StabilityPlanWorkerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityPlanWorkerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityPlanWorkerInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityPlanWorkerInfoMultiError, or nil if none found.
func (m *StabilityPlanWorkerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityPlanWorkerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StabilityPlanId

	// no validation rules for StabilityPlanExecuteId

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanWorkerInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanWorkerInfoValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanWorkerInfoValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StabilityPlanWorkerInfoMultiError(errors)
	}

	return nil
}

// StabilityPlanWorkerInfoMultiError is an error wrapping multiple validation
// errors returned by StabilityPlanWorkerInfo.ValidateAll() if the designated
// constraints aren't met.
type StabilityPlanWorkerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityPlanWorkerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityPlanWorkerInfoMultiError) AllErrors() []error { return m }

// StabilityPlanWorkerInfoValidationError is the validation error returned by
// StabilityPlanWorkerInfo.Validate if the designated constraints aren't met.
type StabilityPlanWorkerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityPlanWorkerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityPlanWorkerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityPlanWorkerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityPlanWorkerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityPlanWorkerInfoValidationError) ErrorName() string {
	return "StabilityPlanWorkerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityPlanWorkerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityPlanWorkerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityPlanWorkerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityPlanWorkerInfoValidationError{}

// Validate checks the field values on PlanMonitorReq_TestInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PlanMonitorReq_TestInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlanMonitorReq_TestInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlanMonitorReq_TestInfoMultiError, or nil if none found.
func (m *PlanMonitorReq_TestInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PlanMonitorReq_TestInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PlanMonitorReq_TestInfoMultiError(errors)
	}

	return nil
}

// PlanMonitorReq_TestInfoMultiError is an error wrapping multiple validation
// errors returned by PlanMonitorReq_TestInfo.ValidateAll() if the designated
// constraints aren't met.
type PlanMonitorReq_TestInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlanMonitorReq_TestInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlanMonitorReq_TestInfoMultiError) AllErrors() []error { return m }

// PlanMonitorReq_TestInfoValidationError is the validation error returned by
// PlanMonitorReq_TestInfo.Validate if the designated constraints aren't met.
type PlanMonitorReq_TestInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlanMonitorReq_TestInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlanMonitorReq_TestInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlanMonitorReq_TestInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlanMonitorReq_TestInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlanMonitorReq_TestInfoValidationError) ErrorName() string {
	return "PlanMonitorReq_TestInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PlanMonitorReq_TestInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlanMonitorReq_TestInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlanMonitorReq_TestInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlanMonitorReq_TestInfoValidationError{}
