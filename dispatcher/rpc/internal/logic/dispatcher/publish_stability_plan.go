package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

type StabilityPlanPublisher struct {
	*BasePublisher

	apiExecutionData *managerpb.ApiExecutionData
}

func NewStabilityPlanPublisher(ctx context.Context, svcCtx *svc.ServiceContext) *StabilityPlanPublisher {
	return &StabilityPlanPublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *StabilityPlanPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	mgrData, err := l.handleMgrData(in)
	if err != nil {
		return errors.Wrapf(
			err, "请求handleMgrData rpc服务出现错误, error: %s", err,
		)
	}
	l.apiExecutionData = mgrData
	stabilityPlan := mgrData.GetStabilityPlan()

	var totalDevice uint32
	if len(stabilityPlan.GetMetaData().GetDevices().GetUdids().GetValues()) > 0 {
		totalDevice = uint32(len(stabilityPlan.GetMetaData().GetDevices().GetUdids().GetValues()))
	} else {
		totalDevice = stabilityPlan.GetMetaData().GetDevices().GetCount()
	}

	_, err = l.svcCtx.StabilityReporterRpc.CreateStabilityPlanRecord(
		l.ctx, &reporterpb.PutStabilityPlanRecordReq{
			ProjectId:      in.GetProjectId(),
			PlanId:         stabilityPlan.GetPlanId(),
			PlanName:       stabilityPlan.GetName(),
			TriggerMode:    in.GetTriggerMode().String(),
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			Status:         pb.ComponentState_Pending.String(),
			TargetDuration: stabilityPlan.GetMetaData().GetDuration(),
			ExecuteData:    protobuf.MarshalJSONToStringIgnoreError(stabilityPlan),
			ExecutedBy:     in.GetUserId(),
			TotalDevice:    totalDevice,
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *StabilityPlanPublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *StabilityPlanPublisher) handleMgrData(in *pb.PublishReq) (*managerpb.ApiExecutionData, error) {
	mgrData, err := l.getApiExecutionData(
		&managerpb.GetApiExecutionDataReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_STABILITY_PLAN,
			Id:        in.GetStabilityPlan().GetStabilityPlanId(),
		},
	)
	if err != nil {
		return nil, err
	}

	if in.GetTriggerMode() == commonpb.TriggerMode_SCHEDULE {
		if mgrData.GetStabilityPlan().GetMaintainedBy() != "" {
			in.UserId = mgrData.GetStabilityPlan().GetMaintainedBy()
		} else {
			in.UserId = mgrData.GetStabilityPlan().GetCreatedBy()
		}
	}

	return mgrData, nil
}

func (l *StabilityPlanPublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	mgrData := l.apiExecutionData
	if mgrData == nil {
		mgrData, err = l.handleMgrData(in)
		if err != nil {
			return resp, err
		}
	}

	_, err = l.record(in, mgrData)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(mgrData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	taskData := l.getWorkerReq(in, mgrData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "稳测任务参数序列化失败, error: %s", err,
		)
	}

	duration := mgrData.GetStabilityPlan().GetMetaData().GetDuration()
	timeout := time.Duration(duration) * time.Minute
	task := base.NewTask(
		constants.MQTaskTypeStabilityWorkerExecutePlanTask, payload,
		base.WithMaxRetryOptions(0),
		base.WithTimeoutOptions(timeout+time.Hour),
		base.WithRetentionOptions(timeout+2*time.Hour),
	)

	_, err = l.svcCtx.StabilityWorkerProducer.Send(
		l.ctx, task, mq.ConvertPbEnumerationToQueuePriority(taskData.GetPriorityType()),
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *StabilityPlanPublisher) getWorkerReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.WorkerReq {
	stabilityPlan := data.GetStabilityPlan()
	req := &pb.WorkerReq{
		TriggerMode: in.GetTriggerMode(),
		TriggerRule: in.GetTriggerRule(),
		ProjectId:   in.GetProjectId(),
		TaskId:      l.taskId,
		ExecuteId:   l.executeId,
		ExecuteType: in.GetExecuteType(),
		WorkerType:  pb.WorkerType_WorkerType_STABILITY_PLAN,
		AccountConfig: []*commonpb.AccountConfig{
			stabilityPlan.MetaData.AccountConfig,
		},
		User:         in.GetUserId(),
		Debug:        in.GetDebug(),
		PriorityType: l.priorityType,
		Data: &pb.WorkerReq_StabilityPlan{
			StabilityPlan: &pb.StabilityPlanWorkerInfo{
				StabilityPlanId:        stabilityPlan.GetPlanId(),
				StabilityPlanExecuteId: l.executeId,
				MetaData:               stabilityPlan.GetMetaData(),
			},
		},
	}
	return req
}

func (l *StabilityPlanPublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyStabilityPlanRecordResp, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	if status == pb.ComponentState_Pending {
		return
	}

	resp, err = l.svcCtx.StabilityReporterRpc.ModifyStabilityPlanRecord(
		l.ctx, &reporterpb.PutStabilityPlanRecordReq{
			ProjectId: in.GetProjectId(),
			TaskId:    l.taskId,
			ExecuteId: l.executeId,
			Status:    status.String(),
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return
}

// IsValid 是否有效
func (l *StabilityPlanPublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	return data.GetUiPlan().GetState() != managerpb.CommonState_CS_DISABLE
}

// Panic 异常处理
func (l *StabilityPlanPublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}
	_, _ = l.svcCtx.StabilityReporterRpc.ModifyStabilityPlanRecord(
		l.ctx, &reporterpb.PutStabilityPlanRecordReq{
			ProjectId: in.GetProjectId(),
			TaskId:    l.taskId,
			ExecuteId: l.executeId,
			Status:    pb.ComponentState_Panic.String(),
		},
	)
}
