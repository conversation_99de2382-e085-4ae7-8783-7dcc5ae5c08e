package svc

import (
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/config"

	rediscommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis/task"
)

type ServiceContext struct {
	Config config.Config

	DB    sqlx.SqlConn
	Redis red.UniversalClient

	TaskInfoRecordModel model.TaskInfoRecordModel

	ManagerRpc           *zrpc.ManagerRpc
	ReporterRpc          *zrpc.ReporterRpc
	UIReporterRpc        *zrpc.UIReporterRpc
	PerfReporterRpc      *zrpc.PerfReporterRpc
	StabilityReporterRpc *zrpc.StabilityReporterRpc
	UserRpc              *zrpc.UserRpc

	ApiWorkerProducer       *producer.Producer
	PerfWorkerProducer      *producer.Producer
	UIWorkerProducer        *producer.Producer
	DispatcherProducer      *producer.Producer
	TaskConsumer            *consumer.Consumer
	BeatConsumer            *consumer.Consumer
	WorkerProducer          *producer.Producer
	StabilityWorkerProducer *producer.Producer

	RCacheService     *rediscommon.RCacheService
	TaskInfoProcessor *task.InfoProcessor
}

func NewServiceContext(c config.Config) *ServiceContext {
	cacheService := rediscommon.NewRCacheService(c.RedisConf, rediscommon.DispatcherRedisKey)
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	return &ServiceContext{
		Config: c,

		Redis: redis.NewClient(c.RedisConf),

		TaskInfoRecordModel: model.NewTaskInfoRecordModel(sqlConn, c.Cache),

		ManagerRpc:           zrpc.NewManagerRpc(c.Manager),
		ReporterRpc:          zrpc.NewReporterRpc(c.Reporter),
		UIReporterRpc:        zrpc.NewUIReporterRpc(c.Reporter),
		PerfReporterRpc:      zrpc.NewPerfReporterRpc(c.Reporter),
		StabilityReporterRpc: zrpc.NewStabilityReporterRpc(c.Reporter),
		UserRpc:              zrpc.NewUserRpc(c.User),

		ApiWorkerProducer:       producer.NewProducer(c.ApiWorkerProducer),
		UIWorkerProducer:        producer.NewProducer(c.UIWorkerProducer),
		PerfWorkerProducer:      producer.NewProducer(c.PerfWorkerProducer),
		DispatcherProducer:      producer.NewProducer(c.DispatcherProducer),
		TaskConsumer:            consumer.NewConsumer(c.TaskConsumer),
		BeatConsumer:            consumer.NewConsumer(c.BeatConsumer),
		WorkerProducer:          producer.NewProducer(c.WorkerProducer),
		StabilityWorkerProducer: producer.NewProducer(c.StabilityWorkerProducer),

		RCacheService:     cacheService,
		TaskInfoProcessor: task.NewTaskInfoProcessor(cacheService),
	}
}
