{
    "config": {
        "update_multi": true
    },
    "i18n_elements": {
        "zh_cn": [
            {
                "tag": "column_set",
                "horizontal_spacing": "16px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": "**计划名称：**{{ if .ModifyPlanURL }}{{ .ModifyPlanURL }}{{ else }}{{ .PlanName }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "livestream-content_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**APP版本：**{{ if .AppVersion }}{{ .AppVerHerf }}{{ else }}{{ .AppVersion }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "version_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**触发模式：**{{ .TriggerMode }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cursor_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**开始时间：**{{ .StartedAt.Format "2006-01-02 15:04:05" }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar-date_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**结束时间：**{{ .EndedAt.Format "2006-01-02 15:04:05" }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar-date_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**执行时长：**{{ .Duration }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "time_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**成功设备数：**{{ .SuccessDevice }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cellphone_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**测试设备数：**{{ .TotalDevice }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "phone_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**状态：**<font color='{{ .Status.Color }}'>{{ .Status.StatusZH }}</font>",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "status-vacation_outlined",
                                    "color": "black"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**执行人：**{{ if .Executor.LarkUserId }}<person id = '{{ .Executor.LarkUserId }}' show_name = true show_avatar = true style = 'normal'></person>{{ else if .Executor.Fullname }}{{ .Executor.Fullname }}{{ else }}{{ .Executor.Account }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "member_outlined",
                                    "color": "grey"
                                }
                            }
                        ],
                        "vertical_align": "top",
                        "vertical_spacing": "8px",
                        "weight": 1
                    }
                ]
            },
            {{- if .ExceptionDevices }}
            {
                "tag": "hr"
            },
            {
                "tag": "markdown",
                "content": "**异常设备列表：**",
                "text_align": "left",
                "text_size": "normal",
                "icon": {
                    "tag": "standard_icon",
                    "token": "table-group_outlined",
                    "color": "grey"
                }
            },
            {
                "tag": "table",
                "columns": [
                    {
                        "data_type": "text",
                        "name": "udid",
                        "display_name": "设备编号",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {
                        "data_type": "text",
                        "name": "name",
                        "display_name": "设备名称",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {
                        "data_type": "number",
                        "name": "crash_count",
                        "display_name": "Crash数量",
                        "horizontal_align": "right",
                        "width": "auto",
                        "format": {
                            "precision": 0
                        }
                    },
                    {
                        "data_type": "number",
                        "name": "anr_count",
                        "display_name": "ANR数量",
                        "horizontal_align": "right",
                        "width": "auto",
                        "format": {
                            "precision": 0
                        }
                    }
                ],
                "rows": {{ .ExceptionDevices }},
                "row_height": "low",
                "header_style": {
                    "background_style": "none",
                    "bold": true,
                    "lines": 1
                },
                "page_size": 5
            },
            {{- end }}
            {
                "tag": "hr"
            },
            {
                "tag": "action",
                "actions": [
                    {
                        "tag": "button",
                        "text": {
                            "tag": "plain_text",
                            "content": "详细报告"
                        },
                        "type": "primary",
                        "width": "default",
                        "size": "medium",
                        "icon": {
                            "tag": "standard_icon",
                            "token": "lookup_outlined"
                        },
                        "behaviors": [
                            {
                                "type": "open_url",
                                "default_url": "{{ .ReportURL }}",
                                "pc_url": "",
                                "ios_url": "",
                                "android_url": ""
                            }
                        ]
                    }
                ]
            }
        ]
    },
    "i18n_header": {
        "zh_cn": {
            "title": {
                "tag": "plain_text",
                "content": "稳定性测试报告"
            },
            "subtitle": {
                "tag": "plain_text",
                "content": ""
            },
            "template": "{{ .Status.Color }}",
            "ud_icon": {
                "tag": "standard_icon",
                "token": "livestream-ing_filled"
            }
        }
    }
}