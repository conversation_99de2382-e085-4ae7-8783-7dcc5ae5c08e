package stabilityReport

const (
	textLinkFormat      = "[%s](%s)"
	modifyPlanURLFormat = "%s/#/stability-test/testplan/edit?project_id=%s&from_router=projectIndex&stability_plan_id=%s&mode=edit" // base_url, project_id, plan_id
	reportURLFormat     = "%s/#/stability-test/testplan/report?plan_id=%s&task_id=%s&project_id=%s&execute_id=%s"                   // base_url, plan_id, task_id, project_id, execute_id
)

const (
	statusOfPending = "Pending"
	statusOfInit    = "Init"
	statusOfStarted = "Started"
	statusOfSuccess = "Success"
	statusOfWaiting = "Waiting"
	statusOfWarning = "Warning"
	statusOfSkip    = "Skip"
	statusOfFailure = "Failure"
	statusOfPanic   = "Panic"
	statusOfStop    = "Stop"
	statusOfInvalid = "Invalid"
)

type statusZH string

const (
	pending statusZH = "待处理"
	initial statusZH = "初始化中"
	started statusZH = "开始处理"
	success statusZH = "成功"
	waiting statusZH = "等待中"
	warning statusZH = "警告"
	skipped statusZH = "跳过"
	failure statusZH = "失败"
	crash   statusZH = "崩溃"
	stopped statusZH = "终止"
	invalid statusZH = "无效"
	unknown statusZH = "未知"
)

type color string

const (
	blue   color = "blue"   // 蓝色
	green  color = "green"  // 绿色
	grey   color = "grey"   // 灰色
	red    color = "red"    // 红色
	yellow color = "yellow" // 黄色
)
