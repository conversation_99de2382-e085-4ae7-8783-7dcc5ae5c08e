package common

import (
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
)

const (
	ConstMaxRecvSize = 20 * 1024 * 1024

	ConstMRMaxWorkers     = 16
	ConstWalkDirMakeFiles = 64

	ConstLockExpireTime                               = 5 * time.Second
	ConstAcquireLockTimeout                           = 5 * time.Second
	ConstImportInterfaceDefinitionTimeout             = 570 * time.Second
	ConstModifyApiCaseTimeout                         = 10 * time.Second
	ConstUICaseTimeout                                = 10 * time.Second
	ConstGetCategoryTreeTimeout                       = 10 * time.Second
	ConstLockImportInterfaceDefinitionExpireTime      = ConstImportInterfaceDefinitionTimeout
	ConstLockHandleParsePythonProjectResultExpireTime = 10 * time.Second

	ConstDomainSourceProbe = "probe" // `permission`服务用到的领域源名称（即项目名称）

	ConstCaseFailStatNDays        = 7
	ConstCheckScheduleTimes       = 10
	ConstCheckScheduleMinInterval = time.Hour
	ConstGetMetricsNWeekends      = 4
	ConstGetMetricsDateHour       = 20 // [0, 23]
	ConstGetMetricsDuration       = 3
	ConstAdvancedNotificationDays = 1

	ConstLockProjectProjectIdPrefix                           = "lock:project:projectId"                             // `project`
	ConstLockCategoryProjectIdTypeCategoryIdPrefix            = "lock:category:projectId:type:categoryId"            // `category`
	ConstLockCategoryTreeProjectIdTypePrefix                  = "lock:categoryTree:projectId:type"                   // `category_tree`
	ConstLockTagProjectIdTagIdPrefix                          = "lock:tag:projectId:tagId"                           // `tag`
	ConstLockTagProjectIdTypeNamePrefix                       = "lock:tag:projectId:type:name"                       // `tag`
	ConstLockFunctionProjectIdNameTypePrefix                  = "lock:function:projectId:name:type"                  // `function`
	ConstLockGeneralConfigProjectIdConfigIdPrefix             = "lock:generalConfig:projectId:configId"              // `general_configuration`
	ConstLockAccountConfigProjectIdConfigIdPrefix             = "lock:accountConfig:projectId:configId"              // `account_configuration`
	ConstLockInterfaceDocumentProjectIdDocumentIdPrefix       = "lock:interfaceDocument:projectId:documentId"        // `interface_document`
	ConstLockInterfaceSchemaProjectIdSchemaIdPrefix           = "lock:interfaceSchema:projectId:schemaId"            // `interface_schema`
	ConstLockInterfaceConfigProjectIdDocumentIdConfigIdPrefix = "lock:interfaceConfig:projectId:documentId:configId" // `interface_configuration`
	ConstLockInterfaceCaseProjectIdDocumentIdCaseIdPrefix     = "lock:interfaceCase:projectId:documentId:caseId"     // `interface_case`
	ConstLockComponentGroupProjectIdComponentGroupIdPrefix    = "lock:componentGroup:projectId:componentGroupId"     // `component_group`
	ConstLockApiCaseProjectIdCaseIdPrefix                     = "lock:apiCase:projectId:caseId"                      // `api_case`
	ConstLockApiSuiteProjectIdSuiteIdPrefix                   = "lock:apiSuite:projectId:suiteId"                    // `api_suite`
	ConstLockApiPlanProjectIdPlanIdPrefix                     = "lock:apiPlan:projectId:planId"                      // `api_plan`
	ConstLockNotifyProjectIDNotifyIdPrefix                    = "lock:notify:projectId:notifyId"                     // `notify`
	ConstLockNotifyProjectIDPlanIdPrefix                      = "lock:notify:projectId:planId"                       // `notify`
	ConstLockGitConfigProjectIDConfigIDPrefix                 = "lock:gitConfig:projectID:configID"                  // `git_configuration`
	ConstLockUiPlanProjectIdPlanIdPrefix                      = "lock:uiPlan:projectId:planId"                       // `ui_plan`
	ConstLockReviewRecordProjectIdResourceIdPrefix            = "lock:reviewRecord:projectId:resourceId"             // `review_record`
	ConstLockReviewRecordProjectIdReviewIdPrefix              = "lock:reviewRecord:projectId:reviewId"               // `review_record`
	ConstLockProtobufConfigProjectIDConfigIDPrefix            = "lock:protobufConfig:projectID:configID"             // `protobuf_configuration`
	ConstLockPerfDataProjectIDDataIDPrefix                    = "lock:perfData:projectID:dataID"                     // `perf_data`
	ConstLockPerfCaseProjectIDCaseIDPrefix                    = "lock:perfCase:projectID:caseID"                     // `perf_case`
	ConstLockPerfPlanProjectIDPlanIDPrefix                    = "lock:perfPlan:projectID:planID"                     // `perf_plan`
	ConstLockPerfStopRuleProjectIDMetricTypePrefix            = "lock:perfStopRule:projectID:metricType"             // `perf_stop_rule`
	ConstLockPerfStopRuleProjectIDRuleIDPrefix                = "lock:perfStopRule:projectID:ruleID"                 // `perf_stop_rule`
	ConstLockPerfLarkChatProjectIDPrefix                      = "lock:perfLarkChat:projectID"                        // `perf_lark_chat`
	ConstLockPerfLarkChatChatIDPrefix                         = "lock:perfLarkChat:chatID"
	ConstLockLarkChatChatIDPrefix                             = "lock:larkChat:chatID"
	ConstLockLarkChatProjectIDTypePrefix                      = "lock:larkChat:projectID:type"                // `lark_chat`
	ConstLockCaseFailStatProjectIDCaseTypeCaseIDPrefix        = "lock:caseFailStat:projectID:caseType:caseID" // `case_fail_stat`
	ConstLockProjectDeviceProjectID                           = "lock:projectDevice:projectID"                // `project_device`
	ConstLockStabilityPlanProjectIDPlanIDPrefix               = "lock:stabilityPlan:projectID:planID"         // `stability_plan`

	ConstLockImportInterfaceDefinitionProjectIDPrefix                  = "lock:importInterfaceDefinition:projectId"               // import interface definition
	ConstLockHandleParsePythonProjectResultTaskProjectIDConfigIDPrefix = "lock:handleParsePythonProjectResult:projectID:configID" // handle parse python project result
	ConstLockUpdateInterfaceCoverageProjectIDPrefix                    = "lock:updateInterfaceCoverage:projectID"                 // update interface coverage
	ConstLockUpdateInterfaceDocumentTagsProjectIDPrefix                = "lock:updateInterfaceDocumentTags:projectID"             // update interface document tags
	ConstLockClearAndUpdateFailedCase                                  = "lock:caseFailStat:deleteUpdate"
	ConstLockUpdateInterfaceDefinition                                 = "lock:updateInterfaceDefinition"
	ConstLockUpdateInterfaceCoverage                                   = "lock:updateInterfaceCoverage"
	ConstLockUpdateInterfaceMetricsReference                           = "lock:updateInterfaceMetricsReference"
	ConstLockUpdateAdvancedNotification                                = "lock:updateAdvancedNotification"
	ConstLockDeleteDisabledDevice                                      = "lock:deleteDisabledDevice"

	ConstExpireOfClearAndUpdateFailedCaseTask        = 5 * time.Minute
	ConstExpireOfUpdateInterfaceDefinitionTask       = 10 * time.Minute
	ConstExpireOfUpdateInterfaceCaseCoverageTask     = 5 * time.Minute
	ConstExpireOfUpdateInterfaceMetricsReferenceTask = 30 * time.Minute
	ConstExpireOfLarkChatDisbandedTask               = 30 * time.Second
	ConstExpireOfLarkChatUpdatedTask                 = 30 * time.Second
	ConstExpireOfLarkChatBotDeletedTask              = 30 * time.Second
	ConstExpireOfAdvancedNotificationTask            = 10 * time.Minute
	ConstExpireOfDeleteDisabledDeviceTask            = 10 * time.Minute

	ConstRPCAcquireProjectDeviceTimeout = 8 * time.Second
	ConstRPCReleaseProjectDeviceTimeout = 8 * time.Second
)

const (
	ConstBuiltinGlobalProjectId = utils.ConstProjectIdPrefix + "builtin-global-project"

	ConstTDSCommonCallReqSchemaId = utils.ConstInterfaceSchemaIdPrefix + "builtin-tds-common-call-req"
	ConstTDSSearchClientSchemaId  = utils.ConstInterfaceSchemaIdPrefix + "builtin-tds-search-client"
	ConstTDSFixDictSchemaId       = utils.ConstInterfaceSchemaIdPrefix + "builtin-tds-fix-dict"
	ConstTDSCommonRespSchemaId    = utils.ConstInterfaceSchemaIdPrefix + "builtin-tds-common-resp"
	ConstTDSErrorRespSchemaId     = utils.ConstInterfaceSchemaIdPrefix + "builtin-tds-error-resp"

	ConstTDSServiceName = "tds"

	ConstTDSCommonCallReqSchemaName = "CommonCallReq"
	ConstTDSSearchClientSchemaName  = "SearchClientForm"
	ConstTDSFixDictSchemaName       = "FixDictFieldForm"
	ConstTDSCommonRespSchemaName    = "CommonResp"
	ConstTDSErrorRespSchemaName     = "ErrorResp"

	ConstTTMetaServiceName = "ttmeta"

	ConstTTMetaCommonCallReqSchemaId      = utils.ConstInterfaceSchemaIdPrefix + "builtin-ttmeta-client-common-call-req"
	ConstTTMetaClientSearchClientSchemaId = utils.ConstInterfaceSchemaIdPrefix + "builtin-ttmeta-client-search-client"
	ConstTTMetaCommonRespSchemaId         = utils.ConstInterfaceSchemaIdPrefix + "builtin-ttmeta-client-common-resp"

	ConstTTMetaClientCommonCallReqSchemaName = "TTMetaClientCommonCallReq"
	ConstTTMetaClientSearchClientSchemaName  = "TTMetaClientSearchClientForm"
	ConstTTMetaClientFixDictSchemaName       = "TTMetaClientFixDictFieldForm"
	ConstTTMetaClientCommonRespSchemaName    = "TTMetaClientCommonResp"
	ConstTTMetaClientErrorRespSchemaName     = "TTMetaClientErrorResp"

	ConstApiProxyServiceName = "api-proxy"

	ConstApiProxyCommonApiCallReqSchemaName = "ApiProxyCommonApiCallReq"

	ConstApiProxyMethodSchemaName                 = "ApiProxyMethod"
	ConstApiProxyHeadersSchemaName                = "ApiProxyHeaders"
	ConstApiProxyCommonRespSchemaName             = "ApiProxyCommonResp"
	ConstApiProxyCommonRespDataSchemaName         = "ApiProxyCommonRespData"
	ConstApiProxyCommonRespDataCallRespSchemaName = "ApiProxyCommonRespDataCallResp"
	ConstApiProxyErrorRespSchemaName              = "ApiProxyErrorResp"

	ConstApiProxyCommonApiCallReqSchemaId       = utils.ConstInterfaceSchemaIdPrefix + "builtin-api-proxy-client-common-api-call-req"
	ConstApiProxyCommonRespSchemaId             = utils.ConstInterfaceSchemaIdPrefix + "builtin-api-proxy-common-resp"
	ConstApiProxyCommonRespDataSchemaId         = utils.ConstInterfaceSchemaIdPrefix + "builtin-api-proxy-common-resp-data"
	ConstApiProxyCommonRespDataCallRespSchemaId = utils.ConstInterfaceSchemaIdPrefix + "builtin-api-proxy-common-resp-data-call-resp"

	ConstFieldJSONNameReferenceId = "reference_id"
	ConstFieldJSONNameImports     = "imports"
	ConstFieldJSONNameExports     = "exports"
	ConstFieldJSONNameName        = "name"
	ConstFieldJSONNameDescription = "description"
	ConstFieldJSONNameLabel       = "label"

	ConstJsonSchemaRootTitle = "root"

	ConstFakeSuiteOfEnableCasesID    = "suite_of_enable_cases"
	ConstFakeSuiteOfEnableCasesName  = "存放有效用例的假集合"
	ConstFakeSuiteOfDisableCasesID   = "suite_of_disable_cases"
	ConstFakeSuiteOfDisableCasesName = "存放失效用例的假集合"
)

const (
	ConstTTAuthName      = "登录"
	ConstTTAuthMethod    = "ga.api.auth.AuthLogic.Auth"
	ConstTTAuthBody      = ""
	ConstTTAuthSleepTime = "5s"
	ConstTTAuthCmd       = 10
	ConstTTAuthGrpcPath  = "/ga.api.auth.AuthLogic/Auth"
)

// CategoryTreeType 分类树类型，注：由于 `model` 包也需要用到下列的常量，因此从 `logic` 包迁到这里
type CategoryTreeType = string

const (
	ConstCategoryTreeTypeInterfaceDocument CategoryTreeType = "INTERFACE_DOCUMENT" // 接口文档
	ConstCategoryTreeTypeInterfaceSchema   CategoryTreeType = "INTERFACE_SCHEMA"   // 数据模型
	ConstCategoryTreeTypeComponentGroup    CategoryTreeType = "COMPONENT_GROUP"    // 组件组
	ConstCategoryTreeTypeApiCase           CategoryTreeType = "API_CASE"           // API用例
	ConstCategoryTreeTypeApiSuite          CategoryTreeType = "API_SUITE"          // API集合
	ConstCategoryTreeTypeApiPlan           CategoryTreeType = "API_PLAN"           // API计划
	ConstCategoryTreeTypeUiPlan            CategoryTreeType = "UI_PLAN"            // UI计划
	ConstCategoryTreeTypePerfCase          CategoryTreeType = "PERF_CASE"          // 压测用例
	ConstCategoryTreeTypePerfPlan          CategoryTreeType = "PERF_PLAN"          // 压测计划
	ConstCategoryTreeTypeStabilityPlan     CategoryTreeType = "STABILITY_PLAN"     // 稳测计划
)

// CategoryType 分类类型，注：由于 `model` 包也需要用到下列的常量，因此从 `logic` 包迁到这里
type CategoryType = string

const (
	// ConstCategoryTypeDirectory 分类类型 - 目录
	ConstCategoryTypeDirectory CategoryType = "DIRECTORY"
	// ConstCategoryTypeFile 分类类型 - 文件（即叶子节点）
	ConstCategoryTypeFile CategoryType = "FILE"
)

// CategoryBuiltinName 分类内置名称，注：由于 `model` 包也需要用到下列的常量，因此从 `logic` 包迁到这里
type CategoryBuiltinName = string

const (
	ConstCategoryRootAllDocument               CategoryBuiltinName = "接口"
	ConstCategoryRootAllSchema                 CategoryBuiltinName = "全部数据模型"
	ConstCategorySubRootTDSService             CategoryBuiltinName = "TDS服务"
	ConstCategorySubRootTTMetaClient           CategoryBuiltinName = "T次元客户端"
	ConstCategorySubRootApiProxy               CategoryBuiltinName = "api-proxy服务"
	ConstCategoryRootAllComponentGroup         CategoryBuiltinName = "全部组件"
	ConstCategorySubRootBusinessComponent      CategoryBuiltinName = "业务组件"
	ConstCategorySubRootSetupTeardownComponent CategoryBuiltinName = "前后置组件"
	ConstCategoryRootAllApiCase                CategoryBuiltinName = "全部用例"
	ConstCategoryRootAllApiSuite               CategoryBuiltinName = "全部集合"
	ConstCategoryRootAllApiPlan                CategoryBuiltinName = "全部计划"
	ConstCategoryRootAllUiPlan                 CategoryBuiltinName = "全部计划"
	ConstCategoryRootAllPerfScenario           CategoryBuiltinName = "全部场景"
	ConstCategoryRootAllPerfPlan               CategoryBuiltinName = "全部计划"
	ConstCategoryRootAllStabilityPlan          CategoryBuiltinName = "全部计划"
)

// CategoryMoveType 分类移动类型
type CategoryMoveType = string

const (
	// ConstCategoryMoveTypeBefore 移到到指定分类的前面
	ConstCategoryMoveTypeBefore CategoryMoveType = "BEFORE"
	// ConstCategoryMoveTypeAfter 移到到指定分类的后面
	ConstCategoryMoveTypeAfter CategoryMoveType = "AFTER"
	// ConstCategoryMoveTypeInner 移到到指定分类的里面
	ConstCategoryMoveTypeInner CategoryMoveType = "INNER"
)

// TagType 标签类型
type TagType = string

const (
	ConstTagTypeComponentGroup    TagType = "COMPONENT_GROUP"
	ConstTagTypeCase              TagType = "CASE"
	ConstTagTypeSuite             TagType = "SUITE"
	ConstTagTypePlan              TagType = "PLAN"
	ConstTagTypeInterfaceDocument TagType = "INTERFACE_DOCUMENT"
)

// ComponentGroupType 组件组类型
type ComponentGroupType = string

const (
	// ConstComponentGroupTypeSingle 业务单请求组件组
	ConstComponentGroupTypeSingle ComponentGroupType = "SINGLE"
	// ConstComponentGroupTypeGroup 业务行为组组件组
	ConstComponentGroupTypeGroup ComponentGroupType = "GROUP"
	// ConstComponentGroupTypeSetup 前置组件组
	ConstComponentGroupTypeSetup ComponentGroupType = "SETUP"
	// ConstComponentGroupTypeTeardown 后置组件组
	ConstComponentGroupTypeTeardown ComponentGroupType = "TEARDOWN"
)

// InterfaceDefinitionType 接口定义类型
type InterfaceDefinitionType = string

const (
	ConstInterfaceDefinitionTypeDocument InterfaceDefinitionType = "DOCUMENT" // 接口文档
	ConstInterfaceDefinitionTypeSchema   InterfaceDefinitionType = "SCHEMA"   // 数据模型
)

// InterfaceType 接口类型
type InterfaceType = string

const (
	ConstInterfaceTypeHttp InterfaceType = "HTTP"
	ConstInterfaceTypeGrpc InterfaceType = "gRPC"
)

// InterfaceDefinitionCreateMode 接口定义创建方式
type InterfaceDefinitionCreateMode = string

const (
	ConstInterfaceDefinitionCreateModeBuiltin InterfaceDefinitionCreateMode = "builtin" // 通过程序内建
	ConstInterfaceDefinitionCreateModeLocal   InterfaceDefinitionCreateMode = "local"   // 通过本地目录或文件导入
	ConstInterfaceDefinitionCreateModeRemote  InterfaceDefinitionCreateMode = "remote"  // 通过前端页面导入
	ConstInterfaceDefinitionCreateModeManual  InterfaceDefinitionCreateMode = "manual"  // 通过前端页面创建
	ConstInterfaceDefinitionCreateModePlugin  InterfaceDefinitionCreateMode = "plugin"  // 通过插件导入
)

// InterfaceDefinitionImportType 接口定义导入类型
type InterfaceDefinitionImportType = string

const (
	ConstInterfaceDefinitionImportTypeOpenApi   InterfaceDefinitionImportType = "OpenApi"
	ConstInterfaceDefinitionImportTypeGrpc      InterfaceDefinitionImportType = "gRPC"
	ConstInterfaceDefinitionImportTypeProbe     InterfaceDefinitionImportType = "Probe"
	ConstInterfaceDefinitionImportTypeYApi      InterfaceDefinitionImportType = "YApi"
	ConstInterfaceDefinitionImportTypeTT        InterfaceDefinitionImportType = "TT"
	ConstInterfaceDefinitionImportTypeTTMeta    InterfaceDefinitionImportType = "TTMeta"
	ConstInterfaceDefinitionImportTypeRecommend InterfaceDefinitionImportType = "Recommend"
)

// InterfaceDefinitionLoadType 接口定义加载类型
type InterfaceDefinitionLoadType = string

const (
	InterfaceDefinitionLoadTypeError InterfaceDefinitionLoadType = ""
	InterfaceDefinitionLoadTypeGit   InterfaceDefinitionLoadType = "git"
	InterfaceDefinitionLoadTypeLocal InterfaceDefinitionLoadType = "local"
)

// InterfaceDefinitionStatus 接口状态
type InterfaceDefinitionStatus = int8

const (
	ConstInterfaceDefinitionStatusNone       InterfaceDefinitionStatus = iota
	ConstInterfaceDefinitionStatusDeveloping                           // 开发中
	ConstInterfaceDefinitionStatusTesting                              // 测试中
	ConstInterfaceDefinitionStatusReleased                             // 已发布
	ConstInterfaceDefinitionStatusDeprecated                           // 已废弃

	ConstInterfaceDefinitionStatusDesigning          // 设计中
	ConstInterfaceDefinitionStatusToBeConfirmed      // 待确定
	ConstInterfaceDefinitionStatusIntegrationTesting // 联调中
	ConstInterfaceDefinitionStatusTestingFinished    // 测试完
	ConstInterfaceDefinitionStatusException          // 有异常
)

// InterfaceDefinitionFieldType 接口定义字段类型
type InterfaceDefinitionFieldType = string

const (
	ConstInterfaceDefinitionFieldTypeString  InterfaceDefinitionFieldType = "string"
	ConstInterfaceDefinitionFieldTypeInteger InterfaceDefinitionFieldType = "integer"
	ConstInterfaceDefinitionFieldTypeNumber  InterfaceDefinitionFieldType = "number"
	ConstInterfaceDefinitionFieldTypeBoolean InterfaceDefinitionFieldType = "boolean"
	ConstInterfaceDefinitionFieldTypeArray   InterfaceDefinitionFieldType = "array"
	ConstInterfaceDefinitionFieldTypeObject  InterfaceDefinitionFieldType = "object"
	ConstInterfaceDefinitionFieldTypeNull    InterfaceDefinitionFieldType = "null"
	ConstInterfaceDefinitionFieldTypeAny     InterfaceDefinitionFieldType = "any"
	ConstInterfaceDefinitionFieldTypeAllOf   InterfaceDefinitionFieldType = "allOf"
	ConstInterfaceDefinitionFieldTypeAnyOf   InterfaceDefinitionFieldType = "anyOf"
	ConstInterfaceDefinitionFieldTypeOneOf   InterfaceDefinitionFieldType = "oneOf"
	ConstInterfaceDefinitionFieldTypeCustom  InterfaceDefinitionFieldType = "custom"
	ConstInterfaceDefinitionFieldTypeSchema  InterfaceDefinitionFieldType = "schema"
	ConstInterfaceDefinitionFieldTypeMap     InterfaceDefinitionFieldType = "map"
	ConstInterfaceDefinitionFieldTypeFile    InterfaceDefinitionFieldType = "file"
)

// InterfaceDefinitionFieldFormat 接口定义字段值格式
type InterfaceDefinitionFieldFormat = string

const (
	ConstInterfaceDefinitionFieldFormatInt32    InterfaceDefinitionFieldFormat = "int32"
	ConstInterfaceDefinitionFieldFormatInt64    InterfaceDefinitionFieldFormat = "int64"
	ConstInterfaceDefinitionFieldFormatFloat    InterfaceDefinitionFieldFormat = "float"
	ConstInterfaceDefinitionFieldFormatDouble   InterfaceDefinitionFieldFormat = "double"
	ConstInterfaceDefinitionFieldFormatPassword InterfaceDefinitionFieldFormat = "password"
	ConstInterfaceDefinitionFieldFormatDateTime InterfaceDefinitionFieldFormat = "date-time"
	ConstInterfaceDefinitionFieldFormatDate     InterfaceDefinitionFieldFormat = "date"
	ConstInterfaceDefinitionFieldFormatTime     InterfaceDefinitionFieldFormat = "time"
	ConstInterfaceDefinitionFieldFormatDuration InterfaceDefinitionFieldFormat = "duration"
	ConstInterfaceDefinitionFieldFormatEmail    InterfaceDefinitionFieldFormat = "email"
	ConstInterfaceDefinitionFieldFormatHostname InterfaceDefinitionFieldFormat = "hostname"
	ConstInterfaceDefinitionFieldFormatIpv4     InterfaceDefinitionFieldFormat = "ipv4"
	ConstInterfaceDefinitionFieldFormatIpv6     InterfaceDefinitionFieldFormat = "ipv6"
	ConstInterfaceDefinitionFieldFormatUri      InterfaceDefinitionFieldFormat = "uri"
	ConstInterfaceDefinitionFieldFormatRegex    InterfaceDefinitionFieldFormat = "regex"
)

// MediaType MIME类型
type MediaType = string

const (
	ConstMediaTypeFormData               MediaType = "multipart/form-data"
	ConstMediaTypeUrlEncoded             MediaType = "x-www-form-urlencoded"
	ConstMediaTypeApplicationJson        MediaType = "application/json"
	ConstMediaTypeTextPlain              MediaType = "text/plain"
	ConstMediaTypeTextCsv                MediaType = "text/csv"
	ConstMediaTypeTextVNDYaml            MediaType = "text/vnd.yaml"
	ConstMediaTypeApplicationOctetStream MediaType = "application/octet-stream"
)

type ReferenceType = string

const (
	ConstReferenceTypeComponentGroup    ReferenceType = "COMPONENT_GROUP"    // 组件组
	ConstReferenceTypeApiCase           ReferenceType = "API_CASE"           // API用例
	ConstReferenceTypeApiSuite          ReferenceType = "API_SUITE"          // API集合
	ConstReferenceTypeApiPlan           ReferenceType = "API_PLAN"           // API计划
	ConstReferenceTypeInterfaceDocument ReferenceType = "INTERFACE_DOCUMENT" // 接口文档
	ConstReferenceTypeService           ReferenceType = "API_SERVICE"        // 精准测试服务
	ConstReferenceTypeInterfaceCase     ReferenceType = "INTERFACE_CASE"     // 接口用例
	ConstReferenceTypePerfCase          ReferenceType = "PERF_CASE"          // 压测用例
	ConstReferenceTypePerfSuite         ReferenceType = "PERF_SUITE"         // 压测集合
	ConstReferenceTypePerfPlan          ReferenceType = "PERF_PLAN"          // 压测计划
	ConstReferenceTypeUIPlan            ReferenceType = "UI_PLAN"            // UI计划
	ConstReferenceTypeStabilityPlan     ReferenceType = "STABILITY_PLAN"     // 稳定性测试计划
)

// type PlanType = string
//
// const (
//	ConstPlanTypeManual    PlanType = "MANUAL"    // 手动触发
//	ConstPlanTypeSchedule  PlanType = "SCHEDULE"  // 定时触发
//	ConstPlanTypeInterface PlanType = "INTERFACE" // 接口触发
// )

// ListOperatorType 列表操作类型
type ListOperatorType = string

const (
	ConstListOperatorTypeAdd    ListOperatorType = "ADD"    // 添加
	ConstListOperatorTypeRemove ListOperatorType = "REMOVE" // 移除
)

// NodeType 节点类型（如：Python项目树结构节点类型）
type NodeType string

const (
	ConstNodeTypeDirectory NodeType = "DIRECTORY" // 目录，如：testcase/global_search
	ConstNodeTypeFile      NodeType = "FILE"      // 文件，如：testcase/global_search/folder_info.yaml
	ConstNodeTypePackage   NodeType = "PACKAGE"   // 包，如：testcase/global_search/__init__.py
	ConstNodeTypeModule    NodeType = "MODULE"    // 模块，如：testcase/global_search/test_global_search.py
	ConstNodeTypeClass     NodeType = "CLASS"     // 类，如：testcase/global_search/test_global_search.py::TestGlobalSearch
	ConstNodeTypeFunction  NodeType = "FUNCTION"  // 函数，如：testcase/global_search/test_global_search.py::TestGlobalSearch::test_global_search001_1
)

// GitType Git类型
type GitType = string

const (
	ConstGitTypeGitLab GitType = "gitlab"
	ConstGitTypeGitHub GitType = "github"
	ConstGitTypeGitee  GitType = "gitee"
)

// GitPurpose Git用途
type GitPurpose string

const (
	ConstGitPurposeAPI GitPurpose = "API" // API测试
	ConstGitPurposeUI  GitPurpose = "UI"  // UI测试
)

// ResourceState 资源状态
type ResourceState string

const (
	ConstResourceStateEnabled  ResourceState = "ENABLED"  // 生效
	ConstResourceStateDisabled ResourceState = "DISABLED" // 失效

	ConstResourceStateNew             ResourceState = "NEW"               // 新
	ConstResourceStateToBeImplemented ResourceState = "TO_BE_IMPLEMENTED" // 待实现
	ConstResourceStateToBeMaintained  ResourceState = "TO_BE_MAINTAINED"  // 待维护
	ConstResourceStatePendingReview   ResourceState = "PENDING_REVIEW"    // 待审核
	ConstResourceStatePublished       ResourceState = "PUBLISHED"         // 已上线
)

// ReviewResourceEvent 审核资源事件
type ReviewResourceEvent string

const (
	ConstReviewResourceEventAssignedToTheResponsiblePerson     ReviewResourceEvent = "AssignedToTheResponsiblePerson"     // 分配负责人（新 -> 待实现）
	ConstReviewResourceEventApplyForReviewAfterImplementation  ReviewResourceEvent = "ApplyForReviewAfterImplementation"  // 实现后申请审核（待实现 -> 待审核）
	ConstReviewResourceEventApplyForReviewAfterMaintenance     ReviewResourceEvent = "ApplyForReviewAfterMaintenance"     // 维护后申请审核（待维护 -> 待审核）
	ConstReviewResourceEventModifyTheReviewAfterImplementation ReviewResourceEvent = "ModifyTheReviewAfterImplementation" // 编辑实现后提交的申请（待审核 -> 待审核）
	ConstReviewResourceEventModifyTheReviewAfterMaintenance    ReviewResourceEvent = "ModifyTheReviewAfterMaintenance"    // 编辑维护后提交的申请（待审核 -> 待审核）
	ConstReviewResourceEventRevokeTheReviewAfterImplementation ReviewResourceEvent = "RevokeTheReviewAfterImplementation" // 撤回实现后提交的申请（待审核 -> 待实现）
	ConstReviewResourceEventRevokeTheReviewAfterMaintenance    ReviewResourceEvent = "RevokeTheReviewAfterMaintenance"    // 撤回维护后提交的申请（待审核 -> 待维护）
	ConstReviewResourceEventReviewApproved                     ReviewResourceEvent = "ReviewApproved"                     // 审核通过（待审核 -> 已上线）
	ConstReviewResourceEventReviewRejected                     ReviewResourceEvent = "ReviewRejected"                     // 审核驳回（待审核 -> 待维护）
	ConstReviewResourceEventReturnToMaintenanceAfterPublished  ReviewResourceEvent = "ReturnToMaintenanceAfterPublished"  // 上线后再维护（已上线 -> 待维护）
	ConstReviewResourceEventPublishedAfterImplementation       ReviewResourceEvent = "PublishedAfterImplementation"       // 实现后直接上线（待实现 -> 已上线）
	ConstReviewResourceEventPublishedAfterMaintenance          ReviewResourceEvent = "PublishedAfterMaintenance"          // 维护后直接上线（待维护 -> 已上线）
)

// ReviewResourceType 审核资源类型
type ReviewResourceType string

const (
	ConstReviewResourceTypeComponentGroup ReviewResourceType = "COMPONENT_GROUP" // 组件组
	ConstReviewResourceTypeCase           ReviewResourceType = "CASE"            // 用例

	ConstReviewResourceTypeSetupComponent    ReviewResourceType = "SETUP"          // 前置组件
	ConstReviewResourceTypeTeardownComponent ReviewResourceType = "TEARDOWN"       // 后置组件
	ConstReviewResourceTypeBusinessComponent ReviewResourceType = "GROUP"          // 业务组件
	ConstReviewResourceTypeAPICase           ReviewResourceType = "API_CASE"       // 场景用例
	ConstReviewResourceTypeInterfaceCase     ReviewResourceType = "INTERFACE_CASE" // 接口用例
)

type ReviewResourceTypeZH string

const (
	ConstReviewResourceTypeZHComponentGroup ReviewResourceTypeZH = "组件组"
	ConstReviewResourceTypeZHCase           ReviewResourceTypeZH = "用例"

	ConstReviewResourceTypeZHSetupComponent    ReviewResourceTypeZH = "前置组件"
	ConstReviewResourceTypeZHTeardownComponent ReviewResourceTypeZH = "后置组件"
	ConstReviewResourceTypeZHBusinessComponent ReviewResourceTypeZH = "业务组件"
	ConstReviewResourceTypeZHAPICase           ReviewResourceTypeZH = "场景用例"
	ConstReviewResourceTypeZHInterfaceCase     ReviewResourceTypeZH = "接口用例"
)

// ReviewStatus 审核状态
type ReviewStatus string

const (
	ConstReviewStatusPending  ReviewStatus = "PENDING"  // 待审核
	ConstReviewStatusRevoked  ReviewStatus = "REVOKED"  // 已撤回
	ConstReviewStatusApproved ReviewStatus = "APPROVED" // 通过
	ConstReviewStatusRejected ReviewStatus = "REJECTED" // 驳回
)

type ReviewStatusZH string

const (
	ConstReviewStatusZHPending  ReviewStatusZH = "待审核"
	ConstReviewStatusZHRevoked  ReviewStatusZH = "已撤回"
	ConstReviewStatusZHApproved ReviewStatusZH = "通过"
	ConstReviewStatusZHRejected ReviewStatusZH = "驳回"
)

// FileExtension 文件扩展名
type FileExtension string

const (
	ConstFileExtensionJson FileExtension = ".json"
	ConstFileExtensionYaml FileExtension = ".yaml"
	ConstFileExtensionYml  FileExtension = ".yml"
	ConstFileExtensionCsv  FileExtension = ".csv"
)

// PerfCaseStepType 压测用例步骤类型
type PerfCaseStepType string

const (
	ConstPerfCaseStepTypeSetup    PerfCaseStepType = "SETUP"    // 前置步骤
	ConstPerfCaseStepTypeSerial   PerfCaseStepType = "SERIAL"   // 串行步骤
	ConstPerfCaseStepTypeParallel PerfCaseStepType = "PARALLEL" // 并行步骤
	ConstPerfCaseStepTypeTeardown PerfCaseStepType = "TEARDOWN" // 后置步骤
)

// TestType 测试类型
type TestType string

const (
	ConstTestTypeAPI       TestType = "API"       // API测试
	ConstTestTypeUI        TestType = "UI"        // UI测试
	ConstTestTypePerf      TestType = "PERF"      // 压力测试
	ConstTestTypeStability TestType = "STABILITY" // 稳定性测试
)

type InterfaceMetricsReferenceProtocol string

const (
	ConstInterfaceMetricsReferenceProtocolTT InterfaceMetricsReferenceProtocol = "TT"
)

// LarkChatStatus 飞书群组状态
type LarkChatStatus string

const (
	ConstLarkChatStatusNormal        LarkChatStatus = "normal"         // 正常
	ConstLarkChatStatusDissolved     LarkChatStatus = "dissolved"      // 解散
	ConstLarkChatStatusDissolvedSave LarkChatStatus = "dissolved_save" // 解散并保留
)

const DeviceFieldOfUsage devicehubcommon.DeviceField = "usage"
