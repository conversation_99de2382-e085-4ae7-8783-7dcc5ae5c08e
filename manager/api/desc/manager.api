syntax = "v1"

info (
	title:   "质量平台-测试管理服务"
	desc:    "质量平台中的测试管理服务"
	author:  "K"
	email:   "ha<PERSON><PERSON><EMAIL>"
	version: "0.2.5"
)

import (
	"project.api"
	"category.api"
	"tag.api"
	"function.api"
	"general_config.api"
	"account_config.api"
	"interface_definition.api"
	"component_group.api"
	"apicase.api"
	"apisuite.api"
	"apiplan.api"
	"advanced_search.api"
	"notify.api"
	"git_config.api"
	"uiplan.api"
	"apiservice.api"
	"like_plan.api"
	"review.api"
	"casepublic.api"
	"lark_app.api"
	"protobuf_config.api"
	"perfdata.api"
	"perfcase.api"
	"perfplan.api"
	"perfcasev2.api"
	"perfplanv2.api"
	"perf_stop_rule.api"
	"perf_lark_chat.api"
	"perf_lark_member.api"
	"project_device.api"
	"stability_plan.api"
	"lark_chat.api"
)