// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: manager/manager.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ApiExecutionService_GetApiExecutionData_FullMethodName          = "/manager.ApiExecutionService/GetApiExecutionData"
	ApiExecutionService_GetApiExecutionDataStream_FullMethodName    = "/manager.ApiExecutionService/GetApiExecutionDataStream"
	ApiExecutionService_GetApiExecutionDataStructure_FullMethodName = "/manager.ApiExecutionService/GetApiExecutionDataStructure"
)

// ApiExecutionServiceClient is the client API for ApiExecutionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ApiExecutionService API执行服务
type ApiExecutionServiceClient interface {
	//GetApiExecutionData 获取API执行数据
	GetApiExecutionData(ctx context.Context, in *GetApiExecutionDataReq, opts ...grpc.CallOption) (*ApiExecutionData, error)
	//GetApiExecutionDataStream 批量获取API执行数据（服务端流式）
	GetApiExecutionDataStream(ctx context.Context, in *GetApiExecutionDataStreamReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ApiExecutionData], error)
	//GetApiExecutionDataStructure 获取API执行数据结构
	GetApiExecutionDataStructure(ctx context.Context, in *GetApiExecutionDataStructureReq, opts ...grpc.CallOption) (*ApiExecutionData, error)
}

type apiExecutionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApiExecutionServiceClient(cc grpc.ClientConnInterface) ApiExecutionServiceClient {
	return &apiExecutionServiceClient{cc}
}

func (c *apiExecutionServiceClient) GetApiExecutionData(ctx context.Context, in *GetApiExecutionDataReq, opts ...grpc.CallOption) (*ApiExecutionData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApiExecutionData)
	err := c.cc.Invoke(ctx, ApiExecutionService_GetApiExecutionData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiExecutionServiceClient) GetApiExecutionDataStream(ctx context.Context, in *GetApiExecutionDataStreamReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ApiExecutionData], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &ApiExecutionService_ServiceDesc.Streams[0], ApiExecutionService_GetApiExecutionDataStream_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[GetApiExecutionDataStreamReq, ApiExecutionData]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ApiExecutionService_GetApiExecutionDataStreamClient = grpc.ServerStreamingClient[ApiExecutionData]

func (c *apiExecutionServiceClient) GetApiExecutionDataStructure(ctx context.Context, in *GetApiExecutionDataStructureReq, opts ...grpc.CallOption) (*ApiExecutionData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApiExecutionData)
	err := c.cc.Invoke(ctx, ApiExecutionService_GetApiExecutionDataStructure_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiExecutionServiceServer is the server API for ApiExecutionService service.
// All implementations must embed UnimplementedApiExecutionServiceServer
// for forward compatibility.
//
// ApiExecutionService API执行服务
type ApiExecutionServiceServer interface {
	//GetApiExecutionData 获取API执行数据
	GetApiExecutionData(context.Context, *GetApiExecutionDataReq) (*ApiExecutionData, error)
	//GetApiExecutionDataStream 批量获取API执行数据（服务端流式）
	GetApiExecutionDataStream(*GetApiExecutionDataStreamReq, grpc.ServerStreamingServer[ApiExecutionData]) error
	//GetApiExecutionDataStructure 获取API执行数据结构
	GetApiExecutionDataStructure(context.Context, *GetApiExecutionDataStructureReq) (*ApiExecutionData, error)
	mustEmbedUnimplementedApiExecutionServiceServer()
}

// UnimplementedApiExecutionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedApiExecutionServiceServer struct{}

func (UnimplementedApiExecutionServiceServer) GetApiExecutionData(context.Context, *GetApiExecutionDataReq) (*ApiExecutionData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApiExecutionData not implemented")
}
func (UnimplementedApiExecutionServiceServer) GetApiExecutionDataStream(*GetApiExecutionDataStreamReq, grpc.ServerStreamingServer[ApiExecutionData]) error {
	return status.Errorf(codes.Unimplemented, "method GetApiExecutionDataStream not implemented")
}
func (UnimplementedApiExecutionServiceServer) GetApiExecutionDataStructure(context.Context, *GetApiExecutionDataStructureReq) (*ApiExecutionData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApiExecutionDataStructure not implemented")
}
func (UnimplementedApiExecutionServiceServer) mustEmbedUnimplementedApiExecutionServiceServer() {}
func (UnimplementedApiExecutionServiceServer) testEmbeddedByValue()                             {}

// UnsafeApiExecutionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiExecutionServiceServer will
// result in compilation errors.
type UnsafeApiExecutionServiceServer interface {
	mustEmbedUnimplementedApiExecutionServiceServer()
}

func RegisterApiExecutionServiceServer(s grpc.ServiceRegistrar, srv ApiExecutionServiceServer) {
	// If the following call pancis, it indicates UnimplementedApiExecutionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ApiExecutionService_ServiceDesc, srv)
}

func _ApiExecutionService_GetApiExecutionData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApiExecutionDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiExecutionServiceServer).GetApiExecutionData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiExecutionService_GetApiExecutionData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiExecutionServiceServer).GetApiExecutionData(ctx, req.(*GetApiExecutionDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiExecutionService_GetApiExecutionDataStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetApiExecutionDataStreamReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ApiExecutionServiceServer).GetApiExecutionDataStream(m, &grpc.GenericServerStream[GetApiExecutionDataStreamReq, ApiExecutionData]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ApiExecutionService_GetApiExecutionDataStreamServer = grpc.ServerStreamingServer[ApiExecutionData]

func _ApiExecutionService_GetApiExecutionDataStructure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApiExecutionDataStructureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiExecutionServiceServer).GetApiExecutionDataStructure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiExecutionService_GetApiExecutionDataStructure_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiExecutionServiceServer).GetApiExecutionDataStructure(ctx, req.(*GetApiExecutionDataStructureReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ApiExecutionService_ServiceDesc is the grpc.ServiceDesc for ApiExecutionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApiExecutionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ApiExecutionService",
	HandlerType: (*ApiExecutionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetApiExecutionData",
			Handler:    _ApiExecutionService_GetApiExecutionData_Handler,
		},
		{
			MethodName: "GetApiExecutionDataStructure",
			Handler:    _ApiExecutionService_GetApiExecutionDataStructure_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetApiExecutionDataStream",
			Handler:       _ApiExecutionService_GetApiExecutionDataStream_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "manager/manager.proto",
}

const (
	ProjectService_CreateProject_FullMethodName                 = "/manager.ProjectService/CreateProject"
	ProjectService_RemoveProject_FullMethodName                 = "/manager.ProjectService/RemoveProject"
	ProjectService_ModifyProject_FullMethodName                 = "/manager.ProjectService/ModifyProject"
	ProjectService_SearchProject_FullMethodName                 = "/manager.ProjectService/SearchProject"
	ProjectService_ViewProject_FullMethodName                   = "/manager.ProjectService/ViewProject"
	ProjectService_SearchProjectUser_FullMethodName             = "/manager.ProjectService/SearchProjectUser"
	ProjectService_ModifyProjectReviewFunction_FullMethodName   = "/manager.ProjectService/ModifyProjectReviewFunction"
	ProjectService_ModifyProjectCoverageFunction_FullMethodName = "/manager.ProjectService/ModifyProjectCoverageFunction"
)

// ProjectServiceClient is the client API for ProjectService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ProjectService 项目服务
type ProjectServiceClient interface {
	//CreateProject 创建项目
	CreateProject(ctx context.Context, in *CreateProjectReq, opts ...grpc.CallOption) (*CreateProjectResp, error)
	//RemoveProject 删除项目
	RemoveProject(ctx context.Context, in *RemoveProjectReq, opts ...grpc.CallOption) (*RemoveProjectResp, error)
	//ModifyProject 编辑项目
	ModifyProject(ctx context.Context, in *ModifyProjectReq, opts ...grpc.CallOption) (*ModifyProjectResp, error)
	//SearchProject 搜索项目
	SearchProject(ctx context.Context, in *SearchProjectReq, opts ...grpc.CallOption) (*SearchProjectResp, error)
	//ViewProject 查看项目
	ViewProject(ctx context.Context, in *ViewProjectReq, opts ...grpc.CallOption) (*ViewProjectResp, error)
	//SearchProjectUser 搜索项目用户
	SearchProjectUser(ctx context.Context, in *SearchProjectUserReq, opts ...grpc.CallOption) (*SearchProjectUserResp, error)
	//ModifyProjectReviewFunction 开启、关闭用例审核功能
	ModifyProjectReviewFunction(ctx context.Context, in *ModifyProjectReviewFunctionReq, opts ...grpc.CallOption) (*ModifyProjectReviewFunctionResp, error)
	//ModifyProjectCoverageFunction 开启、关闭接口用例覆盖率功能
	ModifyProjectCoverageFunction(ctx context.Context, in *ModifyProjectCoverageFunctionReq, opts ...grpc.CallOption) (*ModifyProjectCoverageFunctionResp, error)
}

type projectServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewProjectServiceClient(cc grpc.ClientConnInterface) ProjectServiceClient {
	return &projectServiceClient{cc}
}

func (c *projectServiceClient) CreateProject(ctx context.Context, in *CreateProjectReq, opts ...grpc.CallOption) (*CreateProjectResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateProjectResp)
	err := c.cc.Invoke(ctx, ProjectService_CreateProject_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) RemoveProject(ctx context.Context, in *RemoveProjectReq, opts ...grpc.CallOption) (*RemoveProjectResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveProjectResp)
	err := c.cc.Invoke(ctx, ProjectService_RemoveProject_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) ModifyProject(ctx context.Context, in *ModifyProjectReq, opts ...grpc.CallOption) (*ModifyProjectResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyProjectResp)
	err := c.cc.Invoke(ctx, ProjectService_ModifyProject_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) SearchProject(ctx context.Context, in *SearchProjectReq, opts ...grpc.CallOption) (*SearchProjectResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchProjectResp)
	err := c.cc.Invoke(ctx, ProjectService_SearchProject_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) ViewProject(ctx context.Context, in *ViewProjectReq, opts ...grpc.CallOption) (*ViewProjectResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewProjectResp)
	err := c.cc.Invoke(ctx, ProjectService_ViewProject_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) SearchProjectUser(ctx context.Context, in *SearchProjectUserReq, opts ...grpc.CallOption) (*SearchProjectUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchProjectUserResp)
	err := c.cc.Invoke(ctx, ProjectService_SearchProjectUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) ModifyProjectReviewFunction(ctx context.Context, in *ModifyProjectReviewFunctionReq, opts ...grpc.CallOption) (*ModifyProjectReviewFunctionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyProjectReviewFunctionResp)
	err := c.cc.Invoke(ctx, ProjectService_ModifyProjectReviewFunction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectServiceClient) ModifyProjectCoverageFunction(ctx context.Context, in *ModifyProjectCoverageFunctionReq, opts ...grpc.CallOption) (*ModifyProjectCoverageFunctionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyProjectCoverageFunctionResp)
	err := c.cc.Invoke(ctx, ProjectService_ModifyProjectCoverageFunction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProjectServiceServer is the server API for ProjectService service.
// All implementations must embed UnimplementedProjectServiceServer
// for forward compatibility.
//
// ProjectService 项目服务
type ProjectServiceServer interface {
	//CreateProject 创建项目
	CreateProject(context.Context, *CreateProjectReq) (*CreateProjectResp, error)
	//RemoveProject 删除项目
	RemoveProject(context.Context, *RemoveProjectReq) (*RemoveProjectResp, error)
	//ModifyProject 编辑项目
	ModifyProject(context.Context, *ModifyProjectReq) (*ModifyProjectResp, error)
	//SearchProject 搜索项目
	SearchProject(context.Context, *SearchProjectReq) (*SearchProjectResp, error)
	//ViewProject 查看项目
	ViewProject(context.Context, *ViewProjectReq) (*ViewProjectResp, error)
	//SearchProjectUser 搜索项目用户
	SearchProjectUser(context.Context, *SearchProjectUserReq) (*SearchProjectUserResp, error)
	//ModifyProjectReviewFunction 开启、关闭用例审核功能
	ModifyProjectReviewFunction(context.Context, *ModifyProjectReviewFunctionReq) (*ModifyProjectReviewFunctionResp, error)
	//ModifyProjectCoverageFunction 开启、关闭接口用例覆盖率功能
	ModifyProjectCoverageFunction(context.Context, *ModifyProjectCoverageFunctionReq) (*ModifyProjectCoverageFunctionResp, error)
	mustEmbedUnimplementedProjectServiceServer()
}

// UnimplementedProjectServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedProjectServiceServer struct{}

func (UnimplementedProjectServiceServer) CreateProject(context.Context, *CreateProjectReq) (*CreateProjectResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateProject not implemented")
}
func (UnimplementedProjectServiceServer) RemoveProject(context.Context, *RemoveProjectReq) (*RemoveProjectResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveProject not implemented")
}
func (UnimplementedProjectServiceServer) ModifyProject(context.Context, *ModifyProjectReq) (*ModifyProjectResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyProject not implemented")
}
func (UnimplementedProjectServiceServer) SearchProject(context.Context, *SearchProjectReq) (*SearchProjectResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchProject not implemented")
}
func (UnimplementedProjectServiceServer) ViewProject(context.Context, *ViewProjectReq) (*ViewProjectResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewProject not implemented")
}
func (UnimplementedProjectServiceServer) SearchProjectUser(context.Context, *SearchProjectUserReq) (*SearchProjectUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchProjectUser not implemented")
}
func (UnimplementedProjectServiceServer) ModifyProjectReviewFunction(context.Context, *ModifyProjectReviewFunctionReq) (*ModifyProjectReviewFunctionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyProjectReviewFunction not implemented")
}
func (UnimplementedProjectServiceServer) ModifyProjectCoverageFunction(context.Context, *ModifyProjectCoverageFunctionReq) (*ModifyProjectCoverageFunctionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyProjectCoverageFunction not implemented")
}
func (UnimplementedProjectServiceServer) mustEmbedUnimplementedProjectServiceServer() {}
func (UnimplementedProjectServiceServer) testEmbeddedByValue()                        {}

// UnsafeProjectServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProjectServiceServer will
// result in compilation errors.
type UnsafeProjectServiceServer interface {
	mustEmbedUnimplementedProjectServiceServer()
}

func RegisterProjectServiceServer(s grpc.ServiceRegistrar, srv ProjectServiceServer) {
	// If the following call pancis, it indicates UnimplementedProjectServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ProjectService_ServiceDesc, srv)
}

func _ProjectService_CreateProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateProjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).CreateProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_CreateProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).CreateProject(ctx, req.(*CreateProjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_RemoveProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveProjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).RemoveProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_RemoveProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).RemoveProject(ctx, req.(*RemoveProjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_ModifyProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyProjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).ModifyProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_ModifyProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).ModifyProject(ctx, req.(*ModifyProjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_SearchProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchProjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).SearchProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_SearchProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).SearchProject(ctx, req.(*SearchProjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_ViewProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewProjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).ViewProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_ViewProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).ViewProject(ctx, req.(*ViewProjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_SearchProjectUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchProjectUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).SearchProjectUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_SearchProjectUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).SearchProjectUser(ctx, req.(*SearchProjectUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_ModifyProjectReviewFunction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyProjectReviewFunctionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).ModifyProjectReviewFunction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_ModifyProjectReviewFunction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).ModifyProjectReviewFunction(ctx, req.(*ModifyProjectReviewFunctionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectService_ModifyProjectCoverageFunction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyProjectCoverageFunctionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServiceServer).ModifyProjectCoverageFunction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectService_ModifyProjectCoverageFunction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServiceServer).ModifyProjectCoverageFunction(ctx, req.(*ModifyProjectCoverageFunctionReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ProjectService_ServiceDesc is the grpc.ServiceDesc for ProjectService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProjectService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ProjectService",
	HandlerType: (*ProjectServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateProject",
			Handler:    _ProjectService_CreateProject_Handler,
		},
		{
			MethodName: "RemoveProject",
			Handler:    _ProjectService_RemoveProject_Handler,
		},
		{
			MethodName: "ModifyProject",
			Handler:    _ProjectService_ModifyProject_Handler,
		},
		{
			MethodName: "SearchProject",
			Handler:    _ProjectService_SearchProject_Handler,
		},
		{
			MethodName: "ViewProject",
			Handler:    _ProjectService_ViewProject_Handler,
		},
		{
			MethodName: "SearchProjectUser",
			Handler:    _ProjectService_SearchProjectUser_Handler,
		},
		{
			MethodName: "ModifyProjectReviewFunction",
			Handler:    _ProjectService_ModifyProjectReviewFunction_Handler,
		},
		{
			MethodName: "ModifyProjectCoverageFunction",
			Handler:    _ProjectService_ModifyProjectCoverageFunction_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	CategoryService_CreateCategory_FullMethodName   = "/manager.CategoryService/CreateCategory"
	CategoryService_RemoveCategory_FullMethodName   = "/manager.CategoryService/RemoveCategory"
	CategoryService_ModifyCategory_FullMethodName   = "/manager.CategoryService/ModifyCategory"
	CategoryService_SearchCategory_FullMethodName   = "/manager.CategoryService/SearchCategory"
	CategoryService_MoveCategoryTree_FullMethodName = "/manager.CategoryService/MoveCategoryTree"
	CategoryService_GetCategoryTree_FullMethodName  = "/manager.CategoryService/GetCategoryTree"
)

// CategoryServiceClient is the client API for CategoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CategoryService 分类服务
type CategoryServiceClient interface {
	//CreateCategory 创建分类
	CreateCategory(ctx context.Context, in *CreateCategoryReq, opts ...grpc.CallOption) (*CreateCategoryResp, error)
	//RemoveCategory 删除分类
	RemoveCategory(ctx context.Context, in *RemoveCategoryReq, opts ...grpc.CallOption) (*RemoveCategoryResp, error)
	//ModifyCategory 编辑分类
	ModifyCategory(ctx context.Context, in *ModifyCategoryReq, opts ...grpc.CallOption) (*ModifyCategoryResp, error)
	//SearchCategory 搜索分类
	SearchCategory(ctx context.Context, in *SearchCategoryReq, opts ...grpc.CallOption) (*SearchCategoryResp, error)
	//MoveCategoryTree 移动分类树
	MoveCategoryTree(ctx context.Context, in *MoveCategoryTreeReq, opts ...grpc.CallOption) (*MoveCategoryTreeResp, error)
	//GetCategoryTree 获取分类树
	GetCategoryTree(ctx context.Context, in *GetCategoryTreeReq, opts ...grpc.CallOption) (*GetCategoryTreeResp, error)
}

type categoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCategoryServiceClient(cc grpc.ClientConnInterface) CategoryServiceClient {
	return &categoryServiceClient{cc}
}

func (c *categoryServiceClient) CreateCategory(ctx context.Context, in *CreateCategoryReq, opts ...grpc.CallOption) (*CreateCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCategoryResp)
	err := c.cc.Invoke(ctx, CategoryService_CreateCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *categoryServiceClient) RemoveCategory(ctx context.Context, in *RemoveCategoryReq, opts ...grpc.CallOption) (*RemoveCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveCategoryResp)
	err := c.cc.Invoke(ctx, CategoryService_RemoveCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *categoryServiceClient) ModifyCategory(ctx context.Context, in *ModifyCategoryReq, opts ...grpc.CallOption) (*ModifyCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyCategoryResp)
	err := c.cc.Invoke(ctx, CategoryService_ModifyCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *categoryServiceClient) SearchCategory(ctx context.Context, in *SearchCategoryReq, opts ...grpc.CallOption) (*SearchCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCategoryResp)
	err := c.cc.Invoke(ctx, CategoryService_SearchCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *categoryServiceClient) MoveCategoryTree(ctx context.Context, in *MoveCategoryTreeReq, opts ...grpc.CallOption) (*MoveCategoryTreeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MoveCategoryTreeResp)
	err := c.cc.Invoke(ctx, CategoryService_MoveCategoryTree_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *categoryServiceClient) GetCategoryTree(ctx context.Context, in *GetCategoryTreeReq, opts ...grpc.CallOption) (*GetCategoryTreeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCategoryTreeResp)
	err := c.cc.Invoke(ctx, CategoryService_GetCategoryTree_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CategoryServiceServer is the server API for CategoryService service.
// All implementations must embed UnimplementedCategoryServiceServer
// for forward compatibility.
//
// CategoryService 分类服务
type CategoryServiceServer interface {
	//CreateCategory 创建分类
	CreateCategory(context.Context, *CreateCategoryReq) (*CreateCategoryResp, error)
	//RemoveCategory 删除分类
	RemoveCategory(context.Context, *RemoveCategoryReq) (*RemoveCategoryResp, error)
	//ModifyCategory 编辑分类
	ModifyCategory(context.Context, *ModifyCategoryReq) (*ModifyCategoryResp, error)
	//SearchCategory 搜索分类
	SearchCategory(context.Context, *SearchCategoryReq) (*SearchCategoryResp, error)
	//MoveCategoryTree 移动分类树
	MoveCategoryTree(context.Context, *MoveCategoryTreeReq) (*MoveCategoryTreeResp, error)
	//GetCategoryTree 获取分类树
	GetCategoryTree(context.Context, *GetCategoryTreeReq) (*GetCategoryTreeResp, error)
	mustEmbedUnimplementedCategoryServiceServer()
}

// UnimplementedCategoryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCategoryServiceServer struct{}

func (UnimplementedCategoryServiceServer) CreateCategory(context.Context, *CreateCategoryReq) (*CreateCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCategory not implemented")
}
func (UnimplementedCategoryServiceServer) RemoveCategory(context.Context, *RemoveCategoryReq) (*RemoveCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveCategory not implemented")
}
func (UnimplementedCategoryServiceServer) ModifyCategory(context.Context, *ModifyCategoryReq) (*ModifyCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyCategory not implemented")
}
func (UnimplementedCategoryServiceServer) SearchCategory(context.Context, *SearchCategoryReq) (*SearchCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCategory not implemented")
}
func (UnimplementedCategoryServiceServer) MoveCategoryTree(context.Context, *MoveCategoryTreeReq) (*MoveCategoryTreeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MoveCategoryTree not implemented")
}
func (UnimplementedCategoryServiceServer) GetCategoryTree(context.Context, *GetCategoryTreeReq) (*GetCategoryTreeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCategoryTree not implemented")
}
func (UnimplementedCategoryServiceServer) mustEmbedUnimplementedCategoryServiceServer() {}
func (UnimplementedCategoryServiceServer) testEmbeddedByValue()                         {}

// UnsafeCategoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CategoryServiceServer will
// result in compilation errors.
type UnsafeCategoryServiceServer interface {
	mustEmbedUnimplementedCategoryServiceServer()
}

func RegisterCategoryServiceServer(s grpc.ServiceRegistrar, srv CategoryServiceServer) {
	// If the following call pancis, it indicates UnimplementedCategoryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CategoryService_ServiceDesc, srv)
}

func _CategoryService_CreateCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryServiceServer).CreateCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryService_CreateCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryServiceServer).CreateCategory(ctx, req.(*CreateCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CategoryService_RemoveCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryServiceServer).RemoveCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryService_RemoveCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryServiceServer).RemoveCategory(ctx, req.(*RemoveCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CategoryService_ModifyCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryServiceServer).ModifyCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryService_ModifyCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryServiceServer).ModifyCategory(ctx, req.(*ModifyCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CategoryService_SearchCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryServiceServer).SearchCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryService_SearchCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryServiceServer).SearchCategory(ctx, req.(*SearchCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CategoryService_MoveCategoryTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveCategoryTreeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryServiceServer).MoveCategoryTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryService_MoveCategoryTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryServiceServer).MoveCategoryTree(ctx, req.(*MoveCategoryTreeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CategoryService_GetCategoryTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCategoryTreeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryServiceServer).GetCategoryTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryService_GetCategoryTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryServiceServer).GetCategoryTree(ctx, req.(*GetCategoryTreeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CategoryService_ServiceDesc is the grpc.ServiceDesc for CategoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CategoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.CategoryService",
	HandlerType: (*CategoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCategory",
			Handler:    _CategoryService_CreateCategory_Handler,
		},
		{
			MethodName: "RemoveCategory",
			Handler:    _CategoryService_RemoveCategory_Handler,
		},
		{
			MethodName: "ModifyCategory",
			Handler:    _CategoryService_ModifyCategory_Handler,
		},
		{
			MethodName: "SearchCategory",
			Handler:    _CategoryService_SearchCategory_Handler,
		},
		{
			MethodName: "MoveCategoryTree",
			Handler:    _CategoryService_MoveCategoryTree_Handler,
		},
		{
			MethodName: "GetCategoryTree",
			Handler:    _CategoryService_GetCategoryTree_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	TagService_CreateTag_FullMethodName = "/manager.TagService/CreateTag"
	TagService_RemoveTag_FullMethodName = "/manager.TagService/RemoveTag"
	TagService_ModifyTag_FullMethodName = "/manager.TagService/ModifyTag"
	TagService_SearchTag_FullMethodName = "/manager.TagService/SearchTag"
	TagService_ViewTag_FullMethodName   = "/manager.TagService/ViewTag"
)

// TagServiceClient is the client API for TagService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// TagService 标签服务
type TagServiceClient interface {
	//CreateTag 创建标签
	CreateTag(ctx context.Context, in *CreateTagReq, opts ...grpc.CallOption) (*CreateTagResp, error)
	//RemoveTag 删除标签
	RemoveTag(ctx context.Context, in *RemoveTagReq, opts ...grpc.CallOption) (*RemoveTagResp, error)
	//ModifyTag 编辑标签
	ModifyTag(ctx context.Context, in *ModifyTagReq, opts ...grpc.CallOption) (*ModifyTagResp, error)
	//SearchTag 查询标签
	SearchTag(ctx context.Context, in *SearchTagReq, opts ...grpc.CallOption) (*SearchTagResp, error)
	//ViewTag   查看标签
	ViewTag(ctx context.Context, in *ViewTagReq, opts ...grpc.CallOption) (*ViewTagResp, error)
}

type tagServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTagServiceClient(cc grpc.ClientConnInterface) TagServiceClient {
	return &tagServiceClient{cc}
}

func (c *tagServiceClient) CreateTag(ctx context.Context, in *CreateTagReq, opts ...grpc.CallOption) (*CreateTagResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTagResp)
	err := c.cc.Invoke(ctx, TagService_CreateTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagServiceClient) RemoveTag(ctx context.Context, in *RemoveTagReq, opts ...grpc.CallOption) (*RemoveTagResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveTagResp)
	err := c.cc.Invoke(ctx, TagService_RemoveTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagServiceClient) ModifyTag(ctx context.Context, in *ModifyTagReq, opts ...grpc.CallOption) (*ModifyTagResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyTagResp)
	err := c.cc.Invoke(ctx, TagService_ModifyTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagServiceClient) SearchTag(ctx context.Context, in *SearchTagReq, opts ...grpc.CallOption) (*SearchTagResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchTagResp)
	err := c.cc.Invoke(ctx, TagService_SearchTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagServiceClient) ViewTag(ctx context.Context, in *ViewTagReq, opts ...grpc.CallOption) (*ViewTagResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewTagResp)
	err := c.cc.Invoke(ctx, TagService_ViewTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TagServiceServer is the server API for TagService service.
// All implementations must embed UnimplementedTagServiceServer
// for forward compatibility.
//
// TagService 标签服务
type TagServiceServer interface {
	//CreateTag 创建标签
	CreateTag(context.Context, *CreateTagReq) (*CreateTagResp, error)
	//RemoveTag 删除标签
	RemoveTag(context.Context, *RemoveTagReq) (*RemoveTagResp, error)
	//ModifyTag 编辑标签
	ModifyTag(context.Context, *ModifyTagReq) (*ModifyTagResp, error)
	//SearchTag 查询标签
	SearchTag(context.Context, *SearchTagReq) (*SearchTagResp, error)
	//ViewTag   查看标签
	ViewTag(context.Context, *ViewTagReq) (*ViewTagResp, error)
	mustEmbedUnimplementedTagServiceServer()
}

// UnimplementedTagServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTagServiceServer struct{}

func (UnimplementedTagServiceServer) CreateTag(context.Context, *CreateTagReq) (*CreateTagResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTag not implemented")
}
func (UnimplementedTagServiceServer) RemoveTag(context.Context, *RemoveTagReq) (*RemoveTagResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveTag not implemented")
}
func (UnimplementedTagServiceServer) ModifyTag(context.Context, *ModifyTagReq) (*ModifyTagResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyTag not implemented")
}
func (UnimplementedTagServiceServer) SearchTag(context.Context, *SearchTagReq) (*SearchTagResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTag not implemented")
}
func (UnimplementedTagServiceServer) ViewTag(context.Context, *ViewTagReq) (*ViewTagResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewTag not implemented")
}
func (UnimplementedTagServiceServer) mustEmbedUnimplementedTagServiceServer() {}
func (UnimplementedTagServiceServer) testEmbeddedByValue()                    {}

// UnsafeTagServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TagServiceServer will
// result in compilation errors.
type UnsafeTagServiceServer interface {
	mustEmbedUnimplementedTagServiceServer()
}

func RegisterTagServiceServer(s grpc.ServiceRegistrar, srv TagServiceServer) {
	// If the following call pancis, it indicates UnimplementedTagServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TagService_ServiceDesc, srv)
}

func _TagService_CreateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagServiceServer).CreateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagService_CreateTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagServiceServer).CreateTag(ctx, req.(*CreateTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagService_RemoveTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagServiceServer).RemoveTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagService_RemoveTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagServiceServer).RemoveTag(ctx, req.(*RemoveTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagService_ModifyTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagServiceServer).ModifyTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagService_ModifyTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagServiceServer).ModifyTag(ctx, req.(*ModifyTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagService_SearchTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagServiceServer).SearchTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagService_SearchTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagServiceServer).SearchTag(ctx, req.(*SearchTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagService_ViewTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagServiceServer).ViewTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagService_ViewTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagServiceServer).ViewTag(ctx, req.(*ViewTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TagService_ServiceDesc is the grpc.ServiceDesc for TagService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TagService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.TagService",
	HandlerType: (*TagServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTag",
			Handler:    _TagService_CreateTag_Handler,
		},
		{
			MethodName: "RemoveTag",
			Handler:    _TagService_RemoveTag_Handler,
		},
		{
			MethodName: "ModifyTag",
			Handler:    _TagService_ModifyTag_Handler,
		},
		{
			MethodName: "SearchTag",
			Handler:    _TagService_SearchTag_Handler,
		},
		{
			MethodName: "ViewTag",
			Handler:    _TagService_ViewTag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	DataProcessingFunctionService_CreateDataProcessingFunction_FullMethodName = "/manager.DataProcessingFunctionService/CreateDataProcessingFunction"
	DataProcessingFunctionService_RemoveDataProcessingFunction_FullMethodName = "/manager.DataProcessingFunctionService/RemoveDataProcessingFunction"
	DataProcessingFunctionService_ModifyDataProcessingFunction_FullMethodName = "/manager.DataProcessingFunctionService/ModifyDataProcessingFunction"
	DataProcessingFunctionService_SearchDataProcessingFunction_FullMethodName = "/manager.DataProcessingFunctionService/SearchDataProcessingFunction"
	DataProcessingFunctionService_ViewDataProcessingFunction_FullMethodName   = "/manager.DataProcessingFunctionService/ViewDataProcessingFunction"
)

// DataProcessingFunctionServiceClient is the client API for DataProcessingFunctionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// DataProcessingFunctionService 数据处理函数服务
type DataProcessingFunctionServiceClient interface {
	//CreateDataProcessingFunction 创建数据处理函数
	CreateDataProcessingFunction(ctx context.Context, in *CreateOrModifyDataProcessingFunctionReq, opts ...grpc.CallOption) (*CreateOrModifyDataProcessingFunctionResp, error)
	//RemoveDataProcessingFunction 删除数据处理函数
	RemoveDataProcessingFunction(ctx context.Context, in *RemoveDataProcessingFunctionReq, opts ...grpc.CallOption) (*RemoveDataProcessingFunctionResp, error)
	//ModifyDataProcessingFunction 编辑数据处理函数
	ModifyDataProcessingFunction(ctx context.Context, in *CreateOrModifyDataProcessingFunctionReq, opts ...grpc.CallOption) (*CreateOrModifyDataProcessingFunctionResp, error)
	//SearchDataProcessingFunction 搜索数据处理函数
	SearchDataProcessingFunction(ctx context.Context, in *SearchDataProcessingFunctionReq, opts ...grpc.CallOption) (*SearchDataProcessingFunctionResp, error)
	//ViewDataProcessingFunction 查看数据处理函数
	ViewDataProcessingFunction(ctx context.Context, in *ViewDataProcessingFunctionReq, opts ...grpc.CallOption) (*ViewDataProcessingFunctionResp, error)
}

type dataProcessingFunctionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataProcessingFunctionServiceClient(cc grpc.ClientConnInterface) DataProcessingFunctionServiceClient {
	return &dataProcessingFunctionServiceClient{cc}
}

func (c *dataProcessingFunctionServiceClient) CreateDataProcessingFunction(ctx context.Context, in *CreateOrModifyDataProcessingFunctionReq, opts ...grpc.CallOption) (*CreateOrModifyDataProcessingFunctionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrModifyDataProcessingFunctionResp)
	err := c.cc.Invoke(ctx, DataProcessingFunctionService_CreateDataProcessingFunction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataProcessingFunctionServiceClient) RemoveDataProcessingFunction(ctx context.Context, in *RemoveDataProcessingFunctionReq, opts ...grpc.CallOption) (*RemoveDataProcessingFunctionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveDataProcessingFunctionResp)
	err := c.cc.Invoke(ctx, DataProcessingFunctionService_RemoveDataProcessingFunction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataProcessingFunctionServiceClient) ModifyDataProcessingFunction(ctx context.Context, in *CreateOrModifyDataProcessingFunctionReq, opts ...grpc.CallOption) (*CreateOrModifyDataProcessingFunctionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrModifyDataProcessingFunctionResp)
	err := c.cc.Invoke(ctx, DataProcessingFunctionService_ModifyDataProcessingFunction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataProcessingFunctionServiceClient) SearchDataProcessingFunction(ctx context.Context, in *SearchDataProcessingFunctionReq, opts ...grpc.CallOption) (*SearchDataProcessingFunctionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchDataProcessingFunctionResp)
	err := c.cc.Invoke(ctx, DataProcessingFunctionService_SearchDataProcessingFunction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataProcessingFunctionServiceClient) ViewDataProcessingFunction(ctx context.Context, in *ViewDataProcessingFunctionReq, opts ...grpc.CallOption) (*ViewDataProcessingFunctionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewDataProcessingFunctionResp)
	err := c.cc.Invoke(ctx, DataProcessingFunctionService_ViewDataProcessingFunction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataProcessingFunctionServiceServer is the server API for DataProcessingFunctionService service.
// All implementations must embed UnimplementedDataProcessingFunctionServiceServer
// for forward compatibility.
//
// DataProcessingFunctionService 数据处理函数服务
type DataProcessingFunctionServiceServer interface {
	//CreateDataProcessingFunction 创建数据处理函数
	CreateDataProcessingFunction(context.Context, *CreateOrModifyDataProcessingFunctionReq) (*CreateOrModifyDataProcessingFunctionResp, error)
	//RemoveDataProcessingFunction 删除数据处理函数
	RemoveDataProcessingFunction(context.Context, *RemoveDataProcessingFunctionReq) (*RemoveDataProcessingFunctionResp, error)
	//ModifyDataProcessingFunction 编辑数据处理函数
	ModifyDataProcessingFunction(context.Context, *CreateOrModifyDataProcessingFunctionReq) (*CreateOrModifyDataProcessingFunctionResp, error)
	//SearchDataProcessingFunction 搜索数据处理函数
	SearchDataProcessingFunction(context.Context, *SearchDataProcessingFunctionReq) (*SearchDataProcessingFunctionResp, error)
	//ViewDataProcessingFunction 查看数据处理函数
	ViewDataProcessingFunction(context.Context, *ViewDataProcessingFunctionReq) (*ViewDataProcessingFunctionResp, error)
	mustEmbedUnimplementedDataProcessingFunctionServiceServer()
}

// UnimplementedDataProcessingFunctionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDataProcessingFunctionServiceServer struct{}

func (UnimplementedDataProcessingFunctionServiceServer) CreateDataProcessingFunction(context.Context, *CreateOrModifyDataProcessingFunctionReq) (*CreateOrModifyDataProcessingFunctionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataProcessingFunction not implemented")
}
func (UnimplementedDataProcessingFunctionServiceServer) RemoveDataProcessingFunction(context.Context, *RemoveDataProcessingFunctionReq) (*RemoveDataProcessingFunctionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveDataProcessingFunction not implemented")
}
func (UnimplementedDataProcessingFunctionServiceServer) ModifyDataProcessingFunction(context.Context, *CreateOrModifyDataProcessingFunctionReq) (*CreateOrModifyDataProcessingFunctionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyDataProcessingFunction not implemented")
}
func (UnimplementedDataProcessingFunctionServiceServer) SearchDataProcessingFunction(context.Context, *SearchDataProcessingFunctionReq) (*SearchDataProcessingFunctionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchDataProcessingFunction not implemented")
}
func (UnimplementedDataProcessingFunctionServiceServer) ViewDataProcessingFunction(context.Context, *ViewDataProcessingFunctionReq) (*ViewDataProcessingFunctionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewDataProcessingFunction not implemented")
}
func (UnimplementedDataProcessingFunctionServiceServer) mustEmbedUnimplementedDataProcessingFunctionServiceServer() {
}
func (UnimplementedDataProcessingFunctionServiceServer) testEmbeddedByValue() {}

// UnsafeDataProcessingFunctionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataProcessingFunctionServiceServer will
// result in compilation errors.
type UnsafeDataProcessingFunctionServiceServer interface {
	mustEmbedUnimplementedDataProcessingFunctionServiceServer()
}

func RegisterDataProcessingFunctionServiceServer(s grpc.ServiceRegistrar, srv DataProcessingFunctionServiceServer) {
	// If the following call pancis, it indicates UnimplementedDataProcessingFunctionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DataProcessingFunctionService_ServiceDesc, srv)
}

func _DataProcessingFunctionService_CreateDataProcessingFunction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrModifyDataProcessingFunctionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataProcessingFunctionServiceServer).CreateDataProcessingFunction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataProcessingFunctionService_CreateDataProcessingFunction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataProcessingFunctionServiceServer).CreateDataProcessingFunction(ctx, req.(*CreateOrModifyDataProcessingFunctionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataProcessingFunctionService_RemoveDataProcessingFunction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveDataProcessingFunctionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataProcessingFunctionServiceServer).RemoveDataProcessingFunction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataProcessingFunctionService_RemoveDataProcessingFunction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataProcessingFunctionServiceServer).RemoveDataProcessingFunction(ctx, req.(*RemoveDataProcessingFunctionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataProcessingFunctionService_ModifyDataProcessingFunction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrModifyDataProcessingFunctionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataProcessingFunctionServiceServer).ModifyDataProcessingFunction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataProcessingFunctionService_ModifyDataProcessingFunction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataProcessingFunctionServiceServer).ModifyDataProcessingFunction(ctx, req.(*CreateOrModifyDataProcessingFunctionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataProcessingFunctionService_SearchDataProcessingFunction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchDataProcessingFunctionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataProcessingFunctionServiceServer).SearchDataProcessingFunction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataProcessingFunctionService_SearchDataProcessingFunction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataProcessingFunctionServiceServer).SearchDataProcessingFunction(ctx, req.(*SearchDataProcessingFunctionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataProcessingFunctionService_ViewDataProcessingFunction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewDataProcessingFunctionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataProcessingFunctionServiceServer).ViewDataProcessingFunction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataProcessingFunctionService_ViewDataProcessingFunction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataProcessingFunctionServiceServer).ViewDataProcessingFunction(ctx, req.(*ViewDataProcessingFunctionReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DataProcessingFunctionService_ServiceDesc is the grpc.ServiceDesc for DataProcessingFunctionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataProcessingFunctionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.DataProcessingFunctionService",
	HandlerType: (*DataProcessingFunctionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDataProcessingFunction",
			Handler:    _DataProcessingFunctionService_CreateDataProcessingFunction_Handler,
		},
		{
			MethodName: "RemoveDataProcessingFunction",
			Handler:    _DataProcessingFunctionService_RemoveDataProcessingFunction_Handler,
		},
		{
			MethodName: "ModifyDataProcessingFunction",
			Handler:    _DataProcessingFunctionService_ModifyDataProcessingFunction_Handler,
		},
		{
			MethodName: "SearchDataProcessingFunction",
			Handler:    _DataProcessingFunctionService_SearchDataProcessingFunction_Handler,
		},
		{
			MethodName: "ViewDataProcessingFunction",
			Handler:    _DataProcessingFunctionService_ViewDataProcessingFunction_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	GeneralConfigurationService_CreateGeneralConfiguration_FullMethodName = "/manager.GeneralConfigurationService/CreateGeneralConfiguration"
	GeneralConfigurationService_RemoveGeneralConfiguration_FullMethodName = "/manager.GeneralConfigurationService/RemoveGeneralConfiguration"
	GeneralConfigurationService_ModifyGeneralConfiguration_FullMethodName = "/manager.GeneralConfigurationService/ModifyGeneralConfiguration"
	GeneralConfigurationService_SearchGeneralConfiguration_FullMethodName = "/manager.GeneralConfigurationService/SearchGeneralConfiguration"
	GeneralConfigurationService_ViewGeneralConfiguration_FullMethodName   = "/manager.GeneralConfigurationService/ViewGeneralConfiguration"
)

// GeneralConfigurationServiceClient is the client API for GeneralConfigurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// GeneralConfigurationService 通用配置服务
type GeneralConfigurationServiceClient interface {
	//CreateGeneralConfiguration 创建通用配置
	CreateGeneralConfiguration(ctx context.Context, in *CreateGeneralConfigurationReq, opts ...grpc.CallOption) (*CreateGeneralConfigurationResp, error)
	//RemoveGeneralConfiguration 删除通用配置
	RemoveGeneralConfiguration(ctx context.Context, in *RemoveGeneralConfigurationReq, opts ...grpc.CallOption) (*RemoveGeneralConfigurationResp, error)
	//ModifyGeneralConfiguration 编辑通用配置
	ModifyGeneralConfiguration(ctx context.Context, in *ModifyGeneralConfigurationReq, opts ...grpc.CallOption) (*ModifyGeneralConfigurationResp, error)
	//SearchGeneralConfiguration 搜索通用配置
	SearchGeneralConfiguration(ctx context.Context, in *SearchGeneralConfigurationReq, opts ...grpc.CallOption) (*SearchGeneralConfigurationResp, error)
	//ViewGeneralConfiguration 查看通用配置
	ViewGeneralConfiguration(ctx context.Context, in *ViewGeneralConfigurationReq, opts ...grpc.CallOption) (*ViewGeneralConfigurationResp, error)
}

type generalConfigurationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGeneralConfigurationServiceClient(cc grpc.ClientConnInterface) GeneralConfigurationServiceClient {
	return &generalConfigurationServiceClient{cc}
}

func (c *generalConfigurationServiceClient) CreateGeneralConfiguration(ctx context.Context, in *CreateGeneralConfigurationReq, opts ...grpc.CallOption) (*CreateGeneralConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateGeneralConfigurationResp)
	err := c.cc.Invoke(ctx, GeneralConfigurationService_CreateGeneralConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *generalConfigurationServiceClient) RemoveGeneralConfiguration(ctx context.Context, in *RemoveGeneralConfigurationReq, opts ...grpc.CallOption) (*RemoveGeneralConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveGeneralConfigurationResp)
	err := c.cc.Invoke(ctx, GeneralConfigurationService_RemoveGeneralConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *generalConfigurationServiceClient) ModifyGeneralConfiguration(ctx context.Context, in *ModifyGeneralConfigurationReq, opts ...grpc.CallOption) (*ModifyGeneralConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyGeneralConfigurationResp)
	err := c.cc.Invoke(ctx, GeneralConfigurationService_ModifyGeneralConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *generalConfigurationServiceClient) SearchGeneralConfiguration(ctx context.Context, in *SearchGeneralConfigurationReq, opts ...grpc.CallOption) (*SearchGeneralConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchGeneralConfigurationResp)
	err := c.cc.Invoke(ctx, GeneralConfigurationService_SearchGeneralConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *generalConfigurationServiceClient) ViewGeneralConfiguration(ctx context.Context, in *ViewGeneralConfigurationReq, opts ...grpc.CallOption) (*ViewGeneralConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewGeneralConfigurationResp)
	err := c.cc.Invoke(ctx, GeneralConfigurationService_ViewGeneralConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GeneralConfigurationServiceServer is the server API for GeneralConfigurationService service.
// All implementations must embed UnimplementedGeneralConfigurationServiceServer
// for forward compatibility.
//
// GeneralConfigurationService 通用配置服务
type GeneralConfigurationServiceServer interface {
	//CreateGeneralConfiguration 创建通用配置
	CreateGeneralConfiguration(context.Context, *CreateGeneralConfigurationReq) (*CreateGeneralConfigurationResp, error)
	//RemoveGeneralConfiguration 删除通用配置
	RemoveGeneralConfiguration(context.Context, *RemoveGeneralConfigurationReq) (*RemoveGeneralConfigurationResp, error)
	//ModifyGeneralConfiguration 编辑通用配置
	ModifyGeneralConfiguration(context.Context, *ModifyGeneralConfigurationReq) (*ModifyGeneralConfigurationResp, error)
	//SearchGeneralConfiguration 搜索通用配置
	SearchGeneralConfiguration(context.Context, *SearchGeneralConfigurationReq) (*SearchGeneralConfigurationResp, error)
	//ViewGeneralConfiguration 查看通用配置
	ViewGeneralConfiguration(context.Context, *ViewGeneralConfigurationReq) (*ViewGeneralConfigurationResp, error)
	mustEmbedUnimplementedGeneralConfigurationServiceServer()
}

// UnimplementedGeneralConfigurationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGeneralConfigurationServiceServer struct{}

func (UnimplementedGeneralConfigurationServiceServer) CreateGeneralConfiguration(context.Context, *CreateGeneralConfigurationReq) (*CreateGeneralConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGeneralConfiguration not implemented")
}
func (UnimplementedGeneralConfigurationServiceServer) RemoveGeneralConfiguration(context.Context, *RemoveGeneralConfigurationReq) (*RemoveGeneralConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveGeneralConfiguration not implemented")
}
func (UnimplementedGeneralConfigurationServiceServer) ModifyGeneralConfiguration(context.Context, *ModifyGeneralConfigurationReq) (*ModifyGeneralConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyGeneralConfiguration not implemented")
}
func (UnimplementedGeneralConfigurationServiceServer) SearchGeneralConfiguration(context.Context, *SearchGeneralConfigurationReq) (*SearchGeneralConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchGeneralConfiguration not implemented")
}
func (UnimplementedGeneralConfigurationServiceServer) ViewGeneralConfiguration(context.Context, *ViewGeneralConfigurationReq) (*ViewGeneralConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewGeneralConfiguration not implemented")
}
func (UnimplementedGeneralConfigurationServiceServer) mustEmbedUnimplementedGeneralConfigurationServiceServer() {
}
func (UnimplementedGeneralConfigurationServiceServer) testEmbeddedByValue() {}

// UnsafeGeneralConfigurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GeneralConfigurationServiceServer will
// result in compilation errors.
type UnsafeGeneralConfigurationServiceServer interface {
	mustEmbedUnimplementedGeneralConfigurationServiceServer()
}

func RegisterGeneralConfigurationServiceServer(s grpc.ServiceRegistrar, srv GeneralConfigurationServiceServer) {
	// If the following call pancis, it indicates UnimplementedGeneralConfigurationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GeneralConfigurationService_ServiceDesc, srv)
}

func _GeneralConfigurationService_CreateGeneralConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGeneralConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GeneralConfigurationServiceServer).CreateGeneralConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GeneralConfigurationService_CreateGeneralConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GeneralConfigurationServiceServer).CreateGeneralConfiguration(ctx, req.(*CreateGeneralConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GeneralConfigurationService_RemoveGeneralConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveGeneralConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GeneralConfigurationServiceServer).RemoveGeneralConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GeneralConfigurationService_RemoveGeneralConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GeneralConfigurationServiceServer).RemoveGeneralConfiguration(ctx, req.(*RemoveGeneralConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GeneralConfigurationService_ModifyGeneralConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGeneralConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GeneralConfigurationServiceServer).ModifyGeneralConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GeneralConfigurationService_ModifyGeneralConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GeneralConfigurationServiceServer).ModifyGeneralConfiguration(ctx, req.(*ModifyGeneralConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GeneralConfigurationService_SearchGeneralConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchGeneralConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GeneralConfigurationServiceServer).SearchGeneralConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GeneralConfigurationService_SearchGeneralConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GeneralConfigurationServiceServer).SearchGeneralConfiguration(ctx, req.(*SearchGeneralConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GeneralConfigurationService_ViewGeneralConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewGeneralConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GeneralConfigurationServiceServer).ViewGeneralConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GeneralConfigurationService_ViewGeneralConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GeneralConfigurationServiceServer).ViewGeneralConfiguration(ctx, req.(*ViewGeneralConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GeneralConfigurationService_ServiceDesc is the grpc.ServiceDesc for GeneralConfigurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GeneralConfigurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.GeneralConfigurationService",
	HandlerType: (*GeneralConfigurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGeneralConfiguration",
			Handler:    _GeneralConfigurationService_CreateGeneralConfiguration_Handler,
		},
		{
			MethodName: "RemoveGeneralConfiguration",
			Handler:    _GeneralConfigurationService_RemoveGeneralConfiguration_Handler,
		},
		{
			MethodName: "ModifyGeneralConfiguration",
			Handler:    _GeneralConfigurationService_ModifyGeneralConfiguration_Handler,
		},
		{
			MethodName: "SearchGeneralConfiguration",
			Handler:    _GeneralConfigurationService_SearchGeneralConfiguration_Handler,
		},
		{
			MethodName: "ViewGeneralConfiguration",
			Handler:    _GeneralConfigurationService_ViewGeneralConfiguration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	AccountConfigurationService_CreateAccountConfiguration_FullMethodName = "/manager.AccountConfigurationService/CreateAccountConfiguration"
	AccountConfigurationService_RemoveAccountConfiguration_FullMethodName = "/manager.AccountConfigurationService/RemoveAccountConfiguration"
	AccountConfigurationService_ModifyAccountConfiguration_FullMethodName = "/manager.AccountConfigurationService/ModifyAccountConfiguration"
	AccountConfigurationService_SearchAccountConfiguration_FullMethodName = "/manager.AccountConfigurationService/SearchAccountConfiguration"
	AccountConfigurationService_ViewAccountConfiguration_FullMethodName   = "/manager.AccountConfigurationService/ViewAccountConfiguration"
)

// AccountConfigurationServiceClient is the client API for AccountConfigurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AccountConfigurationService 池账号配置服务
type AccountConfigurationServiceClient interface {
	//CreateAccountConfiguration 创建池账号配置
	CreateAccountConfiguration(ctx context.Context, in *CreateAccountConfigurationReq, opts ...grpc.CallOption) (*CreateAccountConfigurationResp, error)
	//RemoveAccountConfiguration 删除池账号配置
	RemoveAccountConfiguration(ctx context.Context, in *RemoveAccountConfigurationReq, opts ...grpc.CallOption) (*RemoveAccountConfigurationResp, error)
	//ModifyAccountConfiguration 编辑池账号配置
	ModifyAccountConfiguration(ctx context.Context, in *ModifyAccountConfigurationReq, opts ...grpc.CallOption) (*ModifyAccountConfigurationResp, error)
	//SearchAccountConfiguration 搜索池账号配置
	SearchAccountConfiguration(ctx context.Context, in *SearchAccountConfigurationReq, opts ...grpc.CallOption) (*SearchAccountConfigurationResp, error)
	//ViewAccountConfiguration 查看池账号配置
	ViewAccountConfiguration(ctx context.Context, in *ViewAccountConfigurationReq, opts ...grpc.CallOption) (*ViewAccountConfigurationResp, error)
}

type accountConfigurationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountConfigurationServiceClient(cc grpc.ClientConnInterface) AccountConfigurationServiceClient {
	return &accountConfigurationServiceClient{cc}
}

func (c *accountConfigurationServiceClient) CreateAccountConfiguration(ctx context.Context, in *CreateAccountConfigurationReq, opts ...grpc.CallOption) (*CreateAccountConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAccountConfigurationResp)
	err := c.cc.Invoke(ctx, AccountConfigurationService_CreateAccountConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountConfigurationServiceClient) RemoveAccountConfiguration(ctx context.Context, in *RemoveAccountConfigurationReq, opts ...grpc.CallOption) (*RemoveAccountConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveAccountConfigurationResp)
	err := c.cc.Invoke(ctx, AccountConfigurationService_RemoveAccountConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountConfigurationServiceClient) ModifyAccountConfiguration(ctx context.Context, in *ModifyAccountConfigurationReq, opts ...grpc.CallOption) (*ModifyAccountConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyAccountConfigurationResp)
	err := c.cc.Invoke(ctx, AccountConfigurationService_ModifyAccountConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountConfigurationServiceClient) SearchAccountConfiguration(ctx context.Context, in *SearchAccountConfigurationReq, opts ...grpc.CallOption) (*SearchAccountConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchAccountConfigurationResp)
	err := c.cc.Invoke(ctx, AccountConfigurationService_SearchAccountConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountConfigurationServiceClient) ViewAccountConfiguration(ctx context.Context, in *ViewAccountConfigurationReq, opts ...grpc.CallOption) (*ViewAccountConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewAccountConfigurationResp)
	err := c.cc.Invoke(ctx, AccountConfigurationService_ViewAccountConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountConfigurationServiceServer is the server API for AccountConfigurationService service.
// All implementations must embed UnimplementedAccountConfigurationServiceServer
// for forward compatibility.
//
// AccountConfigurationService 池账号配置服务
type AccountConfigurationServiceServer interface {
	//CreateAccountConfiguration 创建池账号配置
	CreateAccountConfiguration(context.Context, *CreateAccountConfigurationReq) (*CreateAccountConfigurationResp, error)
	//RemoveAccountConfiguration 删除池账号配置
	RemoveAccountConfiguration(context.Context, *RemoveAccountConfigurationReq) (*RemoveAccountConfigurationResp, error)
	//ModifyAccountConfiguration 编辑池账号配置
	ModifyAccountConfiguration(context.Context, *ModifyAccountConfigurationReq) (*ModifyAccountConfigurationResp, error)
	//SearchAccountConfiguration 搜索池账号配置
	SearchAccountConfiguration(context.Context, *SearchAccountConfigurationReq) (*SearchAccountConfigurationResp, error)
	//ViewAccountConfiguration 查看池账号配置
	ViewAccountConfiguration(context.Context, *ViewAccountConfigurationReq) (*ViewAccountConfigurationResp, error)
	mustEmbedUnimplementedAccountConfigurationServiceServer()
}

// UnimplementedAccountConfigurationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAccountConfigurationServiceServer struct{}

func (UnimplementedAccountConfigurationServiceServer) CreateAccountConfiguration(context.Context, *CreateAccountConfigurationReq) (*CreateAccountConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccountConfiguration not implemented")
}
func (UnimplementedAccountConfigurationServiceServer) RemoveAccountConfiguration(context.Context, *RemoveAccountConfigurationReq) (*RemoveAccountConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAccountConfiguration not implemented")
}
func (UnimplementedAccountConfigurationServiceServer) ModifyAccountConfiguration(context.Context, *ModifyAccountConfigurationReq) (*ModifyAccountConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyAccountConfiguration not implemented")
}
func (UnimplementedAccountConfigurationServiceServer) SearchAccountConfiguration(context.Context, *SearchAccountConfigurationReq) (*SearchAccountConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAccountConfiguration not implemented")
}
func (UnimplementedAccountConfigurationServiceServer) ViewAccountConfiguration(context.Context, *ViewAccountConfigurationReq) (*ViewAccountConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewAccountConfiguration not implemented")
}
func (UnimplementedAccountConfigurationServiceServer) mustEmbedUnimplementedAccountConfigurationServiceServer() {
}
func (UnimplementedAccountConfigurationServiceServer) testEmbeddedByValue() {}

// UnsafeAccountConfigurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountConfigurationServiceServer will
// result in compilation errors.
type UnsafeAccountConfigurationServiceServer interface {
	mustEmbedUnimplementedAccountConfigurationServiceServer()
}

func RegisterAccountConfigurationServiceServer(s grpc.ServiceRegistrar, srv AccountConfigurationServiceServer) {
	// If the following call pancis, it indicates UnimplementedAccountConfigurationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AccountConfigurationService_ServiceDesc, srv)
}

func _AccountConfigurationService_CreateAccountConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountConfigurationServiceServer).CreateAccountConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountConfigurationService_CreateAccountConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountConfigurationServiceServer).CreateAccountConfiguration(ctx, req.(*CreateAccountConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountConfigurationService_RemoveAccountConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveAccountConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountConfigurationServiceServer).RemoveAccountConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountConfigurationService_RemoveAccountConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountConfigurationServiceServer).RemoveAccountConfiguration(ctx, req.(*RemoveAccountConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountConfigurationService_ModifyAccountConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyAccountConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountConfigurationServiceServer).ModifyAccountConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountConfigurationService_ModifyAccountConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountConfigurationServiceServer).ModifyAccountConfiguration(ctx, req.(*ModifyAccountConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountConfigurationService_SearchAccountConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAccountConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountConfigurationServiceServer).SearchAccountConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountConfigurationService_SearchAccountConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountConfigurationServiceServer).SearchAccountConfiguration(ctx, req.(*SearchAccountConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountConfigurationService_ViewAccountConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewAccountConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountConfigurationServiceServer).ViewAccountConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountConfigurationService_ViewAccountConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountConfigurationServiceServer).ViewAccountConfiguration(ctx, req.(*ViewAccountConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountConfigurationService_ServiceDesc is the grpc.ServiceDesc for AccountConfigurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountConfigurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.AccountConfigurationService",
	HandlerType: (*AccountConfigurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAccountConfiguration",
			Handler:    _AccountConfigurationService_CreateAccountConfiguration_Handler,
		},
		{
			MethodName: "RemoveAccountConfiguration",
			Handler:    _AccountConfigurationService_RemoveAccountConfiguration_Handler,
		},
		{
			MethodName: "ModifyAccountConfiguration",
			Handler:    _AccountConfigurationService_ModifyAccountConfiguration_Handler,
		},
		{
			MethodName: "SearchAccountConfiguration",
			Handler:    _AccountConfigurationService_SearchAccountConfiguration_Handler,
		},
		{
			MethodName: "ViewAccountConfiguration",
			Handler:    _AccountConfigurationService_ViewAccountConfiguration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	InterfaceDefinitionService_LocalImportInterfaceDefinition_FullMethodName        = "/manager.InterfaceDefinitionService/LocalImportInterfaceDefinition"
	InterfaceDefinitionService_CreateInterfaceDocument_FullMethodName               = "/manager.InterfaceDefinitionService/CreateInterfaceDocument"
	InterfaceDefinitionService_RemoveInterfaceDocument_FullMethodName               = "/manager.InterfaceDefinitionService/RemoveInterfaceDocument"
	InterfaceDefinitionService_ModifyInterfaceDocument_FullMethodName               = "/manager.InterfaceDefinitionService/ModifyInterfaceDocument"
	InterfaceDefinitionService_SearchInterfaceDocument_FullMethodName               = "/manager.InterfaceDefinitionService/SearchInterfaceDocument"
	InterfaceDefinitionService_ViewInterfaceDocument_FullMethodName                 = "/manager.InterfaceDefinitionService/ViewInterfaceDocument"
	InterfaceDefinitionService_MockInterfaceDocument_FullMethodName                 = "/manager.InterfaceDefinitionService/MockInterfaceDocument"
	InterfaceDefinitionService_SearchInterfaceDocumentReference_FullMethodName      = "/manager.InterfaceDefinitionService/SearchInterfaceDocumentReference"
	InterfaceDefinitionService_ModifyInterfaceDocumentReferenceState_FullMethodName = "/manager.InterfaceDefinitionService/ModifyInterfaceDocumentReferenceState"
	InterfaceDefinitionService_CreateInterfaceSchema_FullMethodName                 = "/manager.InterfaceDefinitionService/CreateInterfaceSchema"
	InterfaceDefinitionService_RemoveInterfaceSchema_FullMethodName                 = "/manager.InterfaceDefinitionService/RemoveInterfaceSchema"
	InterfaceDefinitionService_ModifyInterfaceSchema_FullMethodName                 = "/manager.InterfaceDefinitionService/ModifyInterfaceSchema"
	InterfaceDefinitionService_SearchInterfaceSchema_FullMethodName                 = "/manager.InterfaceDefinitionService/SearchInterfaceSchema"
	InterfaceDefinitionService_ViewInterfaceSchema_FullMethodName                   = "/manager.InterfaceDefinitionService/ViewInterfaceSchema"
	InterfaceDefinitionService_CreateInterfaceConfig_FullMethodName                 = "/manager.InterfaceDefinitionService/CreateInterfaceConfig"
	InterfaceDefinitionService_RemoveInterfaceConfig_FullMethodName                 = "/manager.InterfaceDefinitionService/RemoveInterfaceConfig"
	InterfaceDefinitionService_ModifyInterfaceConfig_FullMethodName                 = "/manager.InterfaceDefinitionService/ModifyInterfaceConfig"
	InterfaceDefinitionService_SearchInterfaceConfig_FullMethodName                 = "/manager.InterfaceDefinitionService/SearchInterfaceConfig"
	InterfaceDefinitionService_ViewInterfaceConfig_FullMethodName                   = "/manager.InterfaceDefinitionService/ViewInterfaceConfig"
	InterfaceDefinitionService_CreateInterfaceCase_FullMethodName                   = "/manager.InterfaceDefinitionService/CreateInterfaceCase"
	InterfaceDefinitionService_RemoveInterfaceCase_FullMethodName                   = "/manager.InterfaceDefinitionService/RemoveInterfaceCase"
	InterfaceDefinitionService_ModifyInterfaceCase_FullMethodName                   = "/manager.InterfaceDefinitionService/ModifyInterfaceCase"
	InterfaceDefinitionService_SearchInterfaceCase_FullMethodName                   = "/manager.InterfaceDefinitionService/SearchInterfaceCase"
	InterfaceDefinitionService_ViewInterfaceCase_FullMethodName                     = "/manager.InterfaceDefinitionService/ViewInterfaceCase"
	InterfaceDefinitionService_MaintainInterfaceCase_FullMethodName                 = "/manager.InterfaceDefinitionService/MaintainInterfaceCase"
	InterfaceDefinitionService_PublishInterfaceCase_FullMethodName                  = "/manager.InterfaceDefinitionService/PublishInterfaceCase"
	InterfaceDefinitionService_SearchInterfaceCaseReference_FullMethodName          = "/manager.InterfaceDefinitionService/SearchInterfaceCaseReference"
	InterfaceDefinitionService_UpdateInterfaceCoverageData_FullMethodName           = "/manager.InterfaceDefinitionService/UpdateInterfaceCoverageData"
	InterfaceDefinitionService_GetInterfaceCoverageTeams_FullMethodName             = "/manager.InterfaceDefinitionService/GetInterfaceCoverageTeams"
	InterfaceDefinitionService_GetInterfaceCoverageData_FullMethodName              = "/manager.InterfaceDefinitionService/GetInterfaceCoverageData"
	InterfaceDefinitionService_UpdateInterfaceDocumentTags_FullMethodName           = "/manager.InterfaceDefinitionService/UpdateInterfaceDocumentTags"
)

// InterfaceDefinitionServiceClient is the client API for InterfaceDefinitionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// InterfaceDefinitionService 接口定义服务
type InterfaceDefinitionServiceClient interface {
	//LocalImportInterfaceDefinition 本地导入接口定义
	LocalImportInterfaceDefinition(ctx context.Context, in *LocalImportInterfaceDefinitionReq, opts ...grpc.CallOption) (*LocalImportInterfaceDefinitionResp, error)
	//CreateInterfaceDocument 创建接口文档
	CreateInterfaceDocument(ctx context.Context, in *CreateInterfaceDocumentReq, opts ...grpc.CallOption) (*CreateInterfaceDocumentResp, error)
	//RemoveInterfaceDocument 删除接口文档
	RemoveInterfaceDocument(ctx context.Context, in *RemoveInterfaceDocumentReq, opts ...grpc.CallOption) (*RemoveInterfaceDocumentResp, error)
	//ModifyInterfaceDocument 编辑接口文档
	ModifyInterfaceDocument(ctx context.Context, in *ModifyInterfaceDocumentReq, opts ...grpc.CallOption) (*ModifyInterfaceDocumentResp, error)
	//SearchInterfaceDocument 搜索接口文档
	SearchInterfaceDocument(ctx context.Context, in *SearchInterfaceDocumentReq, opts ...grpc.CallOption) (*SearchInterfaceDocumentResp, error)
	//ViewInterfaceDocument 查看接口文档
	ViewInterfaceDocument(ctx context.Context, in *ViewInterfaceDocumentReq, opts ...grpc.CallOption) (*ViewInterfaceDocumentResp, error)
	//MockInterfaceDocument 根据接口文档生成接口用例数据
	MockInterfaceDocument(ctx context.Context, in *MockInterfaceDocumentReq, opts ...grpc.CallOption) (*MockInterfaceDocumentResp, error)
	//SearchInterfaceDocumentReference 搜索接口集合引用详情
	SearchInterfaceDocumentReference(ctx context.Context, in *SearchInterfaceDocumentReferenceReq, opts ...grpc.CallOption) (*SearchInterfaceDocumentReferenceResp, error)
	//ModifyInterfaceDocumentReferenceState 修改接口集合所在的API计划的引用状态
	ModifyInterfaceDocumentReferenceState(ctx context.Context, in *ModifyInterfaceDocumentReferenceStateReq, opts ...grpc.CallOption) (*ModifyInterfaceDocumentReferenceStateResp, error)
	//CreateInterfaceSchema 创建接口数据模型
	CreateInterfaceSchema(ctx context.Context, in *CreateInterfaceSchemaReq, opts ...grpc.CallOption) (*CreateInterfaceSchemaResp, error)
	//RemoveInterfaceSchema 删除接口数据模型
	RemoveInterfaceSchema(ctx context.Context, in *RemoveInterfaceSchemaReq, opts ...grpc.CallOption) (*RemoveInterfaceSchemaResp, error)
	//ModifyInterfaceSchema 编辑接口数据模型
	ModifyInterfaceSchema(ctx context.Context, in *ModifyInterfaceSchemaReq, opts ...grpc.CallOption) (*ModifyInterfaceSchemaResp, error)
	//SearchInterfaceSchema 搜索接口数据模型
	SearchInterfaceSchema(ctx context.Context, in *SearchInterfaceSchemaReq, opts ...grpc.CallOption) (*SearchInterfaceSchemaResp, error)
	//ViewInterfaceSchema 查看接口数据模型
	ViewInterfaceSchema(ctx context.Context, in *ViewInterfaceSchemaReq, opts ...grpc.CallOption) (*ViewInterfaceSchemaResp, error)
	//CreateInterfaceConfig 创建接口配置
	CreateInterfaceConfig(ctx context.Context, in *CreateInterfaceConfigReq, opts ...grpc.CallOption) (*CreateInterfaceConfigResp, error)
	//RemoveInterfaceConfig 删除接口配置
	RemoveInterfaceConfig(ctx context.Context, in *RemoveInterfaceConfigReq, opts ...grpc.CallOption) (*RemoveInterfaceConfigResp, error)
	//ModifyInterfaceConfig 编辑接口配置
	ModifyInterfaceConfig(ctx context.Context, in *ModifyInterfaceConfigReq, opts ...grpc.CallOption) (*ModifyInterfaceConfigResp, error)
	//SearchInterfaceConfig 搜索接口配置
	SearchInterfaceConfig(ctx context.Context, in *SearchInterfaceConfigReq, opts ...grpc.CallOption) (*SearchInterfaceConfigResp, error)
	//ViewInterfaceConfig 查看接口配置
	ViewInterfaceConfig(ctx context.Context, in *ViewInterfaceConfigReq, opts ...grpc.CallOption) (*ViewInterfaceConfigResp, error)
	//CreateInterfaceCase 创建接口用例
	CreateInterfaceCase(ctx context.Context, in *CreateInterfaceCaseReq, opts ...grpc.CallOption) (*CreateInterfaceCaseResp, error)
	//RemoveInterfaceCase 删除接口用例
	RemoveInterfaceCase(ctx context.Context, in *RemoveInterfaceCaseReq, opts ...grpc.CallOption) (*RemoveInterfaceCaseResp, error)
	//ModifyInterfaceCase 编辑接口用例
	ModifyInterfaceCase(ctx context.Context, in *ModifyInterfaceCaseReq, opts ...grpc.CallOption) (*ModifyInterfaceCaseResp, error)
	//SearchInterfaceCase 搜索接口用例
	SearchInterfaceCase(ctx context.Context, in *SearchInterfaceCaseReq, opts ...grpc.CallOption) (*SearchInterfaceCaseResp, error)
	//ViewInterfaceCase 查看接口用例
	ViewInterfaceCase(ctx context.Context, in *ViewInterfaceCaseReq, opts ...grpc.CallOption) (*ViewInterfaceCaseResp, error)
	//MaintainInterfaceCase 维护接口用例
	MaintainInterfaceCase(ctx context.Context, in *MaintainInterfaceCaseReq, opts ...grpc.CallOption) (*MaintainInterfaceCaseResp, error)
	//PublishInterfaceCase 发布接口用例
	PublishInterfaceCase(ctx context.Context, in *PublishInterfaceCaseReq, opts ...grpc.CallOption) (*PublishInterfaceCaseResp, error)
	//SearchInterfaceCaseReference 搜索接口用例引用详情
	SearchInterfaceCaseReference(ctx context.Context, in *SearchInterfaceCaseReferenceReq, opts ...grpc.CallOption) (*SearchInterfaceCaseReferenceResp, error)
	//UpdateInterfaceCoverageData 更新接口覆盖率数据
	UpdateInterfaceCoverageData(ctx context.Context, in *UpdateInterfaceCoverageDataReq, opts ...grpc.CallOption) (*UpdateInterfaceCoverageDataResp, error)
	//GetInterfaceCoverageTeams 获取接口覆盖率相关的团队
	GetInterfaceCoverageTeams(ctx context.Context, in *GetInterfaceCoverageTeamsReq, opts ...grpc.CallOption) (*GetInterfaceCoverageTeamsResp, error)
	//GetInterfaceCoverageData 获取接口覆盖率数据
	GetInterfaceCoverageData(ctx context.Context, in *GetInterfaceCoverageDataReq, opts ...grpc.CallOption) (*GetInterfaceCoverageDataResp, error)
	//UpdateInterfaceDocumentTags 更新接口文档标签
	UpdateInterfaceDocumentTags(ctx context.Context, in *UpdateInterfaceDocumentTagsReq, opts ...grpc.CallOption) (*UpdateInterfaceDocumentTagsResp, error)
}

type interfaceDefinitionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewInterfaceDefinitionServiceClient(cc grpc.ClientConnInterface) InterfaceDefinitionServiceClient {
	return &interfaceDefinitionServiceClient{cc}
}

func (c *interfaceDefinitionServiceClient) LocalImportInterfaceDefinition(ctx context.Context, in *LocalImportInterfaceDefinitionReq, opts ...grpc.CallOption) (*LocalImportInterfaceDefinitionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LocalImportInterfaceDefinitionResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_LocalImportInterfaceDefinition_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) CreateInterfaceDocument(ctx context.Context, in *CreateInterfaceDocumentReq, opts ...grpc.CallOption) (*CreateInterfaceDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateInterfaceDocumentResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_CreateInterfaceDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) RemoveInterfaceDocument(ctx context.Context, in *RemoveInterfaceDocumentReq, opts ...grpc.CallOption) (*RemoveInterfaceDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveInterfaceDocumentResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_RemoveInterfaceDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ModifyInterfaceDocument(ctx context.Context, in *ModifyInterfaceDocumentReq, opts ...grpc.CallOption) (*ModifyInterfaceDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyInterfaceDocumentResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ModifyInterfaceDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) SearchInterfaceDocument(ctx context.Context, in *SearchInterfaceDocumentReq, opts ...grpc.CallOption) (*SearchInterfaceDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchInterfaceDocumentResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_SearchInterfaceDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ViewInterfaceDocument(ctx context.Context, in *ViewInterfaceDocumentReq, opts ...grpc.CallOption) (*ViewInterfaceDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewInterfaceDocumentResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ViewInterfaceDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) MockInterfaceDocument(ctx context.Context, in *MockInterfaceDocumentReq, opts ...grpc.CallOption) (*MockInterfaceDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MockInterfaceDocumentResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_MockInterfaceDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) SearchInterfaceDocumentReference(ctx context.Context, in *SearchInterfaceDocumentReferenceReq, opts ...grpc.CallOption) (*SearchInterfaceDocumentReferenceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchInterfaceDocumentReferenceResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_SearchInterfaceDocumentReference_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ModifyInterfaceDocumentReferenceState(ctx context.Context, in *ModifyInterfaceDocumentReferenceStateReq, opts ...grpc.CallOption) (*ModifyInterfaceDocumentReferenceStateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyInterfaceDocumentReferenceStateResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ModifyInterfaceDocumentReferenceState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) CreateInterfaceSchema(ctx context.Context, in *CreateInterfaceSchemaReq, opts ...grpc.CallOption) (*CreateInterfaceSchemaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateInterfaceSchemaResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_CreateInterfaceSchema_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) RemoveInterfaceSchema(ctx context.Context, in *RemoveInterfaceSchemaReq, opts ...grpc.CallOption) (*RemoveInterfaceSchemaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveInterfaceSchemaResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_RemoveInterfaceSchema_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ModifyInterfaceSchema(ctx context.Context, in *ModifyInterfaceSchemaReq, opts ...grpc.CallOption) (*ModifyInterfaceSchemaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyInterfaceSchemaResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ModifyInterfaceSchema_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) SearchInterfaceSchema(ctx context.Context, in *SearchInterfaceSchemaReq, opts ...grpc.CallOption) (*SearchInterfaceSchemaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchInterfaceSchemaResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_SearchInterfaceSchema_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ViewInterfaceSchema(ctx context.Context, in *ViewInterfaceSchemaReq, opts ...grpc.CallOption) (*ViewInterfaceSchemaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewInterfaceSchemaResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ViewInterfaceSchema_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) CreateInterfaceConfig(ctx context.Context, in *CreateInterfaceConfigReq, opts ...grpc.CallOption) (*CreateInterfaceConfigResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateInterfaceConfigResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_CreateInterfaceConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) RemoveInterfaceConfig(ctx context.Context, in *RemoveInterfaceConfigReq, opts ...grpc.CallOption) (*RemoveInterfaceConfigResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveInterfaceConfigResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_RemoveInterfaceConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ModifyInterfaceConfig(ctx context.Context, in *ModifyInterfaceConfigReq, opts ...grpc.CallOption) (*ModifyInterfaceConfigResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyInterfaceConfigResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ModifyInterfaceConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) SearchInterfaceConfig(ctx context.Context, in *SearchInterfaceConfigReq, opts ...grpc.CallOption) (*SearchInterfaceConfigResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchInterfaceConfigResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_SearchInterfaceConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ViewInterfaceConfig(ctx context.Context, in *ViewInterfaceConfigReq, opts ...grpc.CallOption) (*ViewInterfaceConfigResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewInterfaceConfigResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ViewInterfaceConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) CreateInterfaceCase(ctx context.Context, in *CreateInterfaceCaseReq, opts ...grpc.CallOption) (*CreateInterfaceCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateInterfaceCaseResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_CreateInterfaceCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) RemoveInterfaceCase(ctx context.Context, in *RemoveInterfaceCaseReq, opts ...grpc.CallOption) (*RemoveInterfaceCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveInterfaceCaseResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_RemoveInterfaceCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ModifyInterfaceCase(ctx context.Context, in *ModifyInterfaceCaseReq, opts ...grpc.CallOption) (*ModifyInterfaceCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyInterfaceCaseResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ModifyInterfaceCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) SearchInterfaceCase(ctx context.Context, in *SearchInterfaceCaseReq, opts ...grpc.CallOption) (*SearchInterfaceCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchInterfaceCaseResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_SearchInterfaceCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) ViewInterfaceCase(ctx context.Context, in *ViewInterfaceCaseReq, opts ...grpc.CallOption) (*ViewInterfaceCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewInterfaceCaseResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_ViewInterfaceCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) MaintainInterfaceCase(ctx context.Context, in *MaintainInterfaceCaseReq, opts ...grpc.CallOption) (*MaintainInterfaceCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MaintainInterfaceCaseResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_MaintainInterfaceCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) PublishInterfaceCase(ctx context.Context, in *PublishInterfaceCaseReq, opts ...grpc.CallOption) (*PublishInterfaceCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PublishInterfaceCaseResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_PublishInterfaceCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) SearchInterfaceCaseReference(ctx context.Context, in *SearchInterfaceCaseReferenceReq, opts ...grpc.CallOption) (*SearchInterfaceCaseReferenceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchInterfaceCaseReferenceResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_SearchInterfaceCaseReference_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) UpdateInterfaceCoverageData(ctx context.Context, in *UpdateInterfaceCoverageDataReq, opts ...grpc.CallOption) (*UpdateInterfaceCoverageDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateInterfaceCoverageDataResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_UpdateInterfaceCoverageData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) GetInterfaceCoverageTeams(ctx context.Context, in *GetInterfaceCoverageTeamsReq, opts ...grpc.CallOption) (*GetInterfaceCoverageTeamsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetInterfaceCoverageTeamsResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_GetInterfaceCoverageTeams_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) GetInterfaceCoverageData(ctx context.Context, in *GetInterfaceCoverageDataReq, opts ...grpc.CallOption) (*GetInterfaceCoverageDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetInterfaceCoverageDataResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_GetInterfaceCoverageData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interfaceDefinitionServiceClient) UpdateInterfaceDocumentTags(ctx context.Context, in *UpdateInterfaceDocumentTagsReq, opts ...grpc.CallOption) (*UpdateInterfaceDocumentTagsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateInterfaceDocumentTagsResp)
	err := c.cc.Invoke(ctx, InterfaceDefinitionService_UpdateInterfaceDocumentTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InterfaceDefinitionServiceServer is the server API for InterfaceDefinitionService service.
// All implementations must embed UnimplementedInterfaceDefinitionServiceServer
// for forward compatibility.
//
// InterfaceDefinitionService 接口定义服务
type InterfaceDefinitionServiceServer interface {
	//LocalImportInterfaceDefinition 本地导入接口定义
	LocalImportInterfaceDefinition(context.Context, *LocalImportInterfaceDefinitionReq) (*LocalImportInterfaceDefinitionResp, error)
	//CreateInterfaceDocument 创建接口文档
	CreateInterfaceDocument(context.Context, *CreateInterfaceDocumentReq) (*CreateInterfaceDocumentResp, error)
	//RemoveInterfaceDocument 删除接口文档
	RemoveInterfaceDocument(context.Context, *RemoveInterfaceDocumentReq) (*RemoveInterfaceDocumentResp, error)
	//ModifyInterfaceDocument 编辑接口文档
	ModifyInterfaceDocument(context.Context, *ModifyInterfaceDocumentReq) (*ModifyInterfaceDocumentResp, error)
	//SearchInterfaceDocument 搜索接口文档
	SearchInterfaceDocument(context.Context, *SearchInterfaceDocumentReq) (*SearchInterfaceDocumentResp, error)
	//ViewInterfaceDocument 查看接口文档
	ViewInterfaceDocument(context.Context, *ViewInterfaceDocumentReq) (*ViewInterfaceDocumentResp, error)
	//MockInterfaceDocument 根据接口文档生成接口用例数据
	MockInterfaceDocument(context.Context, *MockInterfaceDocumentReq) (*MockInterfaceDocumentResp, error)
	//SearchInterfaceDocumentReference 搜索接口集合引用详情
	SearchInterfaceDocumentReference(context.Context, *SearchInterfaceDocumentReferenceReq) (*SearchInterfaceDocumentReferenceResp, error)
	//ModifyInterfaceDocumentReferenceState 修改接口集合所在的API计划的引用状态
	ModifyInterfaceDocumentReferenceState(context.Context, *ModifyInterfaceDocumentReferenceStateReq) (*ModifyInterfaceDocumentReferenceStateResp, error)
	//CreateInterfaceSchema 创建接口数据模型
	CreateInterfaceSchema(context.Context, *CreateInterfaceSchemaReq) (*CreateInterfaceSchemaResp, error)
	//RemoveInterfaceSchema 删除接口数据模型
	RemoveInterfaceSchema(context.Context, *RemoveInterfaceSchemaReq) (*RemoveInterfaceSchemaResp, error)
	//ModifyInterfaceSchema 编辑接口数据模型
	ModifyInterfaceSchema(context.Context, *ModifyInterfaceSchemaReq) (*ModifyInterfaceSchemaResp, error)
	//SearchInterfaceSchema 搜索接口数据模型
	SearchInterfaceSchema(context.Context, *SearchInterfaceSchemaReq) (*SearchInterfaceSchemaResp, error)
	//ViewInterfaceSchema 查看接口数据模型
	ViewInterfaceSchema(context.Context, *ViewInterfaceSchemaReq) (*ViewInterfaceSchemaResp, error)
	//CreateInterfaceConfig 创建接口配置
	CreateInterfaceConfig(context.Context, *CreateInterfaceConfigReq) (*CreateInterfaceConfigResp, error)
	//RemoveInterfaceConfig 删除接口配置
	RemoveInterfaceConfig(context.Context, *RemoveInterfaceConfigReq) (*RemoveInterfaceConfigResp, error)
	//ModifyInterfaceConfig 编辑接口配置
	ModifyInterfaceConfig(context.Context, *ModifyInterfaceConfigReq) (*ModifyInterfaceConfigResp, error)
	//SearchInterfaceConfig 搜索接口配置
	SearchInterfaceConfig(context.Context, *SearchInterfaceConfigReq) (*SearchInterfaceConfigResp, error)
	//ViewInterfaceConfig 查看接口配置
	ViewInterfaceConfig(context.Context, *ViewInterfaceConfigReq) (*ViewInterfaceConfigResp, error)
	//CreateInterfaceCase 创建接口用例
	CreateInterfaceCase(context.Context, *CreateInterfaceCaseReq) (*CreateInterfaceCaseResp, error)
	//RemoveInterfaceCase 删除接口用例
	RemoveInterfaceCase(context.Context, *RemoveInterfaceCaseReq) (*RemoveInterfaceCaseResp, error)
	//ModifyInterfaceCase 编辑接口用例
	ModifyInterfaceCase(context.Context, *ModifyInterfaceCaseReq) (*ModifyInterfaceCaseResp, error)
	//SearchInterfaceCase 搜索接口用例
	SearchInterfaceCase(context.Context, *SearchInterfaceCaseReq) (*SearchInterfaceCaseResp, error)
	//ViewInterfaceCase 查看接口用例
	ViewInterfaceCase(context.Context, *ViewInterfaceCaseReq) (*ViewInterfaceCaseResp, error)
	//MaintainInterfaceCase 维护接口用例
	MaintainInterfaceCase(context.Context, *MaintainInterfaceCaseReq) (*MaintainInterfaceCaseResp, error)
	//PublishInterfaceCase 发布接口用例
	PublishInterfaceCase(context.Context, *PublishInterfaceCaseReq) (*PublishInterfaceCaseResp, error)
	//SearchInterfaceCaseReference 搜索接口用例引用详情
	SearchInterfaceCaseReference(context.Context, *SearchInterfaceCaseReferenceReq) (*SearchInterfaceCaseReferenceResp, error)
	//UpdateInterfaceCoverageData 更新接口覆盖率数据
	UpdateInterfaceCoverageData(context.Context, *UpdateInterfaceCoverageDataReq) (*UpdateInterfaceCoverageDataResp, error)
	//GetInterfaceCoverageTeams 获取接口覆盖率相关的团队
	GetInterfaceCoverageTeams(context.Context, *GetInterfaceCoverageTeamsReq) (*GetInterfaceCoverageTeamsResp, error)
	//GetInterfaceCoverageData 获取接口覆盖率数据
	GetInterfaceCoverageData(context.Context, *GetInterfaceCoverageDataReq) (*GetInterfaceCoverageDataResp, error)
	//UpdateInterfaceDocumentTags 更新接口文档标签
	UpdateInterfaceDocumentTags(context.Context, *UpdateInterfaceDocumentTagsReq) (*UpdateInterfaceDocumentTagsResp, error)
	mustEmbedUnimplementedInterfaceDefinitionServiceServer()
}

// UnimplementedInterfaceDefinitionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedInterfaceDefinitionServiceServer struct{}

func (UnimplementedInterfaceDefinitionServiceServer) LocalImportInterfaceDefinition(context.Context, *LocalImportInterfaceDefinitionReq) (*LocalImportInterfaceDefinitionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LocalImportInterfaceDefinition not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) CreateInterfaceDocument(context.Context, *CreateInterfaceDocumentReq) (*CreateInterfaceDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInterfaceDocument not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) RemoveInterfaceDocument(context.Context, *RemoveInterfaceDocumentReq) (*RemoveInterfaceDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveInterfaceDocument not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ModifyInterfaceDocument(context.Context, *ModifyInterfaceDocumentReq) (*ModifyInterfaceDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyInterfaceDocument not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) SearchInterfaceDocument(context.Context, *SearchInterfaceDocumentReq) (*SearchInterfaceDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchInterfaceDocument not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ViewInterfaceDocument(context.Context, *ViewInterfaceDocumentReq) (*ViewInterfaceDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewInterfaceDocument not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) MockInterfaceDocument(context.Context, *MockInterfaceDocumentReq) (*MockInterfaceDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MockInterfaceDocument not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) SearchInterfaceDocumentReference(context.Context, *SearchInterfaceDocumentReferenceReq) (*SearchInterfaceDocumentReferenceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchInterfaceDocumentReference not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ModifyInterfaceDocumentReferenceState(context.Context, *ModifyInterfaceDocumentReferenceStateReq) (*ModifyInterfaceDocumentReferenceStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyInterfaceDocumentReferenceState not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) CreateInterfaceSchema(context.Context, *CreateInterfaceSchemaReq) (*CreateInterfaceSchemaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInterfaceSchema not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) RemoveInterfaceSchema(context.Context, *RemoveInterfaceSchemaReq) (*RemoveInterfaceSchemaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveInterfaceSchema not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ModifyInterfaceSchema(context.Context, *ModifyInterfaceSchemaReq) (*ModifyInterfaceSchemaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyInterfaceSchema not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) SearchInterfaceSchema(context.Context, *SearchInterfaceSchemaReq) (*SearchInterfaceSchemaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchInterfaceSchema not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ViewInterfaceSchema(context.Context, *ViewInterfaceSchemaReq) (*ViewInterfaceSchemaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewInterfaceSchema not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) CreateInterfaceConfig(context.Context, *CreateInterfaceConfigReq) (*CreateInterfaceConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInterfaceConfig not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) RemoveInterfaceConfig(context.Context, *RemoveInterfaceConfigReq) (*RemoveInterfaceConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveInterfaceConfig not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ModifyInterfaceConfig(context.Context, *ModifyInterfaceConfigReq) (*ModifyInterfaceConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyInterfaceConfig not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) SearchInterfaceConfig(context.Context, *SearchInterfaceConfigReq) (*SearchInterfaceConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchInterfaceConfig not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ViewInterfaceConfig(context.Context, *ViewInterfaceConfigReq) (*ViewInterfaceConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewInterfaceConfig not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) CreateInterfaceCase(context.Context, *CreateInterfaceCaseReq) (*CreateInterfaceCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInterfaceCase not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) RemoveInterfaceCase(context.Context, *RemoveInterfaceCaseReq) (*RemoveInterfaceCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveInterfaceCase not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ModifyInterfaceCase(context.Context, *ModifyInterfaceCaseReq) (*ModifyInterfaceCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyInterfaceCase not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) SearchInterfaceCase(context.Context, *SearchInterfaceCaseReq) (*SearchInterfaceCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchInterfaceCase not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) ViewInterfaceCase(context.Context, *ViewInterfaceCaseReq) (*ViewInterfaceCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewInterfaceCase not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) MaintainInterfaceCase(context.Context, *MaintainInterfaceCaseReq) (*MaintainInterfaceCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MaintainInterfaceCase not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) PublishInterfaceCase(context.Context, *PublishInterfaceCaseReq) (*PublishInterfaceCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishInterfaceCase not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) SearchInterfaceCaseReference(context.Context, *SearchInterfaceCaseReferenceReq) (*SearchInterfaceCaseReferenceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchInterfaceCaseReference not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) UpdateInterfaceCoverageData(context.Context, *UpdateInterfaceCoverageDataReq) (*UpdateInterfaceCoverageDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInterfaceCoverageData not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) GetInterfaceCoverageTeams(context.Context, *GetInterfaceCoverageTeamsReq) (*GetInterfaceCoverageTeamsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInterfaceCoverageTeams not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) GetInterfaceCoverageData(context.Context, *GetInterfaceCoverageDataReq) (*GetInterfaceCoverageDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInterfaceCoverageData not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) UpdateInterfaceDocumentTags(context.Context, *UpdateInterfaceDocumentTagsReq) (*UpdateInterfaceDocumentTagsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInterfaceDocumentTags not implemented")
}
func (UnimplementedInterfaceDefinitionServiceServer) mustEmbedUnimplementedInterfaceDefinitionServiceServer() {
}
func (UnimplementedInterfaceDefinitionServiceServer) testEmbeddedByValue() {}

// UnsafeInterfaceDefinitionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InterfaceDefinitionServiceServer will
// result in compilation errors.
type UnsafeInterfaceDefinitionServiceServer interface {
	mustEmbedUnimplementedInterfaceDefinitionServiceServer()
}

func RegisterInterfaceDefinitionServiceServer(s grpc.ServiceRegistrar, srv InterfaceDefinitionServiceServer) {
	// If the following call pancis, it indicates UnimplementedInterfaceDefinitionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&InterfaceDefinitionService_ServiceDesc, srv)
}

func _InterfaceDefinitionService_LocalImportInterfaceDefinition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocalImportInterfaceDefinitionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).LocalImportInterfaceDefinition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_LocalImportInterfaceDefinition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).LocalImportInterfaceDefinition(ctx, req.(*LocalImportInterfaceDefinitionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_CreateInterfaceDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInterfaceDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).CreateInterfaceDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_CreateInterfaceDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).CreateInterfaceDocument(ctx, req.(*CreateInterfaceDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_RemoveInterfaceDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveInterfaceDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).RemoveInterfaceDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_RemoveInterfaceDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).RemoveInterfaceDocument(ctx, req.(*RemoveInterfaceDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ModifyInterfaceDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyInterfaceDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ModifyInterfaceDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceDocument(ctx, req.(*ModifyInterfaceDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_SearchInterfaceDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchInterfaceDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_SearchInterfaceDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceDocument(ctx, req.(*SearchInterfaceDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ViewInterfaceDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewInterfaceDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ViewInterfaceDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ViewInterfaceDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ViewInterfaceDocument(ctx, req.(*ViewInterfaceDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_MockInterfaceDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MockInterfaceDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).MockInterfaceDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_MockInterfaceDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).MockInterfaceDocument(ctx, req.(*MockInterfaceDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_SearchInterfaceDocumentReference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchInterfaceDocumentReferenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceDocumentReference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_SearchInterfaceDocumentReference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceDocumentReference(ctx, req.(*SearchInterfaceDocumentReferenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ModifyInterfaceDocumentReferenceState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyInterfaceDocumentReferenceStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceDocumentReferenceState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ModifyInterfaceDocumentReferenceState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceDocumentReferenceState(ctx, req.(*ModifyInterfaceDocumentReferenceStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_CreateInterfaceSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInterfaceSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).CreateInterfaceSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_CreateInterfaceSchema_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).CreateInterfaceSchema(ctx, req.(*CreateInterfaceSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_RemoveInterfaceSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveInterfaceSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).RemoveInterfaceSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_RemoveInterfaceSchema_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).RemoveInterfaceSchema(ctx, req.(*RemoveInterfaceSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ModifyInterfaceSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyInterfaceSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ModifyInterfaceSchema_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceSchema(ctx, req.(*ModifyInterfaceSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_SearchInterfaceSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchInterfaceSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_SearchInterfaceSchema_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceSchema(ctx, req.(*SearchInterfaceSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ViewInterfaceSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewInterfaceSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ViewInterfaceSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ViewInterfaceSchema_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ViewInterfaceSchema(ctx, req.(*ViewInterfaceSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_CreateInterfaceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInterfaceConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).CreateInterfaceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_CreateInterfaceConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).CreateInterfaceConfig(ctx, req.(*CreateInterfaceConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_RemoveInterfaceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveInterfaceConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).RemoveInterfaceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_RemoveInterfaceConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).RemoveInterfaceConfig(ctx, req.(*RemoveInterfaceConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ModifyInterfaceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyInterfaceConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ModifyInterfaceConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceConfig(ctx, req.(*ModifyInterfaceConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_SearchInterfaceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchInterfaceConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_SearchInterfaceConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceConfig(ctx, req.(*SearchInterfaceConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ViewInterfaceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewInterfaceConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ViewInterfaceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ViewInterfaceConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ViewInterfaceConfig(ctx, req.(*ViewInterfaceConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_CreateInterfaceCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInterfaceCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).CreateInterfaceCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_CreateInterfaceCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).CreateInterfaceCase(ctx, req.(*CreateInterfaceCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_RemoveInterfaceCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveInterfaceCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).RemoveInterfaceCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_RemoveInterfaceCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).RemoveInterfaceCase(ctx, req.(*RemoveInterfaceCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ModifyInterfaceCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyInterfaceCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ModifyInterfaceCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ModifyInterfaceCase(ctx, req.(*ModifyInterfaceCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_SearchInterfaceCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchInterfaceCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_SearchInterfaceCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceCase(ctx, req.(*SearchInterfaceCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_ViewInterfaceCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewInterfaceCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).ViewInterfaceCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_ViewInterfaceCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).ViewInterfaceCase(ctx, req.(*ViewInterfaceCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_MaintainInterfaceCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MaintainInterfaceCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).MaintainInterfaceCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_MaintainInterfaceCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).MaintainInterfaceCase(ctx, req.(*MaintainInterfaceCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_PublishInterfaceCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishInterfaceCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).PublishInterfaceCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_PublishInterfaceCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).PublishInterfaceCase(ctx, req.(*PublishInterfaceCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_SearchInterfaceCaseReference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchInterfaceCaseReferenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceCaseReference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_SearchInterfaceCaseReference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).SearchInterfaceCaseReference(ctx, req.(*SearchInterfaceCaseReferenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_UpdateInterfaceCoverageData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInterfaceCoverageDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).UpdateInterfaceCoverageData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_UpdateInterfaceCoverageData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).UpdateInterfaceCoverageData(ctx, req.(*UpdateInterfaceCoverageDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_GetInterfaceCoverageTeams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInterfaceCoverageTeamsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).GetInterfaceCoverageTeams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_GetInterfaceCoverageTeams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).GetInterfaceCoverageTeams(ctx, req.(*GetInterfaceCoverageTeamsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_GetInterfaceCoverageData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInterfaceCoverageDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).GetInterfaceCoverageData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_GetInterfaceCoverageData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).GetInterfaceCoverageData(ctx, req.(*GetInterfaceCoverageDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _InterfaceDefinitionService_UpdateInterfaceDocumentTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInterfaceDocumentTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InterfaceDefinitionServiceServer).UpdateInterfaceDocumentTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InterfaceDefinitionService_UpdateInterfaceDocumentTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InterfaceDefinitionServiceServer).UpdateInterfaceDocumentTags(ctx, req.(*UpdateInterfaceDocumentTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// InterfaceDefinitionService_ServiceDesc is the grpc.ServiceDesc for InterfaceDefinitionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var InterfaceDefinitionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.InterfaceDefinitionService",
	HandlerType: (*InterfaceDefinitionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LocalImportInterfaceDefinition",
			Handler:    _InterfaceDefinitionService_LocalImportInterfaceDefinition_Handler,
		},
		{
			MethodName: "CreateInterfaceDocument",
			Handler:    _InterfaceDefinitionService_CreateInterfaceDocument_Handler,
		},
		{
			MethodName: "RemoveInterfaceDocument",
			Handler:    _InterfaceDefinitionService_RemoveInterfaceDocument_Handler,
		},
		{
			MethodName: "ModifyInterfaceDocument",
			Handler:    _InterfaceDefinitionService_ModifyInterfaceDocument_Handler,
		},
		{
			MethodName: "SearchInterfaceDocument",
			Handler:    _InterfaceDefinitionService_SearchInterfaceDocument_Handler,
		},
		{
			MethodName: "ViewInterfaceDocument",
			Handler:    _InterfaceDefinitionService_ViewInterfaceDocument_Handler,
		},
		{
			MethodName: "MockInterfaceDocument",
			Handler:    _InterfaceDefinitionService_MockInterfaceDocument_Handler,
		},
		{
			MethodName: "SearchInterfaceDocumentReference",
			Handler:    _InterfaceDefinitionService_SearchInterfaceDocumentReference_Handler,
		},
		{
			MethodName: "ModifyInterfaceDocumentReferenceState",
			Handler:    _InterfaceDefinitionService_ModifyInterfaceDocumentReferenceState_Handler,
		},
		{
			MethodName: "CreateInterfaceSchema",
			Handler:    _InterfaceDefinitionService_CreateInterfaceSchema_Handler,
		},
		{
			MethodName: "RemoveInterfaceSchema",
			Handler:    _InterfaceDefinitionService_RemoveInterfaceSchema_Handler,
		},
		{
			MethodName: "ModifyInterfaceSchema",
			Handler:    _InterfaceDefinitionService_ModifyInterfaceSchema_Handler,
		},
		{
			MethodName: "SearchInterfaceSchema",
			Handler:    _InterfaceDefinitionService_SearchInterfaceSchema_Handler,
		},
		{
			MethodName: "ViewInterfaceSchema",
			Handler:    _InterfaceDefinitionService_ViewInterfaceSchema_Handler,
		},
		{
			MethodName: "CreateInterfaceConfig",
			Handler:    _InterfaceDefinitionService_CreateInterfaceConfig_Handler,
		},
		{
			MethodName: "RemoveInterfaceConfig",
			Handler:    _InterfaceDefinitionService_RemoveInterfaceConfig_Handler,
		},
		{
			MethodName: "ModifyInterfaceConfig",
			Handler:    _InterfaceDefinitionService_ModifyInterfaceConfig_Handler,
		},
		{
			MethodName: "SearchInterfaceConfig",
			Handler:    _InterfaceDefinitionService_SearchInterfaceConfig_Handler,
		},
		{
			MethodName: "ViewInterfaceConfig",
			Handler:    _InterfaceDefinitionService_ViewInterfaceConfig_Handler,
		},
		{
			MethodName: "CreateInterfaceCase",
			Handler:    _InterfaceDefinitionService_CreateInterfaceCase_Handler,
		},
		{
			MethodName: "RemoveInterfaceCase",
			Handler:    _InterfaceDefinitionService_RemoveInterfaceCase_Handler,
		},
		{
			MethodName: "ModifyInterfaceCase",
			Handler:    _InterfaceDefinitionService_ModifyInterfaceCase_Handler,
		},
		{
			MethodName: "SearchInterfaceCase",
			Handler:    _InterfaceDefinitionService_SearchInterfaceCase_Handler,
		},
		{
			MethodName: "ViewInterfaceCase",
			Handler:    _InterfaceDefinitionService_ViewInterfaceCase_Handler,
		},
		{
			MethodName: "MaintainInterfaceCase",
			Handler:    _InterfaceDefinitionService_MaintainInterfaceCase_Handler,
		},
		{
			MethodName: "PublishInterfaceCase",
			Handler:    _InterfaceDefinitionService_PublishInterfaceCase_Handler,
		},
		{
			MethodName: "SearchInterfaceCaseReference",
			Handler:    _InterfaceDefinitionService_SearchInterfaceCaseReference_Handler,
		},
		{
			MethodName: "UpdateInterfaceCoverageData",
			Handler:    _InterfaceDefinitionService_UpdateInterfaceCoverageData_Handler,
		},
		{
			MethodName: "GetInterfaceCoverageTeams",
			Handler:    _InterfaceDefinitionService_GetInterfaceCoverageTeams_Handler,
		},
		{
			MethodName: "GetInterfaceCoverageData",
			Handler:    _InterfaceDefinitionService_GetInterfaceCoverageData_Handler,
		},
		{
			MethodName: "UpdateInterfaceDocumentTags",
			Handler:    _InterfaceDefinitionService_UpdateInterfaceDocumentTags_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	ComponentGroupService_CreateComponentGroup_FullMethodName          = "/manager.ComponentGroupService/CreateComponentGroup"
	ComponentGroupService_RemoveComponentGroup_FullMethodName          = "/manager.ComponentGroupService/RemoveComponentGroup"
	ComponentGroupService_ModifyComponentGroup_FullMethodName          = "/manager.ComponentGroupService/ModifyComponentGroup"
	ComponentGroupService_SearchComponentGroup_FullMethodName          = "/manager.ComponentGroupService/SearchComponentGroup"
	ComponentGroupService_ViewComponentGroup_FullMethodName            = "/manager.ComponentGroupService/ViewComponentGroup"
	ComponentGroupService_SearchComponentGroupReference_FullMethodName = "/manager.ComponentGroupService/SearchComponentGroupReference"
)

// ComponentGroupServiceClient is the client API for ComponentGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ComponentGroupService 组件组服务
type ComponentGroupServiceClient interface {
	//CreateComponentGroup 创建组件组
	CreateComponentGroup(ctx context.Context, in *CreateComponentGroupReq, opts ...grpc.CallOption) (*CreateComponentGroupResp, error)
	//RemoveComponentGroup 删除组件组
	RemoveComponentGroup(ctx context.Context, in *RemoveComponentGroupReq, opts ...grpc.CallOption) (*RemoveComponentGroupResp, error)
	//ModifyComponentGroup 编辑组件组
	ModifyComponentGroup(ctx context.Context, in *ModifyComponentGroupReq, opts ...grpc.CallOption) (*ModifyComponentGroupResp, error)
	//SearchComponentGroup 搜索组件组
	SearchComponentGroup(ctx context.Context, in *SearchComponentGroupReq, opts ...grpc.CallOption) (*SearchComponentGroupResp, error)
	//ViewComponentGroup 查看组件组
	ViewComponentGroup(ctx context.Context, in *ViewComponentGroupReq, opts ...grpc.CallOption) (*ViewComponentGroupResp, error)
	//SearchComponentGroupReference 搜索组件组引用详情
	SearchComponentGroupReference(ctx context.Context, in *SearchComponentGroupReferenceReq, opts ...grpc.CallOption) (*SearchComponentGroupReferenceResp, error)
}

type componentGroupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewComponentGroupServiceClient(cc grpc.ClientConnInterface) ComponentGroupServiceClient {
	return &componentGroupServiceClient{cc}
}

func (c *componentGroupServiceClient) CreateComponentGroup(ctx context.Context, in *CreateComponentGroupReq, opts ...grpc.CallOption) (*CreateComponentGroupResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateComponentGroupResp)
	err := c.cc.Invoke(ctx, ComponentGroupService_CreateComponentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *componentGroupServiceClient) RemoveComponentGroup(ctx context.Context, in *RemoveComponentGroupReq, opts ...grpc.CallOption) (*RemoveComponentGroupResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveComponentGroupResp)
	err := c.cc.Invoke(ctx, ComponentGroupService_RemoveComponentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *componentGroupServiceClient) ModifyComponentGroup(ctx context.Context, in *ModifyComponentGroupReq, opts ...grpc.CallOption) (*ModifyComponentGroupResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyComponentGroupResp)
	err := c.cc.Invoke(ctx, ComponentGroupService_ModifyComponentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *componentGroupServiceClient) SearchComponentGroup(ctx context.Context, in *SearchComponentGroupReq, opts ...grpc.CallOption) (*SearchComponentGroupResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchComponentGroupResp)
	err := c.cc.Invoke(ctx, ComponentGroupService_SearchComponentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *componentGroupServiceClient) ViewComponentGroup(ctx context.Context, in *ViewComponentGroupReq, opts ...grpc.CallOption) (*ViewComponentGroupResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewComponentGroupResp)
	err := c.cc.Invoke(ctx, ComponentGroupService_ViewComponentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *componentGroupServiceClient) SearchComponentGroupReference(ctx context.Context, in *SearchComponentGroupReferenceReq, opts ...grpc.CallOption) (*SearchComponentGroupReferenceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchComponentGroupReferenceResp)
	err := c.cc.Invoke(ctx, ComponentGroupService_SearchComponentGroupReference_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ComponentGroupServiceServer is the server API for ComponentGroupService service.
// All implementations must embed UnimplementedComponentGroupServiceServer
// for forward compatibility.
//
// ComponentGroupService 组件组服务
type ComponentGroupServiceServer interface {
	//CreateComponentGroup 创建组件组
	CreateComponentGroup(context.Context, *CreateComponentGroupReq) (*CreateComponentGroupResp, error)
	//RemoveComponentGroup 删除组件组
	RemoveComponentGroup(context.Context, *RemoveComponentGroupReq) (*RemoveComponentGroupResp, error)
	//ModifyComponentGroup 编辑组件组
	ModifyComponentGroup(context.Context, *ModifyComponentGroupReq) (*ModifyComponentGroupResp, error)
	//SearchComponentGroup 搜索组件组
	SearchComponentGroup(context.Context, *SearchComponentGroupReq) (*SearchComponentGroupResp, error)
	//ViewComponentGroup 查看组件组
	ViewComponentGroup(context.Context, *ViewComponentGroupReq) (*ViewComponentGroupResp, error)
	//SearchComponentGroupReference 搜索组件组引用详情
	SearchComponentGroupReference(context.Context, *SearchComponentGroupReferenceReq) (*SearchComponentGroupReferenceResp, error)
	mustEmbedUnimplementedComponentGroupServiceServer()
}

// UnimplementedComponentGroupServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedComponentGroupServiceServer struct{}

func (UnimplementedComponentGroupServiceServer) CreateComponentGroup(context.Context, *CreateComponentGroupReq) (*CreateComponentGroupResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateComponentGroup not implemented")
}
func (UnimplementedComponentGroupServiceServer) RemoveComponentGroup(context.Context, *RemoveComponentGroupReq) (*RemoveComponentGroupResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveComponentGroup not implemented")
}
func (UnimplementedComponentGroupServiceServer) ModifyComponentGroup(context.Context, *ModifyComponentGroupReq) (*ModifyComponentGroupResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyComponentGroup not implemented")
}
func (UnimplementedComponentGroupServiceServer) SearchComponentGroup(context.Context, *SearchComponentGroupReq) (*SearchComponentGroupResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchComponentGroup not implemented")
}
func (UnimplementedComponentGroupServiceServer) ViewComponentGroup(context.Context, *ViewComponentGroupReq) (*ViewComponentGroupResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewComponentGroup not implemented")
}
func (UnimplementedComponentGroupServiceServer) SearchComponentGroupReference(context.Context, *SearchComponentGroupReferenceReq) (*SearchComponentGroupReferenceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchComponentGroupReference not implemented")
}
func (UnimplementedComponentGroupServiceServer) mustEmbedUnimplementedComponentGroupServiceServer() {}
func (UnimplementedComponentGroupServiceServer) testEmbeddedByValue()                               {}

// UnsafeComponentGroupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ComponentGroupServiceServer will
// result in compilation errors.
type UnsafeComponentGroupServiceServer interface {
	mustEmbedUnimplementedComponentGroupServiceServer()
}

func RegisterComponentGroupServiceServer(s grpc.ServiceRegistrar, srv ComponentGroupServiceServer) {
	// If the following call pancis, it indicates UnimplementedComponentGroupServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ComponentGroupService_ServiceDesc, srv)
}

func _ComponentGroupService_CreateComponentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateComponentGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComponentGroupServiceServer).CreateComponentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ComponentGroupService_CreateComponentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComponentGroupServiceServer).CreateComponentGroup(ctx, req.(*CreateComponentGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ComponentGroupService_RemoveComponentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveComponentGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComponentGroupServiceServer).RemoveComponentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ComponentGroupService_RemoveComponentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComponentGroupServiceServer).RemoveComponentGroup(ctx, req.(*RemoveComponentGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ComponentGroupService_ModifyComponentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyComponentGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComponentGroupServiceServer).ModifyComponentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ComponentGroupService_ModifyComponentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComponentGroupServiceServer).ModifyComponentGroup(ctx, req.(*ModifyComponentGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ComponentGroupService_SearchComponentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchComponentGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComponentGroupServiceServer).SearchComponentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ComponentGroupService_SearchComponentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComponentGroupServiceServer).SearchComponentGroup(ctx, req.(*SearchComponentGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ComponentGroupService_ViewComponentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewComponentGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComponentGroupServiceServer).ViewComponentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ComponentGroupService_ViewComponentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComponentGroupServiceServer).ViewComponentGroup(ctx, req.(*ViewComponentGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ComponentGroupService_SearchComponentGroupReference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchComponentGroupReferenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComponentGroupServiceServer).SearchComponentGroupReference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ComponentGroupService_SearchComponentGroupReference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComponentGroupServiceServer).SearchComponentGroupReference(ctx, req.(*SearchComponentGroupReferenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ComponentGroupService_ServiceDesc is the grpc.ServiceDesc for ComponentGroupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ComponentGroupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ComponentGroupService",
	HandlerType: (*ComponentGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateComponentGroup",
			Handler:    _ComponentGroupService_CreateComponentGroup_Handler,
		},
		{
			MethodName: "RemoveComponentGroup",
			Handler:    _ComponentGroupService_RemoveComponentGroup_Handler,
		},
		{
			MethodName: "ModifyComponentGroup",
			Handler:    _ComponentGroupService_ModifyComponentGroup_Handler,
		},
		{
			MethodName: "SearchComponentGroup",
			Handler:    _ComponentGroupService_SearchComponentGroup_Handler,
		},
		{
			MethodName: "ViewComponentGroup",
			Handler:    _ComponentGroupService_ViewComponentGroup_Handler,
		},
		{
			MethodName: "SearchComponentGroupReference",
			Handler:    _ComponentGroupService_SearchComponentGroupReference_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	ApiCaseService_CreateApiCase_FullMethodName          = "/manager.ApiCaseService/CreateApiCase"
	ApiCaseService_RemoveApiCase_FullMethodName          = "/manager.ApiCaseService/RemoveApiCase"
	ApiCaseService_ModifyApiCase_FullMethodName          = "/manager.ApiCaseService/ModifyApiCase"
	ApiCaseService_SearchApiCase_FullMethodName          = "/manager.ApiCaseService/SearchApiCase"
	ApiCaseService_ViewApiCase_FullMethodName            = "/manager.ApiCaseService/ViewApiCase"
	ApiCaseService_SearchApiCaseReference_FullMethodName = "/manager.ApiCaseService/SearchApiCaseReference"
	ApiCaseService_MaintainApiCase_FullMethodName        = "/manager.ApiCaseService/MaintainApiCase"
	ApiCaseService_PublishApiCase_FullMethodName         = "/manager.ApiCaseService/PublishApiCase"
)

// ApiCaseServiceClient is the client API for ApiCaseService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ApiCaseService API用例服务
type ApiCaseServiceClient interface {
	//CreateApiCase 创建API用例
	CreateApiCase(ctx context.Context, in *CreateApiCaseReq, opts ...grpc.CallOption) (*CreateApiCaseResp, error)
	//RemoveApiCase 删除API用例
	RemoveApiCase(ctx context.Context, in *RemoveApiCaseReq, opts ...grpc.CallOption) (*RemoveApiCaseResp, error)
	//ModifyApiCase 编辑API用例
	ModifyApiCase(ctx context.Context, in *ModifyApiCaseReq, opts ...grpc.CallOption) (*ModifyApiCaseResp, error)
	//SearchApiCase 搜索API用例
	SearchApiCase(ctx context.Context, in *SearchApiCaseReq, opts ...grpc.CallOption) (*SearchApiCaseResp, error)
	//ViewApiCase 查看API用例
	ViewApiCase(ctx context.Context, in *ViewApiCaseReq, opts ...grpc.CallOption) (*ViewApiCaseResp, error)
	//SearchApiCaseReference 搜索API用例引用详情
	SearchApiCaseReference(ctx context.Context, in *SearchApiCaseReferenceReq, opts ...grpc.CallOption) (*SearchApiCaseReferenceResp, error)
	//MaintainApiCase 维护API用例
	MaintainApiCase(ctx context.Context, in *MaintainApiCaseReq, opts ...grpc.CallOption) (*MaintainApiCaseResp, error)
	//PublishApiCase 发布API用例
	PublishApiCase(ctx context.Context, in *PublishApiCaseReq, opts ...grpc.CallOption) (*PublishApiCaseResp, error)
}

type apiCaseServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApiCaseServiceClient(cc grpc.ClientConnInterface) ApiCaseServiceClient {
	return &apiCaseServiceClient{cc}
}

func (c *apiCaseServiceClient) CreateApiCase(ctx context.Context, in *CreateApiCaseReq, opts ...grpc.CallOption) (*CreateApiCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateApiCaseResp)
	err := c.cc.Invoke(ctx, ApiCaseService_CreateApiCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiCaseServiceClient) RemoveApiCase(ctx context.Context, in *RemoveApiCaseReq, opts ...grpc.CallOption) (*RemoveApiCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveApiCaseResp)
	err := c.cc.Invoke(ctx, ApiCaseService_RemoveApiCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiCaseServiceClient) ModifyApiCase(ctx context.Context, in *ModifyApiCaseReq, opts ...grpc.CallOption) (*ModifyApiCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyApiCaseResp)
	err := c.cc.Invoke(ctx, ApiCaseService_ModifyApiCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiCaseServiceClient) SearchApiCase(ctx context.Context, in *SearchApiCaseReq, opts ...grpc.CallOption) (*SearchApiCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchApiCaseResp)
	err := c.cc.Invoke(ctx, ApiCaseService_SearchApiCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiCaseServiceClient) ViewApiCase(ctx context.Context, in *ViewApiCaseReq, opts ...grpc.CallOption) (*ViewApiCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewApiCaseResp)
	err := c.cc.Invoke(ctx, ApiCaseService_ViewApiCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiCaseServiceClient) SearchApiCaseReference(ctx context.Context, in *SearchApiCaseReferenceReq, opts ...grpc.CallOption) (*SearchApiCaseReferenceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchApiCaseReferenceResp)
	err := c.cc.Invoke(ctx, ApiCaseService_SearchApiCaseReference_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiCaseServiceClient) MaintainApiCase(ctx context.Context, in *MaintainApiCaseReq, opts ...grpc.CallOption) (*MaintainApiCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MaintainApiCaseResp)
	err := c.cc.Invoke(ctx, ApiCaseService_MaintainApiCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiCaseServiceClient) PublishApiCase(ctx context.Context, in *PublishApiCaseReq, opts ...grpc.CallOption) (*PublishApiCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PublishApiCaseResp)
	err := c.cc.Invoke(ctx, ApiCaseService_PublishApiCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiCaseServiceServer is the server API for ApiCaseService service.
// All implementations must embed UnimplementedApiCaseServiceServer
// for forward compatibility.
//
// ApiCaseService API用例服务
type ApiCaseServiceServer interface {
	//CreateApiCase 创建API用例
	CreateApiCase(context.Context, *CreateApiCaseReq) (*CreateApiCaseResp, error)
	//RemoveApiCase 删除API用例
	RemoveApiCase(context.Context, *RemoveApiCaseReq) (*RemoveApiCaseResp, error)
	//ModifyApiCase 编辑API用例
	ModifyApiCase(context.Context, *ModifyApiCaseReq) (*ModifyApiCaseResp, error)
	//SearchApiCase 搜索API用例
	SearchApiCase(context.Context, *SearchApiCaseReq) (*SearchApiCaseResp, error)
	//ViewApiCase 查看API用例
	ViewApiCase(context.Context, *ViewApiCaseReq) (*ViewApiCaseResp, error)
	//SearchApiCaseReference 搜索API用例引用详情
	SearchApiCaseReference(context.Context, *SearchApiCaseReferenceReq) (*SearchApiCaseReferenceResp, error)
	//MaintainApiCase 维护API用例
	MaintainApiCase(context.Context, *MaintainApiCaseReq) (*MaintainApiCaseResp, error)
	//PublishApiCase 发布API用例
	PublishApiCase(context.Context, *PublishApiCaseReq) (*PublishApiCaseResp, error)
	mustEmbedUnimplementedApiCaseServiceServer()
}

// UnimplementedApiCaseServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedApiCaseServiceServer struct{}

func (UnimplementedApiCaseServiceServer) CreateApiCase(context.Context, *CreateApiCaseReq) (*CreateApiCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateApiCase not implemented")
}
func (UnimplementedApiCaseServiceServer) RemoveApiCase(context.Context, *RemoveApiCaseReq) (*RemoveApiCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveApiCase not implemented")
}
func (UnimplementedApiCaseServiceServer) ModifyApiCase(context.Context, *ModifyApiCaseReq) (*ModifyApiCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyApiCase not implemented")
}
func (UnimplementedApiCaseServiceServer) SearchApiCase(context.Context, *SearchApiCaseReq) (*SearchApiCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchApiCase not implemented")
}
func (UnimplementedApiCaseServiceServer) ViewApiCase(context.Context, *ViewApiCaseReq) (*ViewApiCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewApiCase not implemented")
}
func (UnimplementedApiCaseServiceServer) SearchApiCaseReference(context.Context, *SearchApiCaseReferenceReq) (*SearchApiCaseReferenceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchApiCaseReference not implemented")
}
func (UnimplementedApiCaseServiceServer) MaintainApiCase(context.Context, *MaintainApiCaseReq) (*MaintainApiCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MaintainApiCase not implemented")
}
func (UnimplementedApiCaseServiceServer) PublishApiCase(context.Context, *PublishApiCaseReq) (*PublishApiCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishApiCase not implemented")
}
func (UnimplementedApiCaseServiceServer) mustEmbedUnimplementedApiCaseServiceServer() {}
func (UnimplementedApiCaseServiceServer) testEmbeddedByValue()                        {}

// UnsafeApiCaseServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiCaseServiceServer will
// result in compilation errors.
type UnsafeApiCaseServiceServer interface {
	mustEmbedUnimplementedApiCaseServiceServer()
}

func RegisterApiCaseServiceServer(s grpc.ServiceRegistrar, srv ApiCaseServiceServer) {
	// If the following call pancis, it indicates UnimplementedApiCaseServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ApiCaseService_ServiceDesc, srv)
}

func _ApiCaseService_CreateApiCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateApiCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiCaseServiceServer).CreateApiCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiCaseService_CreateApiCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiCaseServiceServer).CreateApiCase(ctx, req.(*CreateApiCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiCaseService_RemoveApiCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveApiCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiCaseServiceServer).RemoveApiCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiCaseService_RemoveApiCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiCaseServiceServer).RemoveApiCase(ctx, req.(*RemoveApiCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiCaseService_ModifyApiCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyApiCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiCaseServiceServer).ModifyApiCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiCaseService_ModifyApiCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiCaseServiceServer).ModifyApiCase(ctx, req.(*ModifyApiCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiCaseService_SearchApiCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchApiCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiCaseServiceServer).SearchApiCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiCaseService_SearchApiCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiCaseServiceServer).SearchApiCase(ctx, req.(*SearchApiCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiCaseService_ViewApiCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewApiCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiCaseServiceServer).ViewApiCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiCaseService_ViewApiCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiCaseServiceServer).ViewApiCase(ctx, req.(*ViewApiCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiCaseService_SearchApiCaseReference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchApiCaseReferenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiCaseServiceServer).SearchApiCaseReference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiCaseService_SearchApiCaseReference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiCaseServiceServer).SearchApiCaseReference(ctx, req.(*SearchApiCaseReferenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiCaseService_MaintainApiCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MaintainApiCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiCaseServiceServer).MaintainApiCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiCaseService_MaintainApiCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiCaseServiceServer).MaintainApiCase(ctx, req.(*MaintainApiCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiCaseService_PublishApiCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishApiCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiCaseServiceServer).PublishApiCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiCaseService_PublishApiCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiCaseServiceServer).PublishApiCase(ctx, req.(*PublishApiCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ApiCaseService_ServiceDesc is the grpc.ServiceDesc for ApiCaseService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApiCaseService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ApiCaseService",
	HandlerType: (*ApiCaseServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateApiCase",
			Handler:    _ApiCaseService_CreateApiCase_Handler,
		},
		{
			MethodName: "RemoveApiCase",
			Handler:    _ApiCaseService_RemoveApiCase_Handler,
		},
		{
			MethodName: "ModifyApiCase",
			Handler:    _ApiCaseService_ModifyApiCase_Handler,
		},
		{
			MethodName: "SearchApiCase",
			Handler:    _ApiCaseService_SearchApiCase_Handler,
		},
		{
			MethodName: "ViewApiCase",
			Handler:    _ApiCaseService_ViewApiCase_Handler,
		},
		{
			MethodName: "SearchApiCaseReference",
			Handler:    _ApiCaseService_SearchApiCaseReference_Handler,
		},
		{
			MethodName: "MaintainApiCase",
			Handler:    _ApiCaseService_MaintainApiCase_Handler,
		},
		{
			MethodName: "PublishApiCase",
			Handler:    _ApiCaseService_PublishApiCase_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	ApiSuiteService_CreateApiSuite_FullMethodName                 = "/manager.ApiSuiteService/CreateApiSuite"
	ApiSuiteService_RemoveApiSuite_FullMethodName                 = "/manager.ApiSuiteService/RemoveApiSuite"
	ApiSuiteService_ModifyApiSuite_FullMethodName                 = "/manager.ApiSuiteService/ModifyApiSuite"
	ApiSuiteService_SearchApiSuite_FullMethodName                 = "/manager.ApiSuiteService/SearchApiSuite"
	ApiSuiteService_ViewApiSuite_FullMethodName                   = "/manager.ApiSuiteService/ViewApiSuite"
	ApiSuiteService_SearchApiCaseInApiSuite_FullMethodName        = "/manager.ApiSuiteService/SearchApiCaseInApiSuite"
	ApiSuiteService_SearchApiCaseNotInApiSuite_FullMethodName     = "/manager.ApiSuiteService/SearchApiCaseNotInApiSuite"
	ApiSuiteService_AddApiCaseToApiSuite_FullMethodName           = "/manager.ApiSuiteService/AddApiCaseToApiSuite"
	ApiSuiteService_RemoveApiCaseFromApiSuite_FullMethodName      = "/manager.ApiSuiteService/RemoveApiCaseFromApiSuite"
	ApiSuiteService_SearchApiSuiteReference_FullMethodName        = "/manager.ApiSuiteService/SearchApiSuiteReference"
	ApiSuiteService_ModifyApiSuiteReferenceState_FullMethodName   = "/manager.ApiSuiteService/ModifyApiSuiteReferenceState"
	ApiSuiteService_SearchCaseInApiSuite_FullMethodName           = "/manager.ApiSuiteService/SearchCaseInApiSuite"
	ApiSuiteService_AddCaseToApiSuite_FullMethodName              = "/manager.ApiSuiteService/AddCaseToApiSuite"
	ApiSuiteService_RemoveCaseFromApiSuite_FullMethodName         = "/manager.ApiSuiteService/RemoveCaseFromApiSuite"
	ApiSuiteService_SearchServiceCaseNotInApiSuite_FullMethodName = "/manager.ApiSuiteService/SearchServiceCaseNotInApiSuite"
)

// ApiSuiteServiceClient is the client API for ApiSuiteService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ApiSuiteService API集合服务
type ApiSuiteServiceClient interface {
	//CreateApiSuite 创建API集合
	CreateApiSuite(ctx context.Context, in *CreateApiSuiteReq, opts ...grpc.CallOption) (*CreateApiSuiteResp, error)
	//RemoveApiSuite 删除API集合
	RemoveApiSuite(ctx context.Context, in *RemoveApiSuiteReq, opts ...grpc.CallOption) (*RemoveApiSuiteResp, error)
	//ModifyApiSuite 编辑API集合
	ModifyApiSuite(ctx context.Context, in *ModifyApiSuiteReq, opts ...grpc.CallOption) (*ModifyApiSuiteResp, error)
	//SearchApiSuite 搜索API集合
	SearchApiSuite(ctx context.Context, in *SearchApiSuiteReq, opts ...grpc.CallOption) (*SearchApiSuiteResp, error)
	//ViewApiSuite 查看API集合
	ViewApiSuite(ctx context.Context, in *ViewApiSuiteReq, opts ...grpc.CallOption) (*ViewApiSuiteResp, error)
	//SearchApiCaseInApiSuite 搜索API集合中的API用例
	SearchApiCaseInApiSuite(ctx context.Context, in *SearchApiCaseInApiSuiteReq, opts ...grpc.CallOption) (*SearchApiCaseInApiSuiteResp, error)
	//SearchApiCaseNotInApiSuite 搜索不在指定的API集合中的API用例
	SearchApiCaseNotInApiSuite(ctx context.Context, in *SearchApiCaseNotInApiSuiteReq, opts ...grpc.CallOption) (*SearchApiCaseNotInApiSuiteResp, error)
	//AddApiCaseToApiSuite 添加API用例到API集合中
	AddApiCaseToApiSuite(ctx context.Context, in *AddApiCaseToApiSuiteReq, opts ...grpc.CallOption) (*AddApiCaseToApiSuiteResp, error)
	//RemoveApiCaseFromApiSuite 从API集合中移除API用例
	RemoveApiCaseFromApiSuite(ctx context.Context, in *RemoveApiCaseFromApiSuiteReq, opts ...grpc.CallOption) (*RemoveApiCaseFromApiSuiteResp, error)
	//SearchApiSuiteReference 搜索API集合引用详情
	SearchApiSuiteReference(ctx context.Context, in *SearchApiSuiteReferenceReq, opts ...grpc.CallOption) (*SearchApiSuiteReferenceResp, error)
	//ModifyApiSuiteReferenceState 修改API集合所在的API计划的引用状态
	ModifyApiSuiteReferenceState(ctx context.Context, in *ModifyApiSuiteReferenceStateReq, opts ...grpc.CallOption) (*ModifyApiSuiteReferenceStateResp, error)
	//SearchCaseInApiSuite 搜索API集合中的用例
	SearchCaseInApiSuite(ctx context.Context, in *SearchCaseInApiSuiteReq, opts ...grpc.CallOption) (*SearchCaseInApiSuiteResp, error)
	//AddCaseToApiSuite 添加用例到API集合中
	AddCaseToApiSuite(ctx context.Context, in *AddCaseToApiSuiteReq, opts ...grpc.CallOption) (*AddCaseToApiSuiteResp, error)
	//RemoveCaseFromApiSuite 从API集合中移除用例
	RemoveCaseFromApiSuite(ctx context.Context, in *RemoveCaseFromApiSuiteReq, opts ...grpc.CallOption) (*RemoveCaseFromApiSuiteResp, error)
	//SearchServiceCaseNotInApiSuite 搜索不在指定的API集合中的精准测试用例
	SearchServiceCaseNotInApiSuite(ctx context.Context, in *SearchServiceCaseNotInApiSuiteReq, opts ...grpc.CallOption) (*SearchServiceCaseNotInApiSuiteResp, error)
}

type apiSuiteServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApiSuiteServiceClient(cc grpc.ClientConnInterface) ApiSuiteServiceClient {
	return &apiSuiteServiceClient{cc}
}

func (c *apiSuiteServiceClient) CreateApiSuite(ctx context.Context, in *CreateApiSuiteReq, opts ...grpc.CallOption) (*CreateApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_CreateApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) RemoveApiSuite(ctx context.Context, in *RemoveApiSuiteReq, opts ...grpc.CallOption) (*RemoveApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_RemoveApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) ModifyApiSuite(ctx context.Context, in *ModifyApiSuiteReq, opts ...grpc.CallOption) (*ModifyApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_ModifyApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) SearchApiSuite(ctx context.Context, in *SearchApiSuiteReq, opts ...grpc.CallOption) (*SearchApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_SearchApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) ViewApiSuite(ctx context.Context, in *ViewApiSuiteReq, opts ...grpc.CallOption) (*ViewApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_ViewApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) SearchApiCaseInApiSuite(ctx context.Context, in *SearchApiCaseInApiSuiteReq, opts ...grpc.CallOption) (*SearchApiCaseInApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchApiCaseInApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_SearchApiCaseInApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) SearchApiCaseNotInApiSuite(ctx context.Context, in *SearchApiCaseNotInApiSuiteReq, opts ...grpc.CallOption) (*SearchApiCaseNotInApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchApiCaseNotInApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_SearchApiCaseNotInApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) AddApiCaseToApiSuite(ctx context.Context, in *AddApiCaseToApiSuiteReq, opts ...grpc.CallOption) (*AddApiCaseToApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddApiCaseToApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_AddApiCaseToApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) RemoveApiCaseFromApiSuite(ctx context.Context, in *RemoveApiCaseFromApiSuiteReq, opts ...grpc.CallOption) (*RemoveApiCaseFromApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveApiCaseFromApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_RemoveApiCaseFromApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) SearchApiSuiteReference(ctx context.Context, in *SearchApiSuiteReferenceReq, opts ...grpc.CallOption) (*SearchApiSuiteReferenceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchApiSuiteReferenceResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_SearchApiSuiteReference_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) ModifyApiSuiteReferenceState(ctx context.Context, in *ModifyApiSuiteReferenceStateReq, opts ...grpc.CallOption) (*ModifyApiSuiteReferenceStateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyApiSuiteReferenceStateResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_ModifyApiSuiteReferenceState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) SearchCaseInApiSuite(ctx context.Context, in *SearchCaseInApiSuiteReq, opts ...grpc.CallOption) (*SearchCaseInApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCaseInApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_SearchCaseInApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) AddCaseToApiSuite(ctx context.Context, in *AddCaseToApiSuiteReq, opts ...grpc.CallOption) (*AddCaseToApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddCaseToApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_AddCaseToApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) RemoveCaseFromApiSuite(ctx context.Context, in *RemoveCaseFromApiSuiteReq, opts ...grpc.CallOption) (*RemoveCaseFromApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveCaseFromApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_RemoveCaseFromApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiSuiteServiceClient) SearchServiceCaseNotInApiSuite(ctx context.Context, in *SearchServiceCaseNotInApiSuiteReq, opts ...grpc.CallOption) (*SearchServiceCaseNotInApiSuiteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchServiceCaseNotInApiSuiteResp)
	err := c.cc.Invoke(ctx, ApiSuiteService_SearchServiceCaseNotInApiSuite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiSuiteServiceServer is the server API for ApiSuiteService service.
// All implementations must embed UnimplementedApiSuiteServiceServer
// for forward compatibility.
//
// ApiSuiteService API集合服务
type ApiSuiteServiceServer interface {
	//CreateApiSuite 创建API集合
	CreateApiSuite(context.Context, *CreateApiSuiteReq) (*CreateApiSuiteResp, error)
	//RemoveApiSuite 删除API集合
	RemoveApiSuite(context.Context, *RemoveApiSuiteReq) (*RemoveApiSuiteResp, error)
	//ModifyApiSuite 编辑API集合
	ModifyApiSuite(context.Context, *ModifyApiSuiteReq) (*ModifyApiSuiteResp, error)
	//SearchApiSuite 搜索API集合
	SearchApiSuite(context.Context, *SearchApiSuiteReq) (*SearchApiSuiteResp, error)
	//ViewApiSuite 查看API集合
	ViewApiSuite(context.Context, *ViewApiSuiteReq) (*ViewApiSuiteResp, error)
	//SearchApiCaseInApiSuite 搜索API集合中的API用例
	SearchApiCaseInApiSuite(context.Context, *SearchApiCaseInApiSuiteReq) (*SearchApiCaseInApiSuiteResp, error)
	//SearchApiCaseNotInApiSuite 搜索不在指定的API集合中的API用例
	SearchApiCaseNotInApiSuite(context.Context, *SearchApiCaseNotInApiSuiteReq) (*SearchApiCaseNotInApiSuiteResp, error)
	//AddApiCaseToApiSuite 添加API用例到API集合中
	AddApiCaseToApiSuite(context.Context, *AddApiCaseToApiSuiteReq) (*AddApiCaseToApiSuiteResp, error)
	//RemoveApiCaseFromApiSuite 从API集合中移除API用例
	RemoveApiCaseFromApiSuite(context.Context, *RemoveApiCaseFromApiSuiteReq) (*RemoveApiCaseFromApiSuiteResp, error)
	//SearchApiSuiteReference 搜索API集合引用详情
	SearchApiSuiteReference(context.Context, *SearchApiSuiteReferenceReq) (*SearchApiSuiteReferenceResp, error)
	//ModifyApiSuiteReferenceState 修改API集合所在的API计划的引用状态
	ModifyApiSuiteReferenceState(context.Context, *ModifyApiSuiteReferenceStateReq) (*ModifyApiSuiteReferenceStateResp, error)
	//SearchCaseInApiSuite 搜索API集合中的用例
	SearchCaseInApiSuite(context.Context, *SearchCaseInApiSuiteReq) (*SearchCaseInApiSuiteResp, error)
	//AddCaseToApiSuite 添加用例到API集合中
	AddCaseToApiSuite(context.Context, *AddCaseToApiSuiteReq) (*AddCaseToApiSuiteResp, error)
	//RemoveCaseFromApiSuite 从API集合中移除用例
	RemoveCaseFromApiSuite(context.Context, *RemoveCaseFromApiSuiteReq) (*RemoveCaseFromApiSuiteResp, error)
	//SearchServiceCaseNotInApiSuite 搜索不在指定的API集合中的精准测试用例
	SearchServiceCaseNotInApiSuite(context.Context, *SearchServiceCaseNotInApiSuiteReq) (*SearchServiceCaseNotInApiSuiteResp, error)
	mustEmbedUnimplementedApiSuiteServiceServer()
}

// UnimplementedApiSuiteServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedApiSuiteServiceServer struct{}

func (UnimplementedApiSuiteServiceServer) CreateApiSuite(context.Context, *CreateApiSuiteReq) (*CreateApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) RemoveApiSuite(context.Context, *RemoveApiSuiteReq) (*RemoveApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) ModifyApiSuite(context.Context, *ModifyApiSuiteReq) (*ModifyApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) SearchApiSuite(context.Context, *SearchApiSuiteReq) (*SearchApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) ViewApiSuite(context.Context, *ViewApiSuiteReq) (*ViewApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) SearchApiCaseInApiSuite(context.Context, *SearchApiCaseInApiSuiteReq) (*SearchApiCaseInApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchApiCaseInApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) SearchApiCaseNotInApiSuite(context.Context, *SearchApiCaseNotInApiSuiteReq) (*SearchApiCaseNotInApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchApiCaseNotInApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) AddApiCaseToApiSuite(context.Context, *AddApiCaseToApiSuiteReq) (*AddApiCaseToApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddApiCaseToApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) RemoveApiCaseFromApiSuite(context.Context, *RemoveApiCaseFromApiSuiteReq) (*RemoveApiCaseFromApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveApiCaseFromApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) SearchApiSuiteReference(context.Context, *SearchApiSuiteReferenceReq) (*SearchApiSuiteReferenceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchApiSuiteReference not implemented")
}
func (UnimplementedApiSuiteServiceServer) ModifyApiSuiteReferenceState(context.Context, *ModifyApiSuiteReferenceStateReq) (*ModifyApiSuiteReferenceStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyApiSuiteReferenceState not implemented")
}
func (UnimplementedApiSuiteServiceServer) SearchCaseInApiSuite(context.Context, *SearchCaseInApiSuiteReq) (*SearchCaseInApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCaseInApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) AddCaseToApiSuite(context.Context, *AddCaseToApiSuiteReq) (*AddCaseToApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCaseToApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) RemoveCaseFromApiSuite(context.Context, *RemoveCaseFromApiSuiteReq) (*RemoveCaseFromApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveCaseFromApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) SearchServiceCaseNotInApiSuite(context.Context, *SearchServiceCaseNotInApiSuiteReq) (*SearchServiceCaseNotInApiSuiteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchServiceCaseNotInApiSuite not implemented")
}
func (UnimplementedApiSuiteServiceServer) mustEmbedUnimplementedApiSuiteServiceServer() {}
func (UnimplementedApiSuiteServiceServer) testEmbeddedByValue()                         {}

// UnsafeApiSuiteServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiSuiteServiceServer will
// result in compilation errors.
type UnsafeApiSuiteServiceServer interface {
	mustEmbedUnimplementedApiSuiteServiceServer()
}

func RegisterApiSuiteServiceServer(s grpc.ServiceRegistrar, srv ApiSuiteServiceServer) {
	// If the following call pancis, it indicates UnimplementedApiSuiteServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ApiSuiteService_ServiceDesc, srv)
}

func _ApiSuiteService_CreateApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).CreateApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_CreateApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).CreateApiSuite(ctx, req.(*CreateApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_RemoveApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).RemoveApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_RemoveApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).RemoveApiSuite(ctx, req.(*RemoveApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_ModifyApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).ModifyApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_ModifyApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).ModifyApiSuite(ctx, req.(*ModifyApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_SearchApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).SearchApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_SearchApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).SearchApiSuite(ctx, req.(*SearchApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_ViewApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).ViewApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_ViewApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).ViewApiSuite(ctx, req.(*ViewApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_SearchApiCaseInApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchApiCaseInApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).SearchApiCaseInApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_SearchApiCaseInApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).SearchApiCaseInApiSuite(ctx, req.(*SearchApiCaseInApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_SearchApiCaseNotInApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchApiCaseNotInApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).SearchApiCaseNotInApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_SearchApiCaseNotInApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).SearchApiCaseNotInApiSuite(ctx, req.(*SearchApiCaseNotInApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_AddApiCaseToApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddApiCaseToApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).AddApiCaseToApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_AddApiCaseToApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).AddApiCaseToApiSuite(ctx, req.(*AddApiCaseToApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_RemoveApiCaseFromApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveApiCaseFromApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).RemoveApiCaseFromApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_RemoveApiCaseFromApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).RemoveApiCaseFromApiSuite(ctx, req.(*RemoveApiCaseFromApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_SearchApiSuiteReference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchApiSuiteReferenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).SearchApiSuiteReference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_SearchApiSuiteReference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).SearchApiSuiteReference(ctx, req.(*SearchApiSuiteReferenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_ModifyApiSuiteReferenceState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyApiSuiteReferenceStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).ModifyApiSuiteReferenceState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_ModifyApiSuiteReferenceState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).ModifyApiSuiteReferenceState(ctx, req.(*ModifyApiSuiteReferenceStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_SearchCaseInApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCaseInApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).SearchCaseInApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_SearchCaseInApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).SearchCaseInApiSuite(ctx, req.(*SearchCaseInApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_AddCaseToApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCaseToApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).AddCaseToApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_AddCaseToApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).AddCaseToApiSuite(ctx, req.(*AddCaseToApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_RemoveCaseFromApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveCaseFromApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).RemoveCaseFromApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_RemoveCaseFromApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).RemoveCaseFromApiSuite(ctx, req.(*RemoveCaseFromApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiSuiteService_SearchServiceCaseNotInApiSuite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchServiceCaseNotInApiSuiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiSuiteServiceServer).SearchServiceCaseNotInApiSuite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiSuiteService_SearchServiceCaseNotInApiSuite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiSuiteServiceServer).SearchServiceCaseNotInApiSuite(ctx, req.(*SearchServiceCaseNotInApiSuiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ApiSuiteService_ServiceDesc is the grpc.ServiceDesc for ApiSuiteService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApiSuiteService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ApiSuiteService",
	HandlerType: (*ApiSuiteServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateApiSuite",
			Handler:    _ApiSuiteService_CreateApiSuite_Handler,
		},
		{
			MethodName: "RemoveApiSuite",
			Handler:    _ApiSuiteService_RemoveApiSuite_Handler,
		},
		{
			MethodName: "ModifyApiSuite",
			Handler:    _ApiSuiteService_ModifyApiSuite_Handler,
		},
		{
			MethodName: "SearchApiSuite",
			Handler:    _ApiSuiteService_SearchApiSuite_Handler,
		},
		{
			MethodName: "ViewApiSuite",
			Handler:    _ApiSuiteService_ViewApiSuite_Handler,
		},
		{
			MethodName: "SearchApiCaseInApiSuite",
			Handler:    _ApiSuiteService_SearchApiCaseInApiSuite_Handler,
		},
		{
			MethodName: "SearchApiCaseNotInApiSuite",
			Handler:    _ApiSuiteService_SearchApiCaseNotInApiSuite_Handler,
		},
		{
			MethodName: "AddApiCaseToApiSuite",
			Handler:    _ApiSuiteService_AddApiCaseToApiSuite_Handler,
		},
		{
			MethodName: "RemoveApiCaseFromApiSuite",
			Handler:    _ApiSuiteService_RemoveApiCaseFromApiSuite_Handler,
		},
		{
			MethodName: "SearchApiSuiteReference",
			Handler:    _ApiSuiteService_SearchApiSuiteReference_Handler,
		},
		{
			MethodName: "ModifyApiSuiteReferenceState",
			Handler:    _ApiSuiteService_ModifyApiSuiteReferenceState_Handler,
		},
		{
			MethodName: "SearchCaseInApiSuite",
			Handler:    _ApiSuiteService_SearchCaseInApiSuite_Handler,
		},
		{
			MethodName: "AddCaseToApiSuite",
			Handler:    _ApiSuiteService_AddCaseToApiSuite_Handler,
		},
		{
			MethodName: "RemoveCaseFromApiSuite",
			Handler:    _ApiSuiteService_RemoveCaseFromApiSuite_Handler,
		},
		{
			MethodName: "SearchServiceCaseNotInApiSuite",
			Handler:    _ApiSuiteService_SearchServiceCaseNotInApiSuite_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	ApiPlanService_CreateApiPlan_FullMethodName                   = "/manager.ApiPlanService/CreateApiPlan"
	ApiPlanService_RemoveApiPlan_FullMethodName                   = "/manager.ApiPlanService/RemoveApiPlan"
	ApiPlanService_ModifyApiPlan_FullMethodName                   = "/manager.ApiPlanService/ModifyApiPlan"
	ApiPlanService_SearchApiPlan_FullMethodName                   = "/manager.ApiPlanService/SearchApiPlan"
	ApiPlanService_ViewApiPlan_FullMethodName                     = "/manager.ApiPlanService/ViewApiPlan"
	ApiPlanService_SearchSuiteInApiPlan_FullMethodName            = "/manager.ApiPlanService/SearchSuiteInApiPlan"
	ApiPlanService_SearchSuiteNotInApiPlan_FullMethodName         = "/manager.ApiPlanService/SearchSuiteNotInApiPlan"
	ApiPlanService_AddSuiteToApiPlan_FullMethodName               = "/manager.ApiPlanService/AddSuiteToApiPlan"
	ApiPlanService_RemoveSuiteFromApiPlan_FullMethodName          = "/manager.ApiPlanService/RemoveSuiteFromApiPlan"
	ApiPlanService_SearchCaseInApiPlan_FullMethodName             = "/manager.ApiPlanService/SearchCaseInApiPlan"
	ApiPlanService_ModifyApiPlanReferenceState_FullMethodName     = "/manager.ApiPlanService/ModifyApiPlanReferenceState"
	ApiPlanService_AdvancedSearchSuiteNotInApiPlan_FullMethodName = "/manager.ApiPlanService/AdvancedSearchSuiteNotInApiPlan"
	ApiPlanService_SearchLikeApiPlan_FullMethodName               = "/manager.ApiPlanService/SearchLikeApiPlan"
)

// ApiPlanServiceClient is the client API for ApiPlanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ApiPlanService API计划服务
type ApiPlanServiceClient interface {
	//CreateApiPlan 创建API计划
	CreateApiPlan(ctx context.Context, in *CreateApiPlanReq, opts ...grpc.CallOption) (*CreateApiPlanResp, error)
	//RemoveApiPlan 删除API计划
	RemoveApiPlan(ctx context.Context, in *RemoveApiPlanReq, opts ...grpc.CallOption) (*RemoveApiPlanResp, error)
	//ModifyApiPlan 编辑API计划
	ModifyApiPlan(ctx context.Context, in *ModifyApiPlanReq, opts ...grpc.CallOption) (*ModifyApiPlanResp, error)
	//SearchApiPlan 搜索API计划
	SearchApiPlan(ctx context.Context, in *SearchApiPlanReq, opts ...grpc.CallOption) (*SearchApiPlanResp, error)
	//ViewApiPlan 查看API计划
	ViewApiPlan(ctx context.Context, in *ViewApiPlanReq, opts ...grpc.CallOption) (*ViewApiPlanResp, error)
	//SearchSuiteInApiPlan 搜索API计划中的集合（包括：场景集合和接口集合）
	SearchSuiteInApiPlan(ctx context.Context, in *SearchSuiteInApiPlanReq, opts ...grpc.CallOption) (*SearchSuiteInApiPlanResp, error)
	//SearchSuiteNotInApiPlan 搜索不在指定的API计划中的集合（包括：场景集合和接口集合）
	SearchSuiteNotInApiPlan(ctx context.Context, in *SearchSuiteNotInApiPlanReq, opts ...grpc.CallOption) (*SearchSuiteNotInApiPlanResp, error)
	//AddSuiteToApiPlan 添加集合到API计划中
	AddSuiteToApiPlan(ctx context.Context, in *AddSuiteToApiPlanReq, opts ...grpc.CallOption) (*AddSuiteToApiPlanResp, error)
	//RemoveSuiteFromApiPlan 移除API计划中的集合
	RemoveSuiteFromApiPlan(ctx context.Context, in *RemoveSuiteFromApiPlanReq, opts ...grpc.CallOption) (*RemoveSuiteFromApiPlanResp, error)
	//SearchCaseInApiPlan 搜索API计划中指定集合中的用例
	SearchCaseInApiPlan(ctx context.Context, in *SearchCaseInApiPlanReq, opts ...grpc.CallOption) (*SearchCaseInApiPlanResp, error)
	//ModifyApiPlanReferenceState 修改API计划执行数据的引用状态（包括：集合以及集合中的用例）
	ModifyApiPlanReferenceState(ctx context.Context, in *ModifyApiPlanReferenceStateReq, opts ...grpc.CallOption) (*ModifyApiPlanReferenceStateResp, error)
	// AdvancedSearchSuiteNotInApiPlan 高级搜索接口文档
	AdvancedSearchSuiteNotInApiPlan(ctx context.Context, in *AdvancedSearchSuiteNotInApiPlanReq, opts ...grpc.CallOption) (*AdvancedSearchSuiteNotInApiPlanResp, error)
	// SearchLikeApiPlan 搜索API计划
	SearchLikeApiPlan(ctx context.Context, in *SearchApiPlanReq, opts ...grpc.CallOption) (*SearchApiPlanResp, error)
}

type apiPlanServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApiPlanServiceClient(cc grpc.ClientConnInterface) ApiPlanServiceClient {
	return &apiPlanServiceClient{cc}
}

func (c *apiPlanServiceClient) CreateApiPlan(ctx context.Context, in *CreateApiPlanReq, opts ...grpc.CallOption) (*CreateApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_CreateApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) RemoveApiPlan(ctx context.Context, in *RemoveApiPlanReq, opts ...grpc.CallOption) (*RemoveApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_RemoveApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) ModifyApiPlan(ctx context.Context, in *ModifyApiPlanReq, opts ...grpc.CallOption) (*ModifyApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_ModifyApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) SearchApiPlan(ctx context.Context, in *SearchApiPlanReq, opts ...grpc.CallOption) (*SearchApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_SearchApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) ViewApiPlan(ctx context.Context, in *ViewApiPlanReq, opts ...grpc.CallOption) (*ViewApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_ViewApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) SearchSuiteInApiPlan(ctx context.Context, in *SearchSuiteInApiPlanReq, opts ...grpc.CallOption) (*SearchSuiteInApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchSuiteInApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_SearchSuiteInApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) SearchSuiteNotInApiPlan(ctx context.Context, in *SearchSuiteNotInApiPlanReq, opts ...grpc.CallOption) (*SearchSuiteNotInApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchSuiteNotInApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_SearchSuiteNotInApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) AddSuiteToApiPlan(ctx context.Context, in *AddSuiteToApiPlanReq, opts ...grpc.CallOption) (*AddSuiteToApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddSuiteToApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_AddSuiteToApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) RemoveSuiteFromApiPlan(ctx context.Context, in *RemoveSuiteFromApiPlanReq, opts ...grpc.CallOption) (*RemoveSuiteFromApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveSuiteFromApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_RemoveSuiteFromApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) SearchCaseInApiPlan(ctx context.Context, in *SearchCaseInApiPlanReq, opts ...grpc.CallOption) (*SearchCaseInApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCaseInApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_SearchCaseInApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) ModifyApiPlanReferenceState(ctx context.Context, in *ModifyApiPlanReferenceStateReq, opts ...grpc.CallOption) (*ModifyApiPlanReferenceStateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyApiPlanReferenceStateResp)
	err := c.cc.Invoke(ctx, ApiPlanService_ModifyApiPlanReferenceState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) AdvancedSearchSuiteNotInApiPlan(ctx context.Context, in *AdvancedSearchSuiteNotInApiPlanReq, opts ...grpc.CallOption) (*AdvancedSearchSuiteNotInApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdvancedSearchSuiteNotInApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_AdvancedSearchSuiteNotInApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiPlanServiceClient) SearchLikeApiPlan(ctx context.Context, in *SearchApiPlanReq, opts ...grpc.CallOption) (*SearchApiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchApiPlanResp)
	err := c.cc.Invoke(ctx, ApiPlanService_SearchLikeApiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiPlanServiceServer is the server API for ApiPlanService service.
// All implementations must embed UnimplementedApiPlanServiceServer
// for forward compatibility.
//
// ApiPlanService API计划服务
type ApiPlanServiceServer interface {
	//CreateApiPlan 创建API计划
	CreateApiPlan(context.Context, *CreateApiPlanReq) (*CreateApiPlanResp, error)
	//RemoveApiPlan 删除API计划
	RemoveApiPlan(context.Context, *RemoveApiPlanReq) (*RemoveApiPlanResp, error)
	//ModifyApiPlan 编辑API计划
	ModifyApiPlan(context.Context, *ModifyApiPlanReq) (*ModifyApiPlanResp, error)
	//SearchApiPlan 搜索API计划
	SearchApiPlan(context.Context, *SearchApiPlanReq) (*SearchApiPlanResp, error)
	//ViewApiPlan 查看API计划
	ViewApiPlan(context.Context, *ViewApiPlanReq) (*ViewApiPlanResp, error)
	//SearchSuiteInApiPlan 搜索API计划中的集合（包括：场景集合和接口集合）
	SearchSuiteInApiPlan(context.Context, *SearchSuiteInApiPlanReq) (*SearchSuiteInApiPlanResp, error)
	//SearchSuiteNotInApiPlan 搜索不在指定的API计划中的集合（包括：场景集合和接口集合）
	SearchSuiteNotInApiPlan(context.Context, *SearchSuiteNotInApiPlanReq) (*SearchSuiteNotInApiPlanResp, error)
	//AddSuiteToApiPlan 添加集合到API计划中
	AddSuiteToApiPlan(context.Context, *AddSuiteToApiPlanReq) (*AddSuiteToApiPlanResp, error)
	//RemoveSuiteFromApiPlan 移除API计划中的集合
	RemoveSuiteFromApiPlan(context.Context, *RemoveSuiteFromApiPlanReq) (*RemoveSuiteFromApiPlanResp, error)
	//SearchCaseInApiPlan 搜索API计划中指定集合中的用例
	SearchCaseInApiPlan(context.Context, *SearchCaseInApiPlanReq) (*SearchCaseInApiPlanResp, error)
	//ModifyApiPlanReferenceState 修改API计划执行数据的引用状态（包括：集合以及集合中的用例）
	ModifyApiPlanReferenceState(context.Context, *ModifyApiPlanReferenceStateReq) (*ModifyApiPlanReferenceStateResp, error)
	// AdvancedSearchSuiteNotInApiPlan 高级搜索接口文档
	AdvancedSearchSuiteNotInApiPlan(context.Context, *AdvancedSearchSuiteNotInApiPlanReq) (*AdvancedSearchSuiteNotInApiPlanResp, error)
	// SearchLikeApiPlan 搜索API计划
	SearchLikeApiPlan(context.Context, *SearchApiPlanReq) (*SearchApiPlanResp, error)
	mustEmbedUnimplementedApiPlanServiceServer()
}

// UnimplementedApiPlanServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedApiPlanServiceServer struct{}

func (UnimplementedApiPlanServiceServer) CreateApiPlan(context.Context, *CreateApiPlanReq) (*CreateApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) RemoveApiPlan(context.Context, *RemoveApiPlanReq) (*RemoveApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) ModifyApiPlan(context.Context, *ModifyApiPlanReq) (*ModifyApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) SearchApiPlan(context.Context, *SearchApiPlanReq) (*SearchApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) ViewApiPlan(context.Context, *ViewApiPlanReq) (*ViewApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) SearchSuiteInApiPlan(context.Context, *SearchSuiteInApiPlanReq) (*SearchSuiteInApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchSuiteInApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) SearchSuiteNotInApiPlan(context.Context, *SearchSuiteNotInApiPlanReq) (*SearchSuiteNotInApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchSuiteNotInApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) AddSuiteToApiPlan(context.Context, *AddSuiteToApiPlanReq) (*AddSuiteToApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddSuiteToApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) RemoveSuiteFromApiPlan(context.Context, *RemoveSuiteFromApiPlanReq) (*RemoveSuiteFromApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveSuiteFromApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) SearchCaseInApiPlan(context.Context, *SearchCaseInApiPlanReq) (*SearchCaseInApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCaseInApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) ModifyApiPlanReferenceState(context.Context, *ModifyApiPlanReferenceStateReq) (*ModifyApiPlanReferenceStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyApiPlanReferenceState not implemented")
}
func (UnimplementedApiPlanServiceServer) AdvancedSearchSuiteNotInApiPlan(context.Context, *AdvancedSearchSuiteNotInApiPlanReq) (*AdvancedSearchSuiteNotInApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdvancedSearchSuiteNotInApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) SearchLikeApiPlan(context.Context, *SearchApiPlanReq) (*SearchApiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchLikeApiPlan not implemented")
}
func (UnimplementedApiPlanServiceServer) mustEmbedUnimplementedApiPlanServiceServer() {}
func (UnimplementedApiPlanServiceServer) testEmbeddedByValue()                        {}

// UnsafeApiPlanServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiPlanServiceServer will
// result in compilation errors.
type UnsafeApiPlanServiceServer interface {
	mustEmbedUnimplementedApiPlanServiceServer()
}

func RegisterApiPlanServiceServer(s grpc.ServiceRegistrar, srv ApiPlanServiceServer) {
	// If the following call pancis, it indicates UnimplementedApiPlanServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ApiPlanService_ServiceDesc, srv)
}

func _ApiPlanService_CreateApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).CreateApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_CreateApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).CreateApiPlan(ctx, req.(*CreateApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_RemoveApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).RemoveApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_RemoveApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).RemoveApiPlan(ctx, req.(*RemoveApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_ModifyApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).ModifyApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_ModifyApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).ModifyApiPlan(ctx, req.(*ModifyApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_SearchApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).SearchApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_SearchApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).SearchApiPlan(ctx, req.(*SearchApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_ViewApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).ViewApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_ViewApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).ViewApiPlan(ctx, req.(*ViewApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_SearchSuiteInApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchSuiteInApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).SearchSuiteInApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_SearchSuiteInApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).SearchSuiteInApiPlan(ctx, req.(*SearchSuiteInApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_SearchSuiteNotInApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchSuiteNotInApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).SearchSuiteNotInApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_SearchSuiteNotInApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).SearchSuiteNotInApiPlan(ctx, req.(*SearchSuiteNotInApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_AddSuiteToApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSuiteToApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).AddSuiteToApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_AddSuiteToApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).AddSuiteToApiPlan(ctx, req.(*AddSuiteToApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_RemoveSuiteFromApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveSuiteFromApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).RemoveSuiteFromApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_RemoveSuiteFromApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).RemoveSuiteFromApiPlan(ctx, req.(*RemoveSuiteFromApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_SearchCaseInApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCaseInApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).SearchCaseInApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_SearchCaseInApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).SearchCaseInApiPlan(ctx, req.(*SearchCaseInApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_ModifyApiPlanReferenceState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyApiPlanReferenceStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).ModifyApiPlanReferenceState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_ModifyApiPlanReferenceState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).ModifyApiPlanReferenceState(ctx, req.(*ModifyApiPlanReferenceStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_AdvancedSearchSuiteNotInApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdvancedSearchSuiteNotInApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).AdvancedSearchSuiteNotInApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_AdvancedSearchSuiteNotInApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).AdvancedSearchSuiteNotInApiPlan(ctx, req.(*AdvancedSearchSuiteNotInApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApiPlanService_SearchLikeApiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchApiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiPlanServiceServer).SearchLikeApiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApiPlanService_SearchLikeApiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiPlanServiceServer).SearchLikeApiPlan(ctx, req.(*SearchApiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ApiPlanService_ServiceDesc is the grpc.ServiceDesc for ApiPlanService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApiPlanService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ApiPlanService",
	HandlerType: (*ApiPlanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateApiPlan",
			Handler:    _ApiPlanService_CreateApiPlan_Handler,
		},
		{
			MethodName: "RemoveApiPlan",
			Handler:    _ApiPlanService_RemoveApiPlan_Handler,
		},
		{
			MethodName: "ModifyApiPlan",
			Handler:    _ApiPlanService_ModifyApiPlan_Handler,
		},
		{
			MethodName: "SearchApiPlan",
			Handler:    _ApiPlanService_SearchApiPlan_Handler,
		},
		{
			MethodName: "ViewApiPlan",
			Handler:    _ApiPlanService_ViewApiPlan_Handler,
		},
		{
			MethodName: "SearchSuiteInApiPlan",
			Handler:    _ApiPlanService_SearchSuiteInApiPlan_Handler,
		},
		{
			MethodName: "SearchSuiteNotInApiPlan",
			Handler:    _ApiPlanService_SearchSuiteNotInApiPlan_Handler,
		},
		{
			MethodName: "AddSuiteToApiPlan",
			Handler:    _ApiPlanService_AddSuiteToApiPlan_Handler,
		},
		{
			MethodName: "RemoveSuiteFromApiPlan",
			Handler:    _ApiPlanService_RemoveSuiteFromApiPlan_Handler,
		},
		{
			MethodName: "SearchCaseInApiPlan",
			Handler:    _ApiPlanService_SearchCaseInApiPlan_Handler,
		},
		{
			MethodName: "ModifyApiPlanReferenceState",
			Handler:    _ApiPlanService_ModifyApiPlanReferenceState_Handler,
		},
		{
			MethodName: "AdvancedSearchSuiteNotInApiPlan",
			Handler:    _ApiPlanService_AdvancedSearchSuiteNotInApiPlan_Handler,
		},
		{
			MethodName: "SearchLikeApiPlan",
			Handler:    _ApiPlanService_SearchLikeApiPlan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	AdvancedSearchService_SearchAdvancedSearchField_FullMethodName     = "/manager.AdvancedSearchService/SearchAdvancedSearchField"
	AdvancedSearchService_SearchAdvancedSearchCondition_FullMethodName = "/manager.AdvancedSearchService/SearchAdvancedSearchCondition"
)

// AdvancedSearchServiceClient is the client API for AdvancedSearchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdvancedSearchServiceClient interface {
	SearchAdvancedSearchField(ctx context.Context, in *SearchAdvancedSearchFieldReq, opts ...grpc.CallOption) (*SearchAdvancedSearchFieldResp, error)
	SearchAdvancedSearchCondition(ctx context.Context, in *SearchAdvancedSearchConditionReq, opts ...grpc.CallOption) (*SearchAdvancedSearchConditionResp, error)
}

type advancedSearchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAdvancedSearchServiceClient(cc grpc.ClientConnInterface) AdvancedSearchServiceClient {
	return &advancedSearchServiceClient{cc}
}

func (c *advancedSearchServiceClient) SearchAdvancedSearchField(ctx context.Context, in *SearchAdvancedSearchFieldReq, opts ...grpc.CallOption) (*SearchAdvancedSearchFieldResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchAdvancedSearchFieldResp)
	err := c.cc.Invoke(ctx, AdvancedSearchService_SearchAdvancedSearchField_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *advancedSearchServiceClient) SearchAdvancedSearchCondition(ctx context.Context, in *SearchAdvancedSearchConditionReq, opts ...grpc.CallOption) (*SearchAdvancedSearchConditionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchAdvancedSearchConditionResp)
	err := c.cc.Invoke(ctx, AdvancedSearchService_SearchAdvancedSearchCondition_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdvancedSearchServiceServer is the server API for AdvancedSearchService service.
// All implementations must embed UnimplementedAdvancedSearchServiceServer
// for forward compatibility.
type AdvancedSearchServiceServer interface {
	SearchAdvancedSearchField(context.Context, *SearchAdvancedSearchFieldReq) (*SearchAdvancedSearchFieldResp, error)
	SearchAdvancedSearchCondition(context.Context, *SearchAdvancedSearchConditionReq) (*SearchAdvancedSearchConditionResp, error)
	mustEmbedUnimplementedAdvancedSearchServiceServer()
}

// UnimplementedAdvancedSearchServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAdvancedSearchServiceServer struct{}

func (UnimplementedAdvancedSearchServiceServer) SearchAdvancedSearchField(context.Context, *SearchAdvancedSearchFieldReq) (*SearchAdvancedSearchFieldResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAdvancedSearchField not implemented")
}
func (UnimplementedAdvancedSearchServiceServer) SearchAdvancedSearchCondition(context.Context, *SearchAdvancedSearchConditionReq) (*SearchAdvancedSearchConditionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAdvancedSearchCondition not implemented")
}
func (UnimplementedAdvancedSearchServiceServer) mustEmbedUnimplementedAdvancedSearchServiceServer() {}
func (UnimplementedAdvancedSearchServiceServer) testEmbeddedByValue()                               {}

// UnsafeAdvancedSearchServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdvancedSearchServiceServer will
// result in compilation errors.
type UnsafeAdvancedSearchServiceServer interface {
	mustEmbedUnimplementedAdvancedSearchServiceServer()
}

func RegisterAdvancedSearchServiceServer(s grpc.ServiceRegistrar, srv AdvancedSearchServiceServer) {
	// If the following call pancis, it indicates UnimplementedAdvancedSearchServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AdvancedSearchService_ServiceDesc, srv)
}

func _AdvancedSearchService_SearchAdvancedSearchField_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAdvancedSearchFieldReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdvancedSearchServiceServer).SearchAdvancedSearchField(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdvancedSearchService_SearchAdvancedSearchField_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdvancedSearchServiceServer).SearchAdvancedSearchField(ctx, req.(*SearchAdvancedSearchFieldReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdvancedSearchService_SearchAdvancedSearchCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAdvancedSearchConditionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdvancedSearchServiceServer).SearchAdvancedSearchCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdvancedSearchService_SearchAdvancedSearchCondition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdvancedSearchServiceServer).SearchAdvancedSearchCondition(ctx, req.(*SearchAdvancedSearchConditionReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AdvancedSearchService_ServiceDesc is the grpc.ServiceDesc for AdvancedSearchService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdvancedSearchService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.AdvancedSearchService",
	HandlerType: (*AdvancedSearchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchAdvancedSearchField",
			Handler:    _AdvancedSearchService_SearchAdvancedSearchField_Handler,
		},
		{
			MethodName: "SearchAdvancedSearchCondition",
			Handler:    _AdvancedSearchService_SearchAdvancedSearchCondition_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	NotifyService_CreatePlanNotify_FullMethodName = "/manager.NotifyService/CreatePlanNotify"
	NotifyService_RemovePlanNotify_FullMethodName = "/manager.NotifyService/RemovePlanNotify"
	NotifyService_ModifyPlanNotify_FullMethodName = "/manager.NotifyService/ModifyPlanNotify"
	NotifyService_SearchPlanNotify_FullMethodName = "/manager.NotifyService/SearchPlanNotify"
	NotifyService_GetPlanNotify_FullMethodName    = "/manager.NotifyService/GetPlanNotify"
)

// NotifyServiceClient is the client API for NotifyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NotifyServiceClient interface {
	CreatePlanNotify(ctx context.Context, in *CreatePlanNotifyReq, opts ...grpc.CallOption) (*CreatePlanNotifyResp, error)
	RemovePlanNotify(ctx context.Context, in *RemovePlanNotifyReq, opts ...grpc.CallOption) (*RemovePlanNotifyResp, error)
	ModifyPlanNotify(ctx context.Context, in *ModifyPlanNotifyReq, opts ...grpc.CallOption) (*ModifyPlanNotifyResp, error)
	SearchPlanNotify(ctx context.Context, in *SearchPlanNotifyReq, opts ...grpc.CallOption) (*SearchPlanNotifyResp, error)
	GetPlanNotify(ctx context.Context, in *GetPlanNotifyReq, opts ...grpc.CallOption) (*GetPlanNotifyResp, error)
}

type notifyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNotifyServiceClient(cc grpc.ClientConnInterface) NotifyServiceClient {
	return &notifyServiceClient{cc}
}

func (c *notifyServiceClient) CreatePlanNotify(ctx context.Context, in *CreatePlanNotifyReq, opts ...grpc.CallOption) (*CreatePlanNotifyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePlanNotifyResp)
	err := c.cc.Invoke(ctx, NotifyService_CreatePlanNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyServiceClient) RemovePlanNotify(ctx context.Context, in *RemovePlanNotifyReq, opts ...grpc.CallOption) (*RemovePlanNotifyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePlanNotifyResp)
	err := c.cc.Invoke(ctx, NotifyService_RemovePlanNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyServiceClient) ModifyPlanNotify(ctx context.Context, in *ModifyPlanNotifyReq, opts ...grpc.CallOption) (*ModifyPlanNotifyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPlanNotifyResp)
	err := c.cc.Invoke(ctx, NotifyService_ModifyPlanNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyServiceClient) SearchPlanNotify(ctx context.Context, in *SearchPlanNotifyReq, opts ...grpc.CallOption) (*SearchPlanNotifyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPlanNotifyResp)
	err := c.cc.Invoke(ctx, NotifyService_SearchPlanNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyServiceClient) GetPlanNotify(ctx context.Context, in *GetPlanNotifyReq, opts ...grpc.CallOption) (*GetPlanNotifyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPlanNotifyResp)
	err := c.cc.Invoke(ctx, NotifyService_GetPlanNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotifyServiceServer is the server API for NotifyService service.
// All implementations must embed UnimplementedNotifyServiceServer
// for forward compatibility.
type NotifyServiceServer interface {
	CreatePlanNotify(context.Context, *CreatePlanNotifyReq) (*CreatePlanNotifyResp, error)
	RemovePlanNotify(context.Context, *RemovePlanNotifyReq) (*RemovePlanNotifyResp, error)
	ModifyPlanNotify(context.Context, *ModifyPlanNotifyReq) (*ModifyPlanNotifyResp, error)
	SearchPlanNotify(context.Context, *SearchPlanNotifyReq) (*SearchPlanNotifyResp, error)
	GetPlanNotify(context.Context, *GetPlanNotifyReq) (*GetPlanNotifyResp, error)
	mustEmbedUnimplementedNotifyServiceServer()
}

// UnimplementedNotifyServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedNotifyServiceServer struct{}

func (UnimplementedNotifyServiceServer) CreatePlanNotify(context.Context, *CreatePlanNotifyReq) (*CreatePlanNotifyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlanNotify not implemented")
}
func (UnimplementedNotifyServiceServer) RemovePlanNotify(context.Context, *RemovePlanNotifyReq) (*RemovePlanNotifyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePlanNotify not implemented")
}
func (UnimplementedNotifyServiceServer) ModifyPlanNotify(context.Context, *ModifyPlanNotifyReq) (*ModifyPlanNotifyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPlanNotify not implemented")
}
func (UnimplementedNotifyServiceServer) SearchPlanNotify(context.Context, *SearchPlanNotifyReq) (*SearchPlanNotifyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPlanNotify not implemented")
}
func (UnimplementedNotifyServiceServer) GetPlanNotify(context.Context, *GetPlanNotifyReq) (*GetPlanNotifyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanNotify not implemented")
}
func (UnimplementedNotifyServiceServer) mustEmbedUnimplementedNotifyServiceServer() {}
func (UnimplementedNotifyServiceServer) testEmbeddedByValue()                       {}

// UnsafeNotifyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotifyServiceServer will
// result in compilation errors.
type UnsafeNotifyServiceServer interface {
	mustEmbedUnimplementedNotifyServiceServer()
}

func RegisterNotifyServiceServer(s grpc.ServiceRegistrar, srv NotifyServiceServer) {
	// If the following call pancis, it indicates UnimplementedNotifyServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&NotifyService_ServiceDesc, srv)
}

func _NotifyService_CreatePlanNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePlanNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServiceServer).CreatePlanNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotifyService_CreatePlanNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServiceServer).CreatePlanNotify(ctx, req.(*CreatePlanNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotifyService_RemovePlanNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePlanNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServiceServer).RemovePlanNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotifyService_RemovePlanNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServiceServer).RemovePlanNotify(ctx, req.(*RemovePlanNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotifyService_ModifyPlanNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPlanNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServiceServer).ModifyPlanNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotifyService_ModifyPlanNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServiceServer).ModifyPlanNotify(ctx, req.(*ModifyPlanNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotifyService_SearchPlanNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPlanNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServiceServer).SearchPlanNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotifyService_SearchPlanNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServiceServer).SearchPlanNotify(ctx, req.(*SearchPlanNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotifyService_GetPlanNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlanNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServiceServer).GetPlanNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotifyService_GetPlanNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServiceServer).GetPlanNotify(ctx, req.(*GetPlanNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

// NotifyService_ServiceDesc is the grpc.ServiceDesc for NotifyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NotifyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.NotifyService",
	HandlerType: (*NotifyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePlanNotify",
			Handler:    _NotifyService_CreatePlanNotify_Handler,
		},
		{
			MethodName: "RemovePlanNotify",
			Handler:    _NotifyService_RemovePlanNotify_Handler,
		},
		{
			MethodName: "ModifyPlanNotify",
			Handler:    _NotifyService_ModifyPlanNotify_Handler,
		},
		{
			MethodName: "SearchPlanNotify",
			Handler:    _NotifyService_SearchPlanNotify_Handler,
		},
		{
			MethodName: "GetPlanNotify",
			Handler:    _NotifyService_GetPlanNotify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	GitConfigurationService_CreateGitConfiguration_FullMethodName        = "/manager.GitConfigurationService/CreateGitConfiguration"
	GitConfigurationService_RemoveGitConfiguration_FullMethodName        = "/manager.GitConfigurationService/RemoveGitConfiguration"
	GitConfigurationService_ModifyGitConfiguration_FullMethodName        = "/manager.GitConfigurationService/ModifyGitConfiguration"
	GitConfigurationService_SearchGitConfiguration_FullMethodName        = "/manager.GitConfigurationService/SearchGitConfiguration"
	GitConfigurationService_ViewGitConfiguration_FullMethodName          = "/manager.GitConfigurationService/ViewGitConfiguration"
	GitConfigurationService_TestGitConfiguration_FullMethodName          = "/manager.GitConfigurationService/TestGitConfiguration"
	GitConfigurationService_SyncGitConfiguration_FullMethodName          = "/manager.GitConfigurationService/SyncGitConfiguration"
	GitConfigurationService_SyncGitConfigurationByWebhook_FullMethodName = "/manager.GitConfigurationService/SyncGitConfigurationByWebhook"
)

// GitConfigurationServiceClient is the client API for GitConfigurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// GitConfigurationService Git配置服务
type GitConfigurationServiceClient interface {
	//CreateGitConfiguration 创建Git配置
	CreateGitConfiguration(ctx context.Context, in *CreateGitConfigurationReq, opts ...grpc.CallOption) (*CreateGitConfigurationResp, error)
	//RemoveGitConfiguration 删除Git配置
	RemoveGitConfiguration(ctx context.Context, in *RemoveGitConfigurationReq, opts ...grpc.CallOption) (*RemoveGitConfigurationResp, error)
	//ModifyGitConfiguration 编辑Git配置
	ModifyGitConfiguration(ctx context.Context, in *ModifyGitConfigurationReq, opts ...grpc.CallOption) (*ModifyGitConfigurationResp, error)
	//SearchGitConfiguration 搜索Git配置
	SearchGitConfiguration(ctx context.Context, in *SearchGitConfigurationReq, opts ...grpc.CallOption) (*SearchGitConfigurationResp, error)
	//ViewGitConfiguration 查看Git配置
	ViewGitConfiguration(ctx context.Context, in *ViewGitConfigurationReq, opts ...grpc.CallOption) (*ViewGitConfigurationResp, error)
	//TestGitConfiguration 测试Git配置
	TestGitConfiguration(ctx context.Context, in *TestGitConfigurationReq, opts ...grpc.CallOption) (*TestGitConfigurationResp, error)
	//SyncGitConfiguration 通过手动同步Git配置对应的Git项目测试数据
	SyncGitConfiguration(ctx context.Context, in *SyncGitConfigurationReq, opts ...grpc.CallOption) (*SyncGitConfigurationResp, error)
	//SyncGitConfigurationByWebhook 通过Webhook同步Git配置对应的Git项目测试数据
	SyncGitConfigurationByWebhook(ctx context.Context, in *SyncGitConfigurationByWebhookReq, opts ...grpc.CallOption) (*SyncGitConfigurationByWebhookResp, error)
}

type gitConfigurationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGitConfigurationServiceClient(cc grpc.ClientConnInterface) GitConfigurationServiceClient {
	return &gitConfigurationServiceClient{cc}
}

func (c *gitConfigurationServiceClient) CreateGitConfiguration(ctx context.Context, in *CreateGitConfigurationReq, opts ...grpc.CallOption) (*CreateGitConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateGitConfigurationResp)
	err := c.cc.Invoke(ctx, GitConfigurationService_CreateGitConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gitConfigurationServiceClient) RemoveGitConfiguration(ctx context.Context, in *RemoveGitConfigurationReq, opts ...grpc.CallOption) (*RemoveGitConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveGitConfigurationResp)
	err := c.cc.Invoke(ctx, GitConfigurationService_RemoveGitConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gitConfigurationServiceClient) ModifyGitConfiguration(ctx context.Context, in *ModifyGitConfigurationReq, opts ...grpc.CallOption) (*ModifyGitConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyGitConfigurationResp)
	err := c.cc.Invoke(ctx, GitConfigurationService_ModifyGitConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gitConfigurationServiceClient) SearchGitConfiguration(ctx context.Context, in *SearchGitConfigurationReq, opts ...grpc.CallOption) (*SearchGitConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchGitConfigurationResp)
	err := c.cc.Invoke(ctx, GitConfigurationService_SearchGitConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gitConfigurationServiceClient) ViewGitConfiguration(ctx context.Context, in *ViewGitConfigurationReq, opts ...grpc.CallOption) (*ViewGitConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewGitConfigurationResp)
	err := c.cc.Invoke(ctx, GitConfigurationService_ViewGitConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gitConfigurationServiceClient) TestGitConfiguration(ctx context.Context, in *TestGitConfigurationReq, opts ...grpc.CallOption) (*TestGitConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TestGitConfigurationResp)
	err := c.cc.Invoke(ctx, GitConfigurationService_TestGitConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gitConfigurationServiceClient) SyncGitConfiguration(ctx context.Context, in *SyncGitConfigurationReq, opts ...grpc.CallOption) (*SyncGitConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncGitConfigurationResp)
	err := c.cc.Invoke(ctx, GitConfigurationService_SyncGitConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gitConfigurationServiceClient) SyncGitConfigurationByWebhook(ctx context.Context, in *SyncGitConfigurationByWebhookReq, opts ...grpc.CallOption) (*SyncGitConfigurationByWebhookResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncGitConfigurationByWebhookResp)
	err := c.cc.Invoke(ctx, GitConfigurationService_SyncGitConfigurationByWebhook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GitConfigurationServiceServer is the server API for GitConfigurationService service.
// All implementations must embed UnimplementedGitConfigurationServiceServer
// for forward compatibility.
//
// GitConfigurationService Git配置服务
type GitConfigurationServiceServer interface {
	//CreateGitConfiguration 创建Git配置
	CreateGitConfiguration(context.Context, *CreateGitConfigurationReq) (*CreateGitConfigurationResp, error)
	//RemoveGitConfiguration 删除Git配置
	RemoveGitConfiguration(context.Context, *RemoveGitConfigurationReq) (*RemoveGitConfigurationResp, error)
	//ModifyGitConfiguration 编辑Git配置
	ModifyGitConfiguration(context.Context, *ModifyGitConfigurationReq) (*ModifyGitConfigurationResp, error)
	//SearchGitConfiguration 搜索Git配置
	SearchGitConfiguration(context.Context, *SearchGitConfigurationReq) (*SearchGitConfigurationResp, error)
	//ViewGitConfiguration 查看Git配置
	ViewGitConfiguration(context.Context, *ViewGitConfigurationReq) (*ViewGitConfigurationResp, error)
	//TestGitConfiguration 测试Git配置
	TestGitConfiguration(context.Context, *TestGitConfigurationReq) (*TestGitConfigurationResp, error)
	//SyncGitConfiguration 通过手动同步Git配置对应的Git项目测试数据
	SyncGitConfiguration(context.Context, *SyncGitConfigurationReq) (*SyncGitConfigurationResp, error)
	//SyncGitConfigurationByWebhook 通过Webhook同步Git配置对应的Git项目测试数据
	SyncGitConfigurationByWebhook(context.Context, *SyncGitConfigurationByWebhookReq) (*SyncGitConfigurationByWebhookResp, error)
	mustEmbedUnimplementedGitConfigurationServiceServer()
}

// UnimplementedGitConfigurationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGitConfigurationServiceServer struct{}

func (UnimplementedGitConfigurationServiceServer) CreateGitConfiguration(context.Context, *CreateGitConfigurationReq) (*CreateGitConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGitConfiguration not implemented")
}
func (UnimplementedGitConfigurationServiceServer) RemoveGitConfiguration(context.Context, *RemoveGitConfigurationReq) (*RemoveGitConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveGitConfiguration not implemented")
}
func (UnimplementedGitConfigurationServiceServer) ModifyGitConfiguration(context.Context, *ModifyGitConfigurationReq) (*ModifyGitConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyGitConfiguration not implemented")
}
func (UnimplementedGitConfigurationServiceServer) SearchGitConfiguration(context.Context, *SearchGitConfigurationReq) (*SearchGitConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchGitConfiguration not implemented")
}
func (UnimplementedGitConfigurationServiceServer) ViewGitConfiguration(context.Context, *ViewGitConfigurationReq) (*ViewGitConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewGitConfiguration not implemented")
}
func (UnimplementedGitConfigurationServiceServer) TestGitConfiguration(context.Context, *TestGitConfigurationReq) (*TestGitConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TestGitConfiguration not implemented")
}
func (UnimplementedGitConfigurationServiceServer) SyncGitConfiguration(context.Context, *SyncGitConfigurationReq) (*SyncGitConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncGitConfiguration not implemented")
}
func (UnimplementedGitConfigurationServiceServer) SyncGitConfigurationByWebhook(context.Context, *SyncGitConfigurationByWebhookReq) (*SyncGitConfigurationByWebhookResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncGitConfigurationByWebhook not implemented")
}
func (UnimplementedGitConfigurationServiceServer) mustEmbedUnimplementedGitConfigurationServiceServer() {
}
func (UnimplementedGitConfigurationServiceServer) testEmbeddedByValue() {}

// UnsafeGitConfigurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GitConfigurationServiceServer will
// result in compilation errors.
type UnsafeGitConfigurationServiceServer interface {
	mustEmbedUnimplementedGitConfigurationServiceServer()
}

func RegisterGitConfigurationServiceServer(s grpc.ServiceRegistrar, srv GitConfigurationServiceServer) {
	// If the following call pancis, it indicates UnimplementedGitConfigurationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GitConfigurationService_ServiceDesc, srv)
}

func _GitConfigurationService_CreateGitConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGitConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GitConfigurationServiceServer).CreateGitConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GitConfigurationService_CreateGitConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GitConfigurationServiceServer).CreateGitConfiguration(ctx, req.(*CreateGitConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GitConfigurationService_RemoveGitConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveGitConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GitConfigurationServiceServer).RemoveGitConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GitConfigurationService_RemoveGitConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GitConfigurationServiceServer).RemoveGitConfiguration(ctx, req.(*RemoveGitConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GitConfigurationService_ModifyGitConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGitConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GitConfigurationServiceServer).ModifyGitConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GitConfigurationService_ModifyGitConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GitConfigurationServiceServer).ModifyGitConfiguration(ctx, req.(*ModifyGitConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GitConfigurationService_SearchGitConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchGitConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GitConfigurationServiceServer).SearchGitConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GitConfigurationService_SearchGitConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GitConfigurationServiceServer).SearchGitConfiguration(ctx, req.(*SearchGitConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GitConfigurationService_ViewGitConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewGitConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GitConfigurationServiceServer).ViewGitConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GitConfigurationService_ViewGitConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GitConfigurationServiceServer).ViewGitConfiguration(ctx, req.(*ViewGitConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GitConfigurationService_TestGitConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestGitConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GitConfigurationServiceServer).TestGitConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GitConfigurationService_TestGitConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GitConfigurationServiceServer).TestGitConfiguration(ctx, req.(*TestGitConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GitConfigurationService_SyncGitConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncGitConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GitConfigurationServiceServer).SyncGitConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GitConfigurationService_SyncGitConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GitConfigurationServiceServer).SyncGitConfiguration(ctx, req.(*SyncGitConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GitConfigurationService_SyncGitConfigurationByWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncGitConfigurationByWebhookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GitConfigurationServiceServer).SyncGitConfigurationByWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GitConfigurationService_SyncGitConfigurationByWebhook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GitConfigurationServiceServer).SyncGitConfigurationByWebhook(ctx, req.(*SyncGitConfigurationByWebhookReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GitConfigurationService_ServiceDesc is the grpc.ServiceDesc for GitConfigurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GitConfigurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.GitConfigurationService",
	HandlerType: (*GitConfigurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGitConfiguration",
			Handler:    _GitConfigurationService_CreateGitConfiguration_Handler,
		},
		{
			MethodName: "RemoveGitConfiguration",
			Handler:    _GitConfigurationService_RemoveGitConfiguration_Handler,
		},
		{
			MethodName: "ModifyGitConfiguration",
			Handler:    _GitConfigurationService_ModifyGitConfiguration_Handler,
		},
		{
			MethodName: "SearchGitConfiguration",
			Handler:    _GitConfigurationService_SearchGitConfiguration_Handler,
		},
		{
			MethodName: "ViewGitConfiguration",
			Handler:    _GitConfigurationService_ViewGitConfiguration_Handler,
		},
		{
			MethodName: "TestGitConfiguration",
			Handler:    _GitConfigurationService_TestGitConfiguration_Handler,
		},
		{
			MethodName: "SyncGitConfiguration",
			Handler:    _GitConfigurationService_SyncGitConfiguration_Handler,
		},
		{
			MethodName: "SyncGitConfigurationByWebhook",
			Handler:    _GitConfigurationService_SyncGitConfigurationByWebhook_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	UiPlanService_CreateUiPlan_FullMethodName                    = "/manager.UiPlanService/CreateUiPlan"
	UiPlanService_RemoveUiPlan_FullMethodName                    = "/manager.UiPlanService/RemoveUiPlan"
	UiPlanService_ModifyUiPlan_FullMethodName                    = "/manager.UiPlanService/ModifyUiPlan"
	UiPlanService_SearchUiPlan_FullMethodName                    = "/manager.UiPlanService/SearchUiPlan"
	UiPlanService_ViewUiPlan_FullMethodName                      = "/manager.UiPlanService/ViewUiPlan"
	UiPlanService_SearchUiPlanByProjectIdConfigId_FullMethodName = "/manager.UiPlanService/SearchUiPlanByProjectIdConfigId"
	UiPlanService_GetCaseTreeOfUIPlan_FullMethodName             = "/manager.UiPlanService/GetCaseTreeOfUIPlan"
	UiPlanService_GetCaseTreeOfNotAddedToUIPlan_FullMethodName   = "/manager.UiPlanService/GetCaseTreeOfNotAddedToUIPlan"
	UiPlanService_SearchCaseInUIPlan_FullMethodName              = "/manager.UiPlanService/SearchCaseInUIPlan"
	UiPlanService_ListDisableCaseInUIPlan_FullMethodName         = "/manager.UiPlanService/ListDisableCaseInUIPlan"
	UiPlanService_SearchCaseNotInUIPlan_FullMethodName           = "/manager.UiPlanService/SearchCaseNotInUIPlan"
	UiPlanService_AddCaseToUIPlan_FullMethodName                 = "/manager.UiPlanService/AddCaseToUIPlan"
	UiPlanService_RemoveCaseFromUIPlan_FullMethodName            = "/manager.UiPlanService/RemoveCaseFromUIPlan"
	UiPlanService_SearchLikeUiPlan_FullMethodName                = "/manager.UiPlanService/SearchLikeUiPlan"
)

// UiPlanServiceClient is the client API for UiPlanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// UiPlanService UI计划服务
type UiPlanServiceClient interface {
	//CreateUiPlan 创建UI计划
	CreateUiPlan(ctx context.Context, in *CreateUiPlanReq, opts ...grpc.CallOption) (*CreateUiPlanResp, error)
	//RemoveUiPlan 删除UI计划
	RemoveUiPlan(ctx context.Context, in *RemoveUiPlanReq, opts ...grpc.CallOption) (*RemoveUiPlanResp, error)
	//ModifyUiPlan 编辑UI计划
	ModifyUiPlan(ctx context.Context, in *ModifyUiPlanReq, opts ...grpc.CallOption) (*ModifyUiPlanResp, error)
	//SearchUiPlan 搜索UI计划
	SearchUiPlan(ctx context.Context, in *SearchUiPlanReq, opts ...grpc.CallOption) (*SearchUiPlanResp, error)
	//ViewUiPlan 查看UI计划
	ViewUiPlan(ctx context.Context, in *ViewUiPlanReq, opts ...grpc.CallOption) (*ViewUiPlanResp, error)
	//SearchUiPlanByProjectIdConfigId 搜索关联指定Git配置的UI计划
	SearchUiPlanByProjectIdConfigId(ctx context.Context, in *SearchUiPlanByProjectIdConfigIdReq, opts ...grpc.CallOption) (*SearchUiPlanByProjectIdConfigIdResp, error)
	//GetCaseTreeOfUIPlan 获取UI计划的用例树
	GetCaseTreeOfUIPlan(ctx context.Context, in *GetCaseTreeOfUIPlanReq, opts ...grpc.CallOption) (*GetCaseTreeOfUIPlanResp, error)
	//GetCaseTreeOfNotAddedToUIPlan 获取不在指定的UI计划中的用例树
	GetCaseTreeOfNotAddedToUIPlan(ctx context.Context, in *GetCaseTreeOfNotAddedToUIPlanReq, opts ...grpc.CallOption) (*GetCaseTreeOfNotAddedToUIPlanResp, error)
	//SearchCaseInUIPlan 搜索UI计划中的用例
	SearchCaseInUIPlan(ctx context.Context, in *SearchCaseInUIPlanReq, opts ...grpc.CallOption) (*SearchCaseInUIPlanResp, error)
	//ListDisableCaseInUIPlan 搜索UI计划中的失效用例
	ListDisableCaseInUIPlan(ctx context.Context, in *ListDisableCaseInUIPlanReq, opts ...grpc.CallOption) (*ListDisableCaseInUIPlanResp, error)
	//SearchCaseNotInUIPlan 搜索不在指定的UI计划中的用例
	SearchCaseNotInUIPlan(ctx context.Context, in *SearchCaseNotInUIPlanReq, opts ...grpc.CallOption) (*SearchCaseNotInUIPlanResp, error)
	//AddCaseToUIPlan 添加用例到UI计划中
	AddCaseToUIPlan(ctx context.Context, in *AddCaseToUIPlanReq, opts ...grpc.CallOption) (*AddCaseToUIPlanResp, error)
	//RemoveCaseFromUIPlan 移除UI计划中的用例
	RemoveCaseFromUIPlan(ctx context.Context, in *RemoveCaseFromUIPlanReq, opts ...grpc.CallOption) (*RemoveCaseFromUIPlanResp, error)
	//SearchLikeUiPlan 搜索收藏UI计划
	SearchLikeUiPlan(ctx context.Context, in *SearchUiPlanReq, opts ...grpc.CallOption) (*SearchUiPlanResp, error)
}

type uiPlanServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUiPlanServiceClient(cc grpc.ClientConnInterface) UiPlanServiceClient {
	return &uiPlanServiceClient{cc}
}

func (c *uiPlanServiceClient) CreateUiPlan(ctx context.Context, in *CreateUiPlanReq, opts ...grpc.CallOption) (*CreateUiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUiPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_CreateUiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) RemoveUiPlan(ctx context.Context, in *RemoveUiPlanReq, opts ...grpc.CallOption) (*RemoveUiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveUiPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_RemoveUiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) ModifyUiPlan(ctx context.Context, in *ModifyUiPlanReq, opts ...grpc.CallOption) (*ModifyUiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyUiPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_ModifyUiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) SearchUiPlan(ctx context.Context, in *SearchUiPlanReq, opts ...grpc.CallOption) (*SearchUiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUiPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_SearchUiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) ViewUiPlan(ctx context.Context, in *ViewUiPlanReq, opts ...grpc.CallOption) (*ViewUiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewUiPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_ViewUiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) SearchUiPlanByProjectIdConfigId(ctx context.Context, in *SearchUiPlanByProjectIdConfigIdReq, opts ...grpc.CallOption) (*SearchUiPlanByProjectIdConfigIdResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUiPlanByProjectIdConfigIdResp)
	err := c.cc.Invoke(ctx, UiPlanService_SearchUiPlanByProjectIdConfigId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) GetCaseTreeOfUIPlan(ctx context.Context, in *GetCaseTreeOfUIPlanReq, opts ...grpc.CallOption) (*GetCaseTreeOfUIPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCaseTreeOfUIPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_GetCaseTreeOfUIPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) GetCaseTreeOfNotAddedToUIPlan(ctx context.Context, in *GetCaseTreeOfNotAddedToUIPlanReq, opts ...grpc.CallOption) (*GetCaseTreeOfNotAddedToUIPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCaseTreeOfNotAddedToUIPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_GetCaseTreeOfNotAddedToUIPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) SearchCaseInUIPlan(ctx context.Context, in *SearchCaseInUIPlanReq, opts ...grpc.CallOption) (*SearchCaseInUIPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCaseInUIPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_SearchCaseInUIPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) ListDisableCaseInUIPlan(ctx context.Context, in *ListDisableCaseInUIPlanReq, opts ...grpc.CallOption) (*ListDisableCaseInUIPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDisableCaseInUIPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_ListDisableCaseInUIPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) SearchCaseNotInUIPlan(ctx context.Context, in *SearchCaseNotInUIPlanReq, opts ...grpc.CallOption) (*SearchCaseNotInUIPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCaseNotInUIPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_SearchCaseNotInUIPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) AddCaseToUIPlan(ctx context.Context, in *AddCaseToUIPlanReq, opts ...grpc.CallOption) (*AddCaseToUIPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddCaseToUIPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_AddCaseToUIPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) RemoveCaseFromUIPlan(ctx context.Context, in *RemoveCaseFromUIPlanReq, opts ...grpc.CallOption) (*RemoveCaseFromUIPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveCaseFromUIPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_RemoveCaseFromUIPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uiPlanServiceClient) SearchLikeUiPlan(ctx context.Context, in *SearchUiPlanReq, opts ...grpc.CallOption) (*SearchUiPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUiPlanResp)
	err := c.cc.Invoke(ctx, UiPlanService_SearchLikeUiPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UiPlanServiceServer is the server API for UiPlanService service.
// All implementations must embed UnimplementedUiPlanServiceServer
// for forward compatibility.
//
// UiPlanService UI计划服务
type UiPlanServiceServer interface {
	//CreateUiPlan 创建UI计划
	CreateUiPlan(context.Context, *CreateUiPlanReq) (*CreateUiPlanResp, error)
	//RemoveUiPlan 删除UI计划
	RemoveUiPlan(context.Context, *RemoveUiPlanReq) (*RemoveUiPlanResp, error)
	//ModifyUiPlan 编辑UI计划
	ModifyUiPlan(context.Context, *ModifyUiPlanReq) (*ModifyUiPlanResp, error)
	//SearchUiPlan 搜索UI计划
	SearchUiPlan(context.Context, *SearchUiPlanReq) (*SearchUiPlanResp, error)
	//ViewUiPlan 查看UI计划
	ViewUiPlan(context.Context, *ViewUiPlanReq) (*ViewUiPlanResp, error)
	//SearchUiPlanByProjectIdConfigId 搜索关联指定Git配置的UI计划
	SearchUiPlanByProjectIdConfigId(context.Context, *SearchUiPlanByProjectIdConfigIdReq) (*SearchUiPlanByProjectIdConfigIdResp, error)
	//GetCaseTreeOfUIPlan 获取UI计划的用例树
	GetCaseTreeOfUIPlan(context.Context, *GetCaseTreeOfUIPlanReq) (*GetCaseTreeOfUIPlanResp, error)
	//GetCaseTreeOfNotAddedToUIPlan 获取不在指定的UI计划中的用例树
	GetCaseTreeOfNotAddedToUIPlan(context.Context, *GetCaseTreeOfNotAddedToUIPlanReq) (*GetCaseTreeOfNotAddedToUIPlanResp, error)
	//SearchCaseInUIPlan 搜索UI计划中的用例
	SearchCaseInUIPlan(context.Context, *SearchCaseInUIPlanReq) (*SearchCaseInUIPlanResp, error)
	//ListDisableCaseInUIPlan 搜索UI计划中的失效用例
	ListDisableCaseInUIPlan(context.Context, *ListDisableCaseInUIPlanReq) (*ListDisableCaseInUIPlanResp, error)
	//SearchCaseNotInUIPlan 搜索不在指定的UI计划中的用例
	SearchCaseNotInUIPlan(context.Context, *SearchCaseNotInUIPlanReq) (*SearchCaseNotInUIPlanResp, error)
	//AddCaseToUIPlan 添加用例到UI计划中
	AddCaseToUIPlan(context.Context, *AddCaseToUIPlanReq) (*AddCaseToUIPlanResp, error)
	//RemoveCaseFromUIPlan 移除UI计划中的用例
	RemoveCaseFromUIPlan(context.Context, *RemoveCaseFromUIPlanReq) (*RemoveCaseFromUIPlanResp, error)
	//SearchLikeUiPlan 搜索收藏UI计划
	SearchLikeUiPlan(context.Context, *SearchUiPlanReq) (*SearchUiPlanResp, error)
	mustEmbedUnimplementedUiPlanServiceServer()
}

// UnimplementedUiPlanServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUiPlanServiceServer struct{}

func (UnimplementedUiPlanServiceServer) CreateUiPlan(context.Context, *CreateUiPlanReq) (*CreateUiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUiPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) RemoveUiPlan(context.Context, *RemoveUiPlanReq) (*RemoveUiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveUiPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) ModifyUiPlan(context.Context, *ModifyUiPlanReq) (*ModifyUiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyUiPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) SearchUiPlan(context.Context, *SearchUiPlanReq) (*SearchUiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUiPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) ViewUiPlan(context.Context, *ViewUiPlanReq) (*ViewUiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewUiPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) SearchUiPlanByProjectIdConfigId(context.Context, *SearchUiPlanByProjectIdConfigIdReq) (*SearchUiPlanByProjectIdConfigIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUiPlanByProjectIdConfigId not implemented")
}
func (UnimplementedUiPlanServiceServer) GetCaseTreeOfUIPlan(context.Context, *GetCaseTreeOfUIPlanReq) (*GetCaseTreeOfUIPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCaseTreeOfUIPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) GetCaseTreeOfNotAddedToUIPlan(context.Context, *GetCaseTreeOfNotAddedToUIPlanReq) (*GetCaseTreeOfNotAddedToUIPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCaseTreeOfNotAddedToUIPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) SearchCaseInUIPlan(context.Context, *SearchCaseInUIPlanReq) (*SearchCaseInUIPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCaseInUIPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) ListDisableCaseInUIPlan(context.Context, *ListDisableCaseInUIPlanReq) (*ListDisableCaseInUIPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDisableCaseInUIPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) SearchCaseNotInUIPlan(context.Context, *SearchCaseNotInUIPlanReq) (*SearchCaseNotInUIPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCaseNotInUIPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) AddCaseToUIPlan(context.Context, *AddCaseToUIPlanReq) (*AddCaseToUIPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCaseToUIPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) RemoveCaseFromUIPlan(context.Context, *RemoveCaseFromUIPlanReq) (*RemoveCaseFromUIPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveCaseFromUIPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) SearchLikeUiPlan(context.Context, *SearchUiPlanReq) (*SearchUiPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchLikeUiPlan not implemented")
}
func (UnimplementedUiPlanServiceServer) mustEmbedUnimplementedUiPlanServiceServer() {}
func (UnimplementedUiPlanServiceServer) testEmbeddedByValue()                       {}

// UnsafeUiPlanServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UiPlanServiceServer will
// result in compilation errors.
type UnsafeUiPlanServiceServer interface {
	mustEmbedUnimplementedUiPlanServiceServer()
}

func RegisterUiPlanServiceServer(s grpc.ServiceRegistrar, srv UiPlanServiceServer) {
	// If the following call pancis, it indicates UnimplementedUiPlanServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UiPlanService_ServiceDesc, srv)
}

func _UiPlanService_CreateUiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).CreateUiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_CreateUiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).CreateUiPlan(ctx, req.(*CreateUiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_RemoveUiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveUiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).RemoveUiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_RemoveUiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).RemoveUiPlan(ctx, req.(*RemoveUiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_ModifyUiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyUiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).ModifyUiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_ModifyUiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).ModifyUiPlan(ctx, req.(*ModifyUiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_SearchUiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).SearchUiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_SearchUiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).SearchUiPlan(ctx, req.(*SearchUiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_ViewUiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewUiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).ViewUiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_ViewUiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).ViewUiPlan(ctx, req.(*ViewUiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_SearchUiPlanByProjectIdConfigId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUiPlanByProjectIdConfigIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).SearchUiPlanByProjectIdConfigId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_SearchUiPlanByProjectIdConfigId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).SearchUiPlanByProjectIdConfigId(ctx, req.(*SearchUiPlanByProjectIdConfigIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_GetCaseTreeOfUIPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCaseTreeOfUIPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).GetCaseTreeOfUIPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_GetCaseTreeOfUIPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).GetCaseTreeOfUIPlan(ctx, req.(*GetCaseTreeOfUIPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_GetCaseTreeOfNotAddedToUIPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCaseTreeOfNotAddedToUIPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).GetCaseTreeOfNotAddedToUIPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_GetCaseTreeOfNotAddedToUIPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).GetCaseTreeOfNotAddedToUIPlan(ctx, req.(*GetCaseTreeOfNotAddedToUIPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_SearchCaseInUIPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCaseInUIPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).SearchCaseInUIPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_SearchCaseInUIPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).SearchCaseInUIPlan(ctx, req.(*SearchCaseInUIPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_ListDisableCaseInUIPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDisableCaseInUIPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).ListDisableCaseInUIPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_ListDisableCaseInUIPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).ListDisableCaseInUIPlan(ctx, req.(*ListDisableCaseInUIPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_SearchCaseNotInUIPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCaseNotInUIPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).SearchCaseNotInUIPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_SearchCaseNotInUIPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).SearchCaseNotInUIPlan(ctx, req.(*SearchCaseNotInUIPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_AddCaseToUIPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCaseToUIPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).AddCaseToUIPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_AddCaseToUIPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).AddCaseToUIPlan(ctx, req.(*AddCaseToUIPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_RemoveCaseFromUIPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveCaseFromUIPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).RemoveCaseFromUIPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_RemoveCaseFromUIPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).RemoveCaseFromUIPlan(ctx, req.(*RemoveCaseFromUIPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UiPlanService_SearchLikeUiPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUiPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UiPlanServiceServer).SearchLikeUiPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UiPlanService_SearchLikeUiPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UiPlanServiceServer).SearchLikeUiPlan(ctx, req.(*SearchUiPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UiPlanService_ServiceDesc is the grpc.ServiceDesc for UiPlanService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UiPlanService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.UiPlanService",
	HandlerType: (*UiPlanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateUiPlan",
			Handler:    _UiPlanService_CreateUiPlan_Handler,
		},
		{
			MethodName: "RemoveUiPlan",
			Handler:    _UiPlanService_RemoveUiPlan_Handler,
		},
		{
			MethodName: "ModifyUiPlan",
			Handler:    _UiPlanService_ModifyUiPlan_Handler,
		},
		{
			MethodName: "SearchUiPlan",
			Handler:    _UiPlanService_SearchUiPlan_Handler,
		},
		{
			MethodName: "ViewUiPlan",
			Handler:    _UiPlanService_ViewUiPlan_Handler,
		},
		{
			MethodName: "SearchUiPlanByProjectIdConfigId",
			Handler:    _UiPlanService_SearchUiPlanByProjectIdConfigId_Handler,
		},
		{
			MethodName: "GetCaseTreeOfUIPlan",
			Handler:    _UiPlanService_GetCaseTreeOfUIPlan_Handler,
		},
		{
			MethodName: "GetCaseTreeOfNotAddedToUIPlan",
			Handler:    _UiPlanService_GetCaseTreeOfNotAddedToUIPlan_Handler,
		},
		{
			MethodName: "SearchCaseInUIPlan",
			Handler:    _UiPlanService_SearchCaseInUIPlan_Handler,
		},
		{
			MethodName: "ListDisableCaseInUIPlan",
			Handler:    _UiPlanService_ListDisableCaseInUIPlan_Handler,
		},
		{
			MethodName: "SearchCaseNotInUIPlan",
			Handler:    _UiPlanService_SearchCaseNotInUIPlan_Handler,
		},
		{
			MethodName: "AddCaseToUIPlan",
			Handler:    _UiPlanService_AddCaseToUIPlan_Handler,
		},
		{
			MethodName: "RemoveCaseFromUIPlan",
			Handler:    _UiPlanService_RemoveCaseFromUIPlan_Handler,
		},
		{
			MethodName: "SearchLikeUiPlan",
			Handler:    _UiPlanService_SearchLikeUiPlan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PlanCommonService_HandleLikePlan_FullMethodName = "/manager.PlanCommonService/HandleLikePlan"
	PlanCommonService_CheckLikePlan_FullMethodName  = "/manager.PlanCommonService/CheckLikePlan"
)

// PlanCommonServiceClient is the client API for PlanCommonService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PlanCommonService 通用计划服务
type PlanCommonServiceClient interface {
	//GetApiExecutionData 处理计划收藏
	HandleLikePlan(ctx context.Context, in *HandleLikePlanReq, opts ...grpc.CallOption) (*HandleLikePlanResp, error)
	//CheckLikePlan 检查计划收藏情况
	CheckLikePlan(ctx context.Context, in *CheckLikePlanReq, opts ...grpc.CallOption) (*CheckLikePlanResp, error)
}

type planCommonServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlanCommonServiceClient(cc grpc.ClientConnInterface) PlanCommonServiceClient {
	return &planCommonServiceClient{cc}
}

func (c *planCommonServiceClient) HandleLikePlan(ctx context.Context, in *HandleLikePlanReq, opts ...grpc.CallOption) (*HandleLikePlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleLikePlanResp)
	err := c.cc.Invoke(ctx, PlanCommonService_HandleLikePlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *planCommonServiceClient) CheckLikePlan(ctx context.Context, in *CheckLikePlanReq, opts ...grpc.CallOption) (*CheckLikePlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckLikePlanResp)
	err := c.cc.Invoke(ctx, PlanCommonService_CheckLikePlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlanCommonServiceServer is the server API for PlanCommonService service.
// All implementations must embed UnimplementedPlanCommonServiceServer
// for forward compatibility.
//
// PlanCommonService 通用计划服务
type PlanCommonServiceServer interface {
	//GetApiExecutionData 处理计划收藏
	HandleLikePlan(context.Context, *HandleLikePlanReq) (*HandleLikePlanResp, error)
	//CheckLikePlan 检查计划收藏情况
	CheckLikePlan(context.Context, *CheckLikePlanReq) (*CheckLikePlanResp, error)
	mustEmbedUnimplementedPlanCommonServiceServer()
}

// UnimplementedPlanCommonServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPlanCommonServiceServer struct{}

func (UnimplementedPlanCommonServiceServer) HandleLikePlan(context.Context, *HandleLikePlanReq) (*HandleLikePlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleLikePlan not implemented")
}
func (UnimplementedPlanCommonServiceServer) CheckLikePlan(context.Context, *CheckLikePlanReq) (*CheckLikePlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLikePlan not implemented")
}
func (UnimplementedPlanCommonServiceServer) mustEmbedUnimplementedPlanCommonServiceServer() {}
func (UnimplementedPlanCommonServiceServer) testEmbeddedByValue()                           {}

// UnsafePlanCommonServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlanCommonServiceServer will
// result in compilation errors.
type UnsafePlanCommonServiceServer interface {
	mustEmbedUnimplementedPlanCommonServiceServer()
}

func RegisterPlanCommonServiceServer(s grpc.ServiceRegistrar, srv PlanCommonServiceServer) {
	// If the following call pancis, it indicates UnimplementedPlanCommonServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PlanCommonService_ServiceDesc, srv)
}

func _PlanCommonService_HandleLikePlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleLikePlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlanCommonServiceServer).HandleLikePlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlanCommonService_HandleLikePlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlanCommonServiceServer).HandleLikePlan(ctx, req.(*HandleLikePlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlanCommonService_CheckLikePlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLikePlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlanCommonServiceServer).CheckLikePlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlanCommonService_CheckLikePlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlanCommonServiceServer).CheckLikePlan(ctx, req.(*CheckLikePlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PlanCommonService_ServiceDesc is the grpc.ServiceDesc for PlanCommonService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlanCommonService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PlanCommonService",
	HandlerType: (*PlanCommonServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleLikePlan",
			Handler:    _PlanCommonService_HandleLikePlan_Handler,
		},
		{
			MethodName: "CheckLikePlan",
			Handler:    _PlanCommonService_CheckLikePlan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	ReviewService_CreateReviewRecord_FullMethodName  = "/manager.ReviewService/CreateReviewRecord"
	ReviewService_ModifyReviewRecord_FullMethodName  = "/manager.ReviewService/ModifyReviewRecord"
	ReviewService_RevokeReviewRecord_FullMethodName  = "/manager.ReviewService/RevokeReviewRecord"
	ReviewService_ApproveReviewRecord_FullMethodName = "/manager.ReviewService/ApproveReviewRecord"
	ReviewService_SearchReviewRecord_FullMethodName  = "/manager.ReviewService/SearchReviewRecord"
)

// ReviewServiceClient is the client API for ReviewService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ReviewService 审核服务
type ReviewServiceClient interface {
	//CreateReviewRecord 申请审核
	CreateReviewRecord(ctx context.Context, in *CreateReviewRecordReq, opts ...grpc.CallOption) (*CreateReviewRecordResp, error)
	//ModifyReviewRecord 编辑审核
	ModifyReviewRecord(ctx context.Context, in *ModifyReviewRecordReq, opts ...grpc.CallOption) (*ModifyReviewRecordResp, error)
	//RevokeReviewRecord 撤回审核
	RevokeReviewRecord(ctx context.Context, in *RevokeReviewRecordReq, opts ...grpc.CallOption) (*RevokeReviewRecordResp, error)
	//ApproveReviewRecord 审批审核
	ApproveReviewRecord(ctx context.Context, in *ApproveReviewRecordReq, opts ...grpc.CallOption) (*ApproveReviewRecordResp, error)
	//SearchReviewRecord 搜索审核记录（包括申请记录）
	SearchReviewRecord(ctx context.Context, in *SearchReviewRecordReq, opts ...grpc.CallOption) (*SearchReviewRecordResp, error)
}

type reviewServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReviewServiceClient(cc grpc.ClientConnInterface) ReviewServiceClient {
	return &reviewServiceClient{cc}
}

func (c *reviewServiceClient) CreateReviewRecord(ctx context.Context, in *CreateReviewRecordReq, opts ...grpc.CallOption) (*CreateReviewRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateReviewRecordResp)
	err := c.cc.Invoke(ctx, ReviewService_CreateReviewRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewServiceClient) ModifyReviewRecord(ctx context.Context, in *ModifyReviewRecordReq, opts ...grpc.CallOption) (*ModifyReviewRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyReviewRecordResp)
	err := c.cc.Invoke(ctx, ReviewService_ModifyReviewRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewServiceClient) RevokeReviewRecord(ctx context.Context, in *RevokeReviewRecordReq, opts ...grpc.CallOption) (*RevokeReviewRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RevokeReviewRecordResp)
	err := c.cc.Invoke(ctx, ReviewService_RevokeReviewRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewServiceClient) ApproveReviewRecord(ctx context.Context, in *ApproveReviewRecordReq, opts ...grpc.CallOption) (*ApproveReviewRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApproveReviewRecordResp)
	err := c.cc.Invoke(ctx, ReviewService_ApproveReviewRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewServiceClient) SearchReviewRecord(ctx context.Context, in *SearchReviewRecordReq, opts ...grpc.CallOption) (*SearchReviewRecordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchReviewRecordResp)
	err := c.cc.Invoke(ctx, ReviewService_SearchReviewRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReviewServiceServer is the server API for ReviewService service.
// All implementations must embed UnimplementedReviewServiceServer
// for forward compatibility.
//
// ReviewService 审核服务
type ReviewServiceServer interface {
	//CreateReviewRecord 申请审核
	CreateReviewRecord(context.Context, *CreateReviewRecordReq) (*CreateReviewRecordResp, error)
	//ModifyReviewRecord 编辑审核
	ModifyReviewRecord(context.Context, *ModifyReviewRecordReq) (*ModifyReviewRecordResp, error)
	//RevokeReviewRecord 撤回审核
	RevokeReviewRecord(context.Context, *RevokeReviewRecordReq) (*RevokeReviewRecordResp, error)
	//ApproveReviewRecord 审批审核
	ApproveReviewRecord(context.Context, *ApproveReviewRecordReq) (*ApproveReviewRecordResp, error)
	//SearchReviewRecord 搜索审核记录（包括申请记录）
	SearchReviewRecord(context.Context, *SearchReviewRecordReq) (*SearchReviewRecordResp, error)
	mustEmbedUnimplementedReviewServiceServer()
}

// UnimplementedReviewServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedReviewServiceServer struct{}

func (UnimplementedReviewServiceServer) CreateReviewRecord(context.Context, *CreateReviewRecordReq) (*CreateReviewRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateReviewRecord not implemented")
}
func (UnimplementedReviewServiceServer) ModifyReviewRecord(context.Context, *ModifyReviewRecordReq) (*ModifyReviewRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyReviewRecord not implemented")
}
func (UnimplementedReviewServiceServer) RevokeReviewRecord(context.Context, *RevokeReviewRecordReq) (*RevokeReviewRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeReviewRecord not implemented")
}
func (UnimplementedReviewServiceServer) ApproveReviewRecord(context.Context, *ApproveReviewRecordReq) (*ApproveReviewRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApproveReviewRecord not implemented")
}
func (UnimplementedReviewServiceServer) SearchReviewRecord(context.Context, *SearchReviewRecordReq) (*SearchReviewRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchReviewRecord not implemented")
}
func (UnimplementedReviewServiceServer) mustEmbedUnimplementedReviewServiceServer() {}
func (UnimplementedReviewServiceServer) testEmbeddedByValue()                       {}

// UnsafeReviewServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReviewServiceServer will
// result in compilation errors.
type UnsafeReviewServiceServer interface {
	mustEmbedUnimplementedReviewServiceServer()
}

func RegisterReviewServiceServer(s grpc.ServiceRegistrar, srv ReviewServiceServer) {
	// If the following call pancis, it indicates UnimplementedReviewServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ReviewService_ServiceDesc, srv)
}

func _ReviewService_CreateReviewRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReviewRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewServiceServer).CreateReviewRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReviewService_CreateReviewRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewServiceServer).CreateReviewRecord(ctx, req.(*CreateReviewRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReviewService_ModifyReviewRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyReviewRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewServiceServer).ModifyReviewRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReviewService_ModifyReviewRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewServiceServer).ModifyReviewRecord(ctx, req.(*ModifyReviewRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReviewService_RevokeReviewRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeReviewRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewServiceServer).RevokeReviewRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReviewService_RevokeReviewRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewServiceServer).RevokeReviewRecord(ctx, req.(*RevokeReviewRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReviewService_ApproveReviewRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApproveReviewRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewServiceServer).ApproveReviewRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReviewService_ApproveReviewRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewServiceServer).ApproveReviewRecord(ctx, req.(*ApproveReviewRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReviewService_SearchReviewRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchReviewRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewServiceServer).SearchReviewRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReviewService_SearchReviewRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewServiceServer).SearchReviewRecord(ctx, req.(*SearchReviewRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ReviewService_ServiceDesc is the grpc.ServiceDesc for ReviewService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReviewService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ReviewService",
	HandlerType: (*ReviewServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateReviewRecord",
			Handler:    _ReviewService_CreateReviewRecord_Handler,
		},
		{
			MethodName: "ModifyReviewRecord",
			Handler:    _ReviewService_ModifyReviewRecord_Handler,
		},
		{
			MethodName: "RevokeReviewRecord",
			Handler:    _ReviewService_RevokeReviewRecord_Handler,
		},
		{
			MethodName: "ApproveReviewRecord",
			Handler:    _ReviewService_ApproveReviewRecord_Handler,
		},
		{
			MethodName: "SearchReviewRecord",
			Handler:    _ReviewService_SearchReviewRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	CaseCommonService_SearchNotReleasedCase_FullMethodName    = "/manager.CaseCommonService/SearchNotReleasedCase"
	CaseCommonService_SearchFailLogCase_FullMethodName        = "/manager.CaseCommonService/SearchFailLogCase"
	CaseCommonService_DeleteFailLogCase_FullMethodName        = "/manager.CaseCommonService/DeleteFailLogCase"
	CaseCommonService_BatchDeleteFailLogCase_FullMethodName   = "/manager.CaseCommonService/BatchDeleteFailLogCase"
	CaseCommonService_CreateOrModifyFailedCase_FullMethodName = "/manager.CaseCommonService/CreateOrModifyFailedCase"
)

// CaseCommonServiceClient is the client API for CaseCommonService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CaseCommonService 通用用例服务
type CaseCommonServiceClient interface {
	//SearchNotReleasedCase 搜索未上线用例
	SearchNotReleasedCase(ctx context.Context, in *SearchCaseReq, opts ...grpc.CallOption) (*SearchCaseResp, error)
	//SearchFailLogCase 搜索用例失败记录
	SearchFailLogCase(ctx context.Context, in *SearchCaseFailLogReq, opts ...grpc.CallOption) (*SearchCaseFailLogResp, error)
	//DeleteFailLogCase 删除（忽略）用例失败记录
	DeleteFailLogCase(ctx context.Context, in *DeleteFailLogCaseReq, opts ...grpc.CallOption) (*DeleteFailLogCaseResp, error)
	//BatchDeleteFailLogCase 批量删除（忽略）用例失败记录
	BatchDeleteFailLogCase(ctx context.Context, in *BatchDeleteCaseFailLogReq, opts ...grpc.CallOption) (*BatchDeleteCaseFailLogResp, error)
	//CreateOrModifyFailedCase 创建或修改失败用例记录
	CreateOrModifyFailedCase(ctx context.Context, in *CreateOrModifyFailedCaseReq, opts ...grpc.CallOption) (*CreateOrModifyFailedCaseResp, error)
}

type caseCommonServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCaseCommonServiceClient(cc grpc.ClientConnInterface) CaseCommonServiceClient {
	return &caseCommonServiceClient{cc}
}

func (c *caseCommonServiceClient) SearchNotReleasedCase(ctx context.Context, in *SearchCaseReq, opts ...grpc.CallOption) (*SearchCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCaseResp)
	err := c.cc.Invoke(ctx, CaseCommonService_SearchNotReleasedCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *caseCommonServiceClient) SearchFailLogCase(ctx context.Context, in *SearchCaseFailLogReq, opts ...grpc.CallOption) (*SearchCaseFailLogResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCaseFailLogResp)
	err := c.cc.Invoke(ctx, CaseCommonService_SearchFailLogCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *caseCommonServiceClient) DeleteFailLogCase(ctx context.Context, in *DeleteFailLogCaseReq, opts ...grpc.CallOption) (*DeleteFailLogCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteFailLogCaseResp)
	err := c.cc.Invoke(ctx, CaseCommonService_DeleteFailLogCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *caseCommonServiceClient) BatchDeleteFailLogCase(ctx context.Context, in *BatchDeleteCaseFailLogReq, opts ...grpc.CallOption) (*BatchDeleteCaseFailLogResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchDeleteCaseFailLogResp)
	err := c.cc.Invoke(ctx, CaseCommonService_BatchDeleteFailLogCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *caseCommonServiceClient) CreateOrModifyFailedCase(ctx context.Context, in *CreateOrModifyFailedCaseReq, opts ...grpc.CallOption) (*CreateOrModifyFailedCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrModifyFailedCaseResp)
	err := c.cc.Invoke(ctx, CaseCommonService_CreateOrModifyFailedCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CaseCommonServiceServer is the server API for CaseCommonService service.
// All implementations must embed UnimplementedCaseCommonServiceServer
// for forward compatibility.
//
// CaseCommonService 通用用例服务
type CaseCommonServiceServer interface {
	//SearchNotReleasedCase 搜索未上线用例
	SearchNotReleasedCase(context.Context, *SearchCaseReq) (*SearchCaseResp, error)
	//SearchFailLogCase 搜索用例失败记录
	SearchFailLogCase(context.Context, *SearchCaseFailLogReq) (*SearchCaseFailLogResp, error)
	//DeleteFailLogCase 删除（忽略）用例失败记录
	DeleteFailLogCase(context.Context, *DeleteFailLogCaseReq) (*DeleteFailLogCaseResp, error)
	//BatchDeleteFailLogCase 批量删除（忽略）用例失败记录
	BatchDeleteFailLogCase(context.Context, *BatchDeleteCaseFailLogReq) (*BatchDeleteCaseFailLogResp, error)
	//CreateOrModifyFailedCase 创建或修改失败用例记录
	CreateOrModifyFailedCase(context.Context, *CreateOrModifyFailedCaseReq) (*CreateOrModifyFailedCaseResp, error)
	mustEmbedUnimplementedCaseCommonServiceServer()
}

// UnimplementedCaseCommonServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCaseCommonServiceServer struct{}

func (UnimplementedCaseCommonServiceServer) SearchNotReleasedCase(context.Context, *SearchCaseReq) (*SearchCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchNotReleasedCase not implemented")
}
func (UnimplementedCaseCommonServiceServer) SearchFailLogCase(context.Context, *SearchCaseFailLogReq) (*SearchCaseFailLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchFailLogCase not implemented")
}
func (UnimplementedCaseCommonServiceServer) DeleteFailLogCase(context.Context, *DeleteFailLogCaseReq) (*DeleteFailLogCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFailLogCase not implemented")
}
func (UnimplementedCaseCommonServiceServer) BatchDeleteFailLogCase(context.Context, *BatchDeleteCaseFailLogReq) (*BatchDeleteCaseFailLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteFailLogCase not implemented")
}
func (UnimplementedCaseCommonServiceServer) CreateOrModifyFailedCase(context.Context, *CreateOrModifyFailedCaseReq) (*CreateOrModifyFailedCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrModifyFailedCase not implemented")
}
func (UnimplementedCaseCommonServiceServer) mustEmbedUnimplementedCaseCommonServiceServer() {}
func (UnimplementedCaseCommonServiceServer) testEmbeddedByValue()                           {}

// UnsafeCaseCommonServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CaseCommonServiceServer will
// result in compilation errors.
type UnsafeCaseCommonServiceServer interface {
	mustEmbedUnimplementedCaseCommonServiceServer()
}

func RegisterCaseCommonServiceServer(s grpc.ServiceRegistrar, srv CaseCommonServiceServer) {
	// If the following call pancis, it indicates UnimplementedCaseCommonServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CaseCommonService_ServiceDesc, srv)
}

func _CaseCommonService_SearchNotReleasedCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CaseCommonServiceServer).SearchNotReleasedCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CaseCommonService_SearchNotReleasedCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CaseCommonServiceServer).SearchNotReleasedCase(ctx, req.(*SearchCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CaseCommonService_SearchFailLogCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCaseFailLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CaseCommonServiceServer).SearchFailLogCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CaseCommonService_SearchFailLogCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CaseCommonServiceServer).SearchFailLogCase(ctx, req.(*SearchCaseFailLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CaseCommonService_DeleteFailLogCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFailLogCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CaseCommonServiceServer).DeleteFailLogCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CaseCommonService_DeleteFailLogCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CaseCommonServiceServer).DeleteFailLogCase(ctx, req.(*DeleteFailLogCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CaseCommonService_BatchDeleteFailLogCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteCaseFailLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CaseCommonServiceServer).BatchDeleteFailLogCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CaseCommonService_BatchDeleteFailLogCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CaseCommonServiceServer).BatchDeleteFailLogCase(ctx, req.(*BatchDeleteCaseFailLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CaseCommonService_CreateOrModifyFailedCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrModifyFailedCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CaseCommonServiceServer).CreateOrModifyFailedCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CaseCommonService_CreateOrModifyFailedCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CaseCommonServiceServer).CreateOrModifyFailedCase(ctx, req.(*CreateOrModifyFailedCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CaseCommonService_ServiceDesc is the grpc.ServiceDesc for CaseCommonService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CaseCommonService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.CaseCommonService",
	HandlerType: (*CaseCommonServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchNotReleasedCase",
			Handler:    _CaseCommonService_SearchNotReleasedCase_Handler,
		},
		{
			MethodName: "SearchFailLogCase",
			Handler:    _CaseCommonService_SearchFailLogCase_Handler,
		},
		{
			MethodName: "DeleteFailLogCase",
			Handler:    _CaseCommonService_DeleteFailLogCase_Handler,
		},
		{
			MethodName: "BatchDeleteFailLogCase",
			Handler:    _CaseCommonService_BatchDeleteFailLogCase_Handler,
		},
		{
			MethodName: "CreateOrModifyFailedCase",
			Handler:    _CaseCommonService_CreateOrModifyFailedCase_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	ProtobufConfigurationService_CreateProtobufConfiguration_FullMethodName = "/manager.ProtobufConfigurationService/CreateProtobufConfiguration"
	ProtobufConfigurationService_RemoveProtobufConfiguration_FullMethodName = "/manager.ProtobufConfigurationService/RemoveProtobufConfiguration"
	ProtobufConfigurationService_ModifyProtobufConfiguration_FullMethodName = "/manager.ProtobufConfigurationService/ModifyProtobufConfiguration"
	ProtobufConfigurationService_SearchProtobufConfiguration_FullMethodName = "/manager.ProtobufConfigurationService/SearchProtobufConfiguration"
	ProtobufConfigurationService_ViewProtobufConfiguration_FullMethodName   = "/manager.ProtobufConfigurationService/ViewProtobufConfiguration"
)

// ProtobufConfigurationServiceClient is the client API for ProtobufConfigurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ProtobufConfigurationService Protobuf配置服务
type ProtobufConfigurationServiceClient interface {
	//CreateProtobufConfiguration 创建Protobuf配置
	CreateProtobufConfiguration(ctx context.Context, in *CreateProtobufConfigurationReq, opts ...grpc.CallOption) (*CreateProtobufConfigurationResp, error)
	//RemoveProtobufConfiguration 删除Protobuf配置
	RemoveProtobufConfiguration(ctx context.Context, in *RemoveProtobufConfigurationReq, opts ...grpc.CallOption) (*RemoveProtobufConfigurationResp, error)
	//ModifyProtobufConfiguration 编辑Protobuf配置
	ModifyProtobufConfiguration(ctx context.Context, in *ModifyProtobufConfigurationReq, opts ...grpc.CallOption) (*ModifyProtobufConfigurationResp, error)
	//SearchProtobufConfiguration 搜索Protobuf配置
	SearchProtobufConfiguration(ctx context.Context, in *SearchProtobufConfigurationReq, opts ...grpc.CallOption) (*SearchProtobufConfigurationResp, error)
	//ViewProtobufConfiguration 查看Protobuf配置
	ViewProtobufConfiguration(ctx context.Context, in *ViewProtobufConfigurationReq, opts ...grpc.CallOption) (*ViewProtobufConfigurationResp, error)
}

type protobufConfigurationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewProtobufConfigurationServiceClient(cc grpc.ClientConnInterface) ProtobufConfigurationServiceClient {
	return &protobufConfigurationServiceClient{cc}
}

func (c *protobufConfigurationServiceClient) CreateProtobufConfiguration(ctx context.Context, in *CreateProtobufConfigurationReq, opts ...grpc.CallOption) (*CreateProtobufConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateProtobufConfigurationResp)
	err := c.cc.Invoke(ctx, ProtobufConfigurationService_CreateProtobufConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *protobufConfigurationServiceClient) RemoveProtobufConfiguration(ctx context.Context, in *RemoveProtobufConfigurationReq, opts ...grpc.CallOption) (*RemoveProtobufConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveProtobufConfigurationResp)
	err := c.cc.Invoke(ctx, ProtobufConfigurationService_RemoveProtobufConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *protobufConfigurationServiceClient) ModifyProtobufConfiguration(ctx context.Context, in *ModifyProtobufConfigurationReq, opts ...grpc.CallOption) (*ModifyProtobufConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyProtobufConfigurationResp)
	err := c.cc.Invoke(ctx, ProtobufConfigurationService_ModifyProtobufConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *protobufConfigurationServiceClient) SearchProtobufConfiguration(ctx context.Context, in *SearchProtobufConfigurationReq, opts ...grpc.CallOption) (*SearchProtobufConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchProtobufConfigurationResp)
	err := c.cc.Invoke(ctx, ProtobufConfigurationService_SearchProtobufConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *protobufConfigurationServiceClient) ViewProtobufConfiguration(ctx context.Context, in *ViewProtobufConfigurationReq, opts ...grpc.CallOption) (*ViewProtobufConfigurationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewProtobufConfigurationResp)
	err := c.cc.Invoke(ctx, ProtobufConfigurationService_ViewProtobufConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProtobufConfigurationServiceServer is the server API for ProtobufConfigurationService service.
// All implementations must embed UnimplementedProtobufConfigurationServiceServer
// for forward compatibility.
//
// ProtobufConfigurationService Protobuf配置服务
type ProtobufConfigurationServiceServer interface {
	//CreateProtobufConfiguration 创建Protobuf配置
	CreateProtobufConfiguration(context.Context, *CreateProtobufConfigurationReq) (*CreateProtobufConfigurationResp, error)
	//RemoveProtobufConfiguration 删除Protobuf配置
	RemoveProtobufConfiguration(context.Context, *RemoveProtobufConfigurationReq) (*RemoveProtobufConfigurationResp, error)
	//ModifyProtobufConfiguration 编辑Protobuf配置
	ModifyProtobufConfiguration(context.Context, *ModifyProtobufConfigurationReq) (*ModifyProtobufConfigurationResp, error)
	//SearchProtobufConfiguration 搜索Protobuf配置
	SearchProtobufConfiguration(context.Context, *SearchProtobufConfigurationReq) (*SearchProtobufConfigurationResp, error)
	//ViewProtobufConfiguration 查看Protobuf配置
	ViewProtobufConfiguration(context.Context, *ViewProtobufConfigurationReq) (*ViewProtobufConfigurationResp, error)
	mustEmbedUnimplementedProtobufConfigurationServiceServer()
}

// UnimplementedProtobufConfigurationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedProtobufConfigurationServiceServer struct{}

func (UnimplementedProtobufConfigurationServiceServer) CreateProtobufConfiguration(context.Context, *CreateProtobufConfigurationReq) (*CreateProtobufConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateProtobufConfiguration not implemented")
}
func (UnimplementedProtobufConfigurationServiceServer) RemoveProtobufConfiguration(context.Context, *RemoveProtobufConfigurationReq) (*RemoveProtobufConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveProtobufConfiguration not implemented")
}
func (UnimplementedProtobufConfigurationServiceServer) ModifyProtobufConfiguration(context.Context, *ModifyProtobufConfigurationReq) (*ModifyProtobufConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyProtobufConfiguration not implemented")
}
func (UnimplementedProtobufConfigurationServiceServer) SearchProtobufConfiguration(context.Context, *SearchProtobufConfigurationReq) (*SearchProtobufConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchProtobufConfiguration not implemented")
}
func (UnimplementedProtobufConfigurationServiceServer) ViewProtobufConfiguration(context.Context, *ViewProtobufConfigurationReq) (*ViewProtobufConfigurationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewProtobufConfiguration not implemented")
}
func (UnimplementedProtobufConfigurationServiceServer) mustEmbedUnimplementedProtobufConfigurationServiceServer() {
}
func (UnimplementedProtobufConfigurationServiceServer) testEmbeddedByValue() {}

// UnsafeProtobufConfigurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProtobufConfigurationServiceServer will
// result in compilation errors.
type UnsafeProtobufConfigurationServiceServer interface {
	mustEmbedUnimplementedProtobufConfigurationServiceServer()
}

func RegisterProtobufConfigurationServiceServer(s grpc.ServiceRegistrar, srv ProtobufConfigurationServiceServer) {
	// If the following call pancis, it indicates UnimplementedProtobufConfigurationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ProtobufConfigurationService_ServiceDesc, srv)
}

func _ProtobufConfigurationService_CreateProtobufConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateProtobufConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProtobufConfigurationServiceServer).CreateProtobufConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProtobufConfigurationService_CreateProtobufConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProtobufConfigurationServiceServer).CreateProtobufConfiguration(ctx, req.(*CreateProtobufConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProtobufConfigurationService_RemoveProtobufConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveProtobufConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProtobufConfigurationServiceServer).RemoveProtobufConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProtobufConfigurationService_RemoveProtobufConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProtobufConfigurationServiceServer).RemoveProtobufConfiguration(ctx, req.(*RemoveProtobufConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProtobufConfigurationService_ModifyProtobufConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyProtobufConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProtobufConfigurationServiceServer).ModifyProtobufConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProtobufConfigurationService_ModifyProtobufConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProtobufConfigurationServiceServer).ModifyProtobufConfiguration(ctx, req.(*ModifyProtobufConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProtobufConfigurationService_SearchProtobufConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchProtobufConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProtobufConfigurationServiceServer).SearchProtobufConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProtobufConfigurationService_SearchProtobufConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProtobufConfigurationServiceServer).SearchProtobufConfiguration(ctx, req.(*SearchProtobufConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProtobufConfigurationService_ViewProtobufConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewProtobufConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProtobufConfigurationServiceServer).ViewProtobufConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProtobufConfigurationService_ViewProtobufConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProtobufConfigurationServiceServer).ViewProtobufConfiguration(ctx, req.(*ViewProtobufConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ProtobufConfigurationService_ServiceDesc is the grpc.ServiceDesc for ProtobufConfigurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProtobufConfigurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ProtobufConfigurationService",
	HandlerType: (*ProtobufConfigurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateProtobufConfiguration",
			Handler:    _ProtobufConfigurationService_CreateProtobufConfiguration_Handler,
		},
		{
			MethodName: "RemoveProtobufConfiguration",
			Handler:    _ProtobufConfigurationService_RemoveProtobufConfiguration_Handler,
		},
		{
			MethodName: "ModifyProtobufConfiguration",
			Handler:    _ProtobufConfigurationService_ModifyProtobufConfiguration_Handler,
		},
		{
			MethodName: "SearchProtobufConfiguration",
			Handler:    _ProtobufConfigurationService_SearchProtobufConfiguration_Handler,
		},
		{
			MethodName: "ViewProtobufConfiguration",
			Handler:    _ProtobufConfigurationService_ViewProtobufConfiguration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PerfDataService_RemovePerfData_FullMethodName = "/manager.PerfDataService/RemovePerfData"
	PerfDataService_SearchPerfData_FullMethodName = "/manager.PerfDataService/SearchPerfData"
)

// PerfDataServiceClient is the client API for PerfDataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PerfDataService 压测数据服务
type PerfDataServiceClient interface {
	//RemovePerfData 删除压测数据
	RemovePerfData(ctx context.Context, in *RemovePerfDataReq, opts ...grpc.CallOption) (*RemovePerfDataResp, error)
	//SearchPerfData 搜索压测数据
	SearchPerfData(ctx context.Context, in *SearchPerfDataReq, opts ...grpc.CallOption) (*SearchPerfDataResp, error)
}

type perfDataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfDataServiceClient(cc grpc.ClientConnInterface) PerfDataServiceClient {
	return &perfDataServiceClient{cc}
}

func (c *perfDataServiceClient) RemovePerfData(ctx context.Context, in *RemovePerfDataReq, opts ...grpc.CallOption) (*RemovePerfDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePerfDataResp)
	err := c.cc.Invoke(ctx, PerfDataService_RemovePerfData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfDataServiceClient) SearchPerfData(ctx context.Context, in *SearchPerfDataReq, opts ...grpc.CallOption) (*SearchPerfDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfDataResp)
	err := c.cc.Invoke(ctx, PerfDataService_SearchPerfData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfDataServiceServer is the server API for PerfDataService service.
// All implementations must embed UnimplementedPerfDataServiceServer
// for forward compatibility.
//
// PerfDataService 压测数据服务
type PerfDataServiceServer interface {
	//RemovePerfData 删除压测数据
	RemovePerfData(context.Context, *RemovePerfDataReq) (*RemovePerfDataResp, error)
	//SearchPerfData 搜索压测数据
	SearchPerfData(context.Context, *SearchPerfDataReq) (*SearchPerfDataResp, error)
	mustEmbedUnimplementedPerfDataServiceServer()
}

// UnimplementedPerfDataServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfDataServiceServer struct{}

func (UnimplementedPerfDataServiceServer) RemovePerfData(context.Context, *RemovePerfDataReq) (*RemovePerfDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePerfData not implemented")
}
func (UnimplementedPerfDataServiceServer) SearchPerfData(context.Context, *SearchPerfDataReq) (*SearchPerfDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfData not implemented")
}
func (UnimplementedPerfDataServiceServer) mustEmbedUnimplementedPerfDataServiceServer() {}
func (UnimplementedPerfDataServiceServer) testEmbeddedByValue()                         {}

// UnsafePerfDataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfDataServiceServer will
// result in compilation errors.
type UnsafePerfDataServiceServer interface {
	mustEmbedUnimplementedPerfDataServiceServer()
}

func RegisterPerfDataServiceServer(s grpc.ServiceRegistrar, srv PerfDataServiceServer) {
	// If the following call pancis, it indicates UnimplementedPerfDataServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfDataService_ServiceDesc, srv)
}

func _PerfDataService_RemovePerfData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePerfDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfDataServiceServer).RemovePerfData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfDataService_RemovePerfData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfDataServiceServer).RemovePerfData(ctx, req.(*RemovePerfDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfDataService_SearchPerfData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfDataServiceServer).SearchPerfData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfDataService_SearchPerfData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfDataServiceServer).SearchPerfData(ctx, req.(*SearchPerfDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfDataService_ServiceDesc is the grpc.ServiceDesc for PerfDataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfDataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PerfDataService",
	HandlerType: (*PerfDataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RemovePerfData",
			Handler:    _PerfDataService_RemovePerfData_Handler,
		},
		{
			MethodName: "SearchPerfData",
			Handler:    _PerfDataService_SearchPerfData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PerfCaseService_RemovePerfCase_FullMethodName = "/manager.PerfCaseService/RemovePerfCase"
	PerfCaseService_ViewPerfCase_FullMethodName   = "/manager.PerfCaseService/ViewPerfCase"
)

// PerfCaseServiceClient is the client API for PerfCaseService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PerfCaseService 压测用例服务
type PerfCaseServiceClient interface {
	//RemovePerfCase 删除压测用例
	RemovePerfCase(ctx context.Context, in *RemovePerfCaseReq, opts ...grpc.CallOption) (*RemovePerfCaseResp, error)
	//ViewPerfCase 查看压测用例
	ViewPerfCase(ctx context.Context, in *ViewPerfCaseReq, opts ...grpc.CallOption) (*ViewPerfCaseResp, error)
}

type perfCaseServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfCaseServiceClient(cc grpc.ClientConnInterface) PerfCaseServiceClient {
	return &perfCaseServiceClient{cc}
}

func (c *perfCaseServiceClient) RemovePerfCase(ctx context.Context, in *RemovePerfCaseReq, opts ...grpc.CallOption) (*RemovePerfCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePerfCaseResp)
	err := c.cc.Invoke(ctx, PerfCaseService_RemovePerfCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfCaseServiceClient) ViewPerfCase(ctx context.Context, in *ViewPerfCaseReq, opts ...grpc.CallOption) (*ViewPerfCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewPerfCaseResp)
	err := c.cc.Invoke(ctx, PerfCaseService_ViewPerfCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfCaseServiceServer is the server API for PerfCaseService service.
// All implementations must embed UnimplementedPerfCaseServiceServer
// for forward compatibility.
//
// PerfCaseService 压测用例服务
type PerfCaseServiceServer interface {
	//RemovePerfCase 删除压测用例
	RemovePerfCase(context.Context, *RemovePerfCaseReq) (*RemovePerfCaseResp, error)
	//ViewPerfCase 查看压测用例
	ViewPerfCase(context.Context, *ViewPerfCaseReq) (*ViewPerfCaseResp, error)
	mustEmbedUnimplementedPerfCaseServiceServer()
}

// UnimplementedPerfCaseServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfCaseServiceServer struct{}

func (UnimplementedPerfCaseServiceServer) RemovePerfCase(context.Context, *RemovePerfCaseReq) (*RemovePerfCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePerfCase not implemented")
}
func (UnimplementedPerfCaseServiceServer) ViewPerfCase(context.Context, *ViewPerfCaseReq) (*ViewPerfCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewPerfCase not implemented")
}
func (UnimplementedPerfCaseServiceServer) mustEmbedUnimplementedPerfCaseServiceServer() {}
func (UnimplementedPerfCaseServiceServer) testEmbeddedByValue()                         {}

// UnsafePerfCaseServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfCaseServiceServer will
// result in compilation errors.
type UnsafePerfCaseServiceServer interface {
	mustEmbedUnimplementedPerfCaseServiceServer()
}

func RegisterPerfCaseServiceServer(s grpc.ServiceRegistrar, srv PerfCaseServiceServer) {
	// If the following call pancis, it indicates UnimplementedPerfCaseServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfCaseService_ServiceDesc, srv)
}

func _PerfCaseService_RemovePerfCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePerfCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfCaseServiceServer).RemovePerfCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfCaseService_RemovePerfCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfCaseServiceServer).RemovePerfCase(ctx, req.(*RemovePerfCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfCaseService_ViewPerfCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewPerfCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfCaseServiceServer).ViewPerfCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfCaseService_ViewPerfCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfCaseServiceServer).ViewPerfCase(ctx, req.(*ViewPerfCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfCaseService_ServiceDesc is the grpc.ServiceDesc for PerfCaseService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfCaseService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PerfCaseService",
	HandlerType: (*PerfCaseServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RemovePerfCase",
			Handler:    _PerfCaseService_RemovePerfCase_Handler,
		},
		{
			MethodName: "ViewPerfCase",
			Handler:    _PerfCaseService_ViewPerfCase_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PerfCaseV2Service_CreatePerfCaseV2_FullMethodName = "/manager.PerfCaseV2Service/CreatePerfCaseV2"
	PerfCaseV2Service_RemovePerfCaseV2_FullMethodName = "/manager.PerfCaseV2Service/RemovePerfCaseV2"
	PerfCaseV2Service_ModifyPerfCaseV2_FullMethodName = "/manager.PerfCaseV2Service/ModifyPerfCaseV2"
	PerfCaseV2Service_SearchPerfCaseV2_FullMethodName = "/manager.PerfCaseV2Service/SearchPerfCaseV2"
	PerfCaseV2Service_ViewPerfCaseV2_FullMethodName   = "/manager.PerfCaseV2Service/ViewPerfCaseV2"
)

// PerfCaseV2ServiceClient is the client API for PerfCaseV2Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PerfCaseV2Service 压测用例V2服务
type PerfCaseV2ServiceClient interface {
	//CreatePerfCaseV2 创建压测用例
	CreatePerfCaseV2(ctx context.Context, in *CreatePerfCaseV2Req, opts ...grpc.CallOption) (*CreatePerfCaseV2Resp, error)
	//RemovePerfCaseV2 删除压测用例
	RemovePerfCaseV2(ctx context.Context, in *RemovePerfCaseV2Req, opts ...grpc.CallOption) (*RemovePerfCaseV2Resp, error)
	//ModifyPerfCaseV2 编辑压测用例
	ModifyPerfCaseV2(ctx context.Context, in *ModifyPerfCaseV2Req, opts ...grpc.CallOption) (*ModifyPerfCaseV2Resp, error)
	//SearchPerfCaseV2 搜索压测用例
	SearchPerfCaseV2(ctx context.Context, in *SearchPerfCaseV2Req, opts ...grpc.CallOption) (*SearchPerfCaseV2Resp, error)
	//ViewPerfCaseV2 查看压测用例
	ViewPerfCaseV2(ctx context.Context, in *ViewPerfCaseV2Req, opts ...grpc.CallOption) (*ViewPerfCaseV2Resp, error)
}

type perfCaseV2ServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfCaseV2ServiceClient(cc grpc.ClientConnInterface) PerfCaseV2ServiceClient {
	return &perfCaseV2ServiceClient{cc}
}

func (c *perfCaseV2ServiceClient) CreatePerfCaseV2(ctx context.Context, in *CreatePerfCaseV2Req, opts ...grpc.CallOption) (*CreatePerfCaseV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePerfCaseV2Resp)
	err := c.cc.Invoke(ctx, PerfCaseV2Service_CreatePerfCaseV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfCaseV2ServiceClient) RemovePerfCaseV2(ctx context.Context, in *RemovePerfCaseV2Req, opts ...grpc.CallOption) (*RemovePerfCaseV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePerfCaseV2Resp)
	err := c.cc.Invoke(ctx, PerfCaseV2Service_RemovePerfCaseV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfCaseV2ServiceClient) ModifyPerfCaseV2(ctx context.Context, in *ModifyPerfCaseV2Req, opts ...grpc.CallOption) (*ModifyPerfCaseV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPerfCaseV2Resp)
	err := c.cc.Invoke(ctx, PerfCaseV2Service_ModifyPerfCaseV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfCaseV2ServiceClient) SearchPerfCaseV2(ctx context.Context, in *SearchPerfCaseV2Req, opts ...grpc.CallOption) (*SearchPerfCaseV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfCaseV2Resp)
	err := c.cc.Invoke(ctx, PerfCaseV2Service_SearchPerfCaseV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfCaseV2ServiceClient) ViewPerfCaseV2(ctx context.Context, in *ViewPerfCaseV2Req, opts ...grpc.CallOption) (*ViewPerfCaseV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewPerfCaseV2Resp)
	err := c.cc.Invoke(ctx, PerfCaseV2Service_ViewPerfCaseV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfCaseV2ServiceServer is the server API for PerfCaseV2Service service.
// All implementations must embed UnimplementedPerfCaseV2ServiceServer
// for forward compatibility.
//
// PerfCaseV2Service 压测用例V2服务
type PerfCaseV2ServiceServer interface {
	//CreatePerfCaseV2 创建压测用例
	CreatePerfCaseV2(context.Context, *CreatePerfCaseV2Req) (*CreatePerfCaseV2Resp, error)
	//RemovePerfCaseV2 删除压测用例
	RemovePerfCaseV2(context.Context, *RemovePerfCaseV2Req) (*RemovePerfCaseV2Resp, error)
	//ModifyPerfCaseV2 编辑压测用例
	ModifyPerfCaseV2(context.Context, *ModifyPerfCaseV2Req) (*ModifyPerfCaseV2Resp, error)
	//SearchPerfCaseV2 搜索压测用例
	SearchPerfCaseV2(context.Context, *SearchPerfCaseV2Req) (*SearchPerfCaseV2Resp, error)
	//ViewPerfCaseV2 查看压测用例
	ViewPerfCaseV2(context.Context, *ViewPerfCaseV2Req) (*ViewPerfCaseV2Resp, error)
	mustEmbedUnimplementedPerfCaseV2ServiceServer()
}

// UnimplementedPerfCaseV2ServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfCaseV2ServiceServer struct{}

func (UnimplementedPerfCaseV2ServiceServer) CreatePerfCaseV2(context.Context, *CreatePerfCaseV2Req) (*CreatePerfCaseV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePerfCaseV2 not implemented")
}
func (UnimplementedPerfCaseV2ServiceServer) RemovePerfCaseV2(context.Context, *RemovePerfCaseV2Req) (*RemovePerfCaseV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePerfCaseV2 not implemented")
}
func (UnimplementedPerfCaseV2ServiceServer) ModifyPerfCaseV2(context.Context, *ModifyPerfCaseV2Req) (*ModifyPerfCaseV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPerfCaseV2 not implemented")
}
func (UnimplementedPerfCaseV2ServiceServer) SearchPerfCaseV2(context.Context, *SearchPerfCaseV2Req) (*SearchPerfCaseV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfCaseV2 not implemented")
}
func (UnimplementedPerfCaseV2ServiceServer) ViewPerfCaseV2(context.Context, *ViewPerfCaseV2Req) (*ViewPerfCaseV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewPerfCaseV2 not implemented")
}
func (UnimplementedPerfCaseV2ServiceServer) mustEmbedUnimplementedPerfCaseV2ServiceServer() {}
func (UnimplementedPerfCaseV2ServiceServer) testEmbeddedByValue()                           {}

// UnsafePerfCaseV2ServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfCaseV2ServiceServer will
// result in compilation errors.
type UnsafePerfCaseV2ServiceServer interface {
	mustEmbedUnimplementedPerfCaseV2ServiceServer()
}

func RegisterPerfCaseV2ServiceServer(s grpc.ServiceRegistrar, srv PerfCaseV2ServiceServer) {
	// If the following call pancis, it indicates UnimplementedPerfCaseV2ServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfCaseV2Service_ServiceDesc, srv)
}

func _PerfCaseV2Service_CreatePerfCaseV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePerfCaseV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfCaseV2ServiceServer).CreatePerfCaseV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfCaseV2Service_CreatePerfCaseV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfCaseV2ServiceServer).CreatePerfCaseV2(ctx, req.(*CreatePerfCaseV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfCaseV2Service_RemovePerfCaseV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePerfCaseV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfCaseV2ServiceServer).RemovePerfCaseV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfCaseV2Service_RemovePerfCaseV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfCaseV2ServiceServer).RemovePerfCaseV2(ctx, req.(*RemovePerfCaseV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfCaseV2Service_ModifyPerfCaseV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPerfCaseV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfCaseV2ServiceServer).ModifyPerfCaseV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfCaseV2Service_ModifyPerfCaseV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfCaseV2ServiceServer).ModifyPerfCaseV2(ctx, req.(*ModifyPerfCaseV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfCaseV2Service_SearchPerfCaseV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfCaseV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfCaseV2ServiceServer).SearchPerfCaseV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfCaseV2Service_SearchPerfCaseV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfCaseV2ServiceServer).SearchPerfCaseV2(ctx, req.(*SearchPerfCaseV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfCaseV2Service_ViewPerfCaseV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewPerfCaseV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfCaseV2ServiceServer).ViewPerfCaseV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfCaseV2Service_ViewPerfCaseV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfCaseV2ServiceServer).ViewPerfCaseV2(ctx, req.(*ViewPerfCaseV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfCaseV2Service_ServiceDesc is the grpc.ServiceDesc for PerfCaseV2Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfCaseV2Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PerfCaseV2Service",
	HandlerType: (*PerfCaseV2ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePerfCaseV2",
			Handler:    _PerfCaseV2Service_CreatePerfCaseV2_Handler,
		},
		{
			MethodName: "RemovePerfCaseV2",
			Handler:    _PerfCaseV2Service_RemovePerfCaseV2_Handler,
		},
		{
			MethodName: "ModifyPerfCaseV2",
			Handler:    _PerfCaseV2Service_ModifyPerfCaseV2_Handler,
		},
		{
			MethodName: "SearchPerfCaseV2",
			Handler:    _PerfCaseV2Service_SearchPerfCaseV2_Handler,
		},
		{
			MethodName: "ViewPerfCaseV2",
			Handler:    _PerfCaseV2Service_ViewPerfCaseV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PerfPlanService_CreatePerfPlan_FullMethodName       = "/manager.PerfPlanService/CreatePerfPlan"
	PerfPlanService_RemovePerfPlan_FullMethodName       = "/manager.PerfPlanService/RemovePerfPlan"
	PerfPlanService_ModifyPerfPlan_FullMethodName       = "/manager.PerfPlanService/ModifyPerfPlan"
	PerfPlanService_SearchPerfPlan_FullMethodName       = "/manager.PerfPlanService/SearchPerfPlan"
	PerfPlanService_ViewPerfPlan_FullMethodName         = "/manager.PerfPlanService/ViewPerfPlan"
	PerfPlanService_SearchCaseInPerfPlan_FullMethodName = "/manager.PerfPlanService/SearchCaseInPerfPlan"
)

// PerfPlanServiceClient is the client API for PerfPlanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PerfPlanService 压测计划服务
type PerfPlanServiceClient interface {
	//CreatePerfPlan 创建压测计划
	CreatePerfPlan(ctx context.Context, in *CreatePerfPlanReq, opts ...grpc.CallOption) (*CreatePerfPlanResp, error)
	//RemovePerfPlan 删除压测计划
	RemovePerfPlan(ctx context.Context, in *RemovePerfPlanReq, opts ...grpc.CallOption) (*RemovePerfPlanResp, error)
	//ModifyPerfPlan 编辑压测计划
	ModifyPerfPlan(ctx context.Context, in *ModifyPerfPlanReq, opts ...grpc.CallOption) (*ModifyPerfPlanResp, error)
	//SearchPerfPlan 搜索压测计划
	SearchPerfPlan(ctx context.Context, in *SearchPerfPlanReq, opts ...grpc.CallOption) (*SearchPerfPlanResp, error)
	//ViewPerfPlan 查看压测计划
	ViewPerfPlan(ctx context.Context, in *ViewPerfPlanReq, opts ...grpc.CallOption) (*ViewPerfPlanResp, error)
	//SearchCaseInPerfPlan 搜索压测计划中的压测用例
	SearchCaseInPerfPlan(ctx context.Context, in *SearchCaseInPerfPlanReq, opts ...grpc.CallOption) (*SearchCaseInPerfPlanResp, error)
}

type perfPlanServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfPlanServiceClient(cc grpc.ClientConnInterface) PerfPlanServiceClient {
	return &perfPlanServiceClient{cc}
}

func (c *perfPlanServiceClient) CreatePerfPlan(ctx context.Context, in *CreatePerfPlanReq, opts ...grpc.CallOption) (*CreatePerfPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePerfPlanResp)
	err := c.cc.Invoke(ctx, PerfPlanService_CreatePerfPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanServiceClient) RemovePerfPlan(ctx context.Context, in *RemovePerfPlanReq, opts ...grpc.CallOption) (*RemovePerfPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePerfPlanResp)
	err := c.cc.Invoke(ctx, PerfPlanService_RemovePerfPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanServiceClient) ModifyPerfPlan(ctx context.Context, in *ModifyPerfPlanReq, opts ...grpc.CallOption) (*ModifyPerfPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPerfPlanResp)
	err := c.cc.Invoke(ctx, PerfPlanService_ModifyPerfPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanServiceClient) SearchPerfPlan(ctx context.Context, in *SearchPerfPlanReq, opts ...grpc.CallOption) (*SearchPerfPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfPlanResp)
	err := c.cc.Invoke(ctx, PerfPlanService_SearchPerfPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanServiceClient) ViewPerfPlan(ctx context.Context, in *ViewPerfPlanReq, opts ...grpc.CallOption) (*ViewPerfPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewPerfPlanResp)
	err := c.cc.Invoke(ctx, PerfPlanService_ViewPerfPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanServiceClient) SearchCaseInPerfPlan(ctx context.Context, in *SearchCaseInPerfPlanReq, opts ...grpc.CallOption) (*SearchCaseInPerfPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCaseInPerfPlanResp)
	err := c.cc.Invoke(ctx, PerfPlanService_SearchCaseInPerfPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfPlanServiceServer is the server API for PerfPlanService service.
// All implementations must embed UnimplementedPerfPlanServiceServer
// for forward compatibility.
//
// PerfPlanService 压测计划服务
type PerfPlanServiceServer interface {
	//CreatePerfPlan 创建压测计划
	CreatePerfPlan(context.Context, *CreatePerfPlanReq) (*CreatePerfPlanResp, error)
	//RemovePerfPlan 删除压测计划
	RemovePerfPlan(context.Context, *RemovePerfPlanReq) (*RemovePerfPlanResp, error)
	//ModifyPerfPlan 编辑压测计划
	ModifyPerfPlan(context.Context, *ModifyPerfPlanReq) (*ModifyPerfPlanResp, error)
	//SearchPerfPlan 搜索压测计划
	SearchPerfPlan(context.Context, *SearchPerfPlanReq) (*SearchPerfPlanResp, error)
	//ViewPerfPlan 查看压测计划
	ViewPerfPlan(context.Context, *ViewPerfPlanReq) (*ViewPerfPlanResp, error)
	//SearchCaseInPerfPlan 搜索压测计划中的压测用例
	SearchCaseInPerfPlan(context.Context, *SearchCaseInPerfPlanReq) (*SearchCaseInPerfPlanResp, error)
	mustEmbedUnimplementedPerfPlanServiceServer()
}

// UnimplementedPerfPlanServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfPlanServiceServer struct{}

func (UnimplementedPerfPlanServiceServer) CreatePerfPlan(context.Context, *CreatePerfPlanReq) (*CreatePerfPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePerfPlan not implemented")
}
func (UnimplementedPerfPlanServiceServer) RemovePerfPlan(context.Context, *RemovePerfPlanReq) (*RemovePerfPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePerfPlan not implemented")
}
func (UnimplementedPerfPlanServiceServer) ModifyPerfPlan(context.Context, *ModifyPerfPlanReq) (*ModifyPerfPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPerfPlan not implemented")
}
func (UnimplementedPerfPlanServiceServer) SearchPerfPlan(context.Context, *SearchPerfPlanReq) (*SearchPerfPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfPlan not implemented")
}
func (UnimplementedPerfPlanServiceServer) ViewPerfPlan(context.Context, *ViewPerfPlanReq) (*ViewPerfPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewPerfPlan not implemented")
}
func (UnimplementedPerfPlanServiceServer) SearchCaseInPerfPlan(context.Context, *SearchCaseInPerfPlanReq) (*SearchCaseInPerfPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCaseInPerfPlan not implemented")
}
func (UnimplementedPerfPlanServiceServer) mustEmbedUnimplementedPerfPlanServiceServer() {}
func (UnimplementedPerfPlanServiceServer) testEmbeddedByValue()                         {}

// UnsafePerfPlanServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfPlanServiceServer will
// result in compilation errors.
type UnsafePerfPlanServiceServer interface {
	mustEmbedUnimplementedPerfPlanServiceServer()
}

func RegisterPerfPlanServiceServer(s grpc.ServiceRegistrar, srv PerfPlanServiceServer) {
	// If the following call pancis, it indicates UnimplementedPerfPlanServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfPlanService_ServiceDesc, srv)
}

func _PerfPlanService_CreatePerfPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePerfPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanServiceServer).CreatePerfPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanService_CreatePerfPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanServiceServer).CreatePerfPlan(ctx, req.(*CreatePerfPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanService_RemovePerfPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePerfPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanServiceServer).RemovePerfPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanService_RemovePerfPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanServiceServer).RemovePerfPlan(ctx, req.(*RemovePerfPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanService_ModifyPerfPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPerfPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanServiceServer).ModifyPerfPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanService_ModifyPerfPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanServiceServer).ModifyPerfPlan(ctx, req.(*ModifyPerfPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanService_SearchPerfPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanServiceServer).SearchPerfPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanService_SearchPerfPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanServiceServer).SearchPerfPlan(ctx, req.(*SearchPerfPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanService_ViewPerfPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewPerfPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanServiceServer).ViewPerfPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanService_ViewPerfPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanServiceServer).ViewPerfPlan(ctx, req.(*ViewPerfPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanService_SearchCaseInPerfPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCaseInPerfPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanServiceServer).SearchCaseInPerfPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanService_SearchCaseInPerfPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanServiceServer).SearchCaseInPerfPlan(ctx, req.(*SearchCaseInPerfPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfPlanService_ServiceDesc is the grpc.ServiceDesc for PerfPlanService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfPlanService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PerfPlanService",
	HandlerType: (*PerfPlanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePerfPlan",
			Handler:    _PerfPlanService_CreatePerfPlan_Handler,
		},
		{
			MethodName: "RemovePerfPlan",
			Handler:    _PerfPlanService_RemovePerfPlan_Handler,
		},
		{
			MethodName: "ModifyPerfPlan",
			Handler:    _PerfPlanService_ModifyPerfPlan_Handler,
		},
		{
			MethodName: "SearchPerfPlan",
			Handler:    _PerfPlanService_SearchPerfPlan_Handler,
		},
		{
			MethodName: "ViewPerfPlan",
			Handler:    _PerfPlanService_ViewPerfPlan_Handler,
		},
		{
			MethodName: "SearchCaseInPerfPlan",
			Handler:    _PerfPlanService_SearchCaseInPerfPlan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PerfPlanV2Service_CreatePerfPlanV2_FullMethodName           = "/manager.PerfPlanV2Service/CreatePerfPlanV2"
	PerfPlanV2Service_RemovePerfPlanV2_FullMethodName           = "/manager.PerfPlanV2Service/RemovePerfPlanV2"
	PerfPlanV2Service_ModifyPerfPlanV2_FullMethodName           = "/manager.PerfPlanV2Service/ModifyPerfPlanV2"
	PerfPlanV2Service_SearchPerfPlanV2_FullMethodName           = "/manager.PerfPlanV2Service/SearchPerfPlanV2"
	PerfPlanV2Service_ViewPerfPlanV2_FullMethodName             = "/manager.PerfPlanV2Service/ViewPerfPlanV2"
	PerfPlanV2Service_SearchCaseInPerfPlanV2_FullMethodName     = "/manager.PerfPlanV2Service/SearchCaseInPerfPlanV2"
	PerfPlanV2Service_SearchProtobufInPerfPlanV2_FullMethodName = "/manager.PerfPlanV2Service/SearchProtobufInPerfPlanV2"
	PerfPlanV2Service_SearchRuleInPerfPlanV2_FullMethodName     = "/manager.PerfPlanV2Service/SearchRuleInPerfPlanV2"
	PerfPlanV2Service_UpdatePerfPlanByCase_FullMethodName       = "/manager.PerfPlanV2Service/UpdatePerfPlanByCase"
	PerfPlanV2Service_UpdatePerfPlanByChatID_FullMethodName     = "/manager.PerfPlanV2Service/UpdatePerfPlanByChatID"
)

// PerfPlanV2ServiceClient is the client API for PerfPlanV2Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PerfPlanV2Service 压测计划V2服务
type PerfPlanV2ServiceClient interface {
	//CreatePerfPlanV2 创建压测计划
	CreatePerfPlanV2(ctx context.Context, in *CreatePerfPlanV2Req, opts ...grpc.CallOption) (*CreatePerfPlanV2Resp, error)
	//RemovePerfPlanV2 删除压测计划
	RemovePerfPlanV2(ctx context.Context, in *RemovePerfPlanV2Req, opts ...grpc.CallOption) (*RemovePerfPlanV2Resp, error)
	//ModifyPerfPlanV2 编辑压测计划
	ModifyPerfPlanV2(ctx context.Context, in *ModifyPerfPlanV2Req, opts ...grpc.CallOption) (*ModifyPerfPlanV2Resp, error)
	//SearchPerfPlanV2 搜索压测计划
	SearchPerfPlanV2(ctx context.Context, in *SearchPerfPlanV2Req, opts ...grpc.CallOption) (*SearchPerfPlanV2Resp, error)
	//ViewPerfPlanV2 查看压测计划
	ViewPerfPlanV2(ctx context.Context, in *ViewPerfPlanV2Req, opts ...grpc.CallOption) (*ViewPerfPlanV2Resp, error)
	//SearchCaseInPerfPlanV2 搜索压测计划中的压测用例
	SearchCaseInPerfPlanV2(ctx context.Context, in *SearchCaseInPerfPlanV2Req, opts ...grpc.CallOption) (*SearchCaseInPerfPlanV2Resp, error)
	//SearchProtobufInPerfPlanV2 搜索压测计划中的Protobuf配置
	SearchProtobufInPerfPlanV2(ctx context.Context, in *SearchProtobufInPerfPlanV2Req, opts ...grpc.CallOption) (*SearchProtobufInPerfPlanV2Resp, error)
	//SearchRuleInPerfPlanV2 搜索压测计划中的停止规则
	SearchRuleInPerfPlanV2(ctx context.Context, in *SearchRuleInPerfPlanV2Req, opts ...grpc.CallOption) (*SearchRuleInPerfPlanV2Resp, error)
	//UpdatePerfPlanByCase 通过编辑压测用例触发关联的压测计划进行相应的更新（包括：持续时长、压测数据、施压机资源）
	UpdatePerfPlanByCase(ctx context.Context, in *UpdatePerfPlanByCaseReq, opts ...grpc.CallOption) (*UpdatePerfPlanByCaseResp, error)
	//UpdatePerfPlanByChatID 更新压测计划中自动创建的飞书群ID
	UpdatePerfPlanByChatID(ctx context.Context, in *UpdatePerfPlanByChatIDReq, opts ...grpc.CallOption) (*UpdatePerfPlanByChatIDResp, error)
}

type perfPlanV2ServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfPlanV2ServiceClient(cc grpc.ClientConnInterface) PerfPlanV2ServiceClient {
	return &perfPlanV2ServiceClient{cc}
}

func (c *perfPlanV2ServiceClient) CreatePerfPlanV2(ctx context.Context, in *CreatePerfPlanV2Req, opts ...grpc.CallOption) (*CreatePerfPlanV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePerfPlanV2Resp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_CreatePerfPlanV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) RemovePerfPlanV2(ctx context.Context, in *RemovePerfPlanV2Req, opts ...grpc.CallOption) (*RemovePerfPlanV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePerfPlanV2Resp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_RemovePerfPlanV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) ModifyPerfPlanV2(ctx context.Context, in *ModifyPerfPlanV2Req, opts ...grpc.CallOption) (*ModifyPerfPlanV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPerfPlanV2Resp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_ModifyPerfPlanV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) SearchPerfPlanV2(ctx context.Context, in *SearchPerfPlanV2Req, opts ...grpc.CallOption) (*SearchPerfPlanV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfPlanV2Resp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_SearchPerfPlanV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) ViewPerfPlanV2(ctx context.Context, in *ViewPerfPlanV2Req, opts ...grpc.CallOption) (*ViewPerfPlanV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewPerfPlanV2Resp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_ViewPerfPlanV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) SearchCaseInPerfPlanV2(ctx context.Context, in *SearchCaseInPerfPlanV2Req, opts ...grpc.CallOption) (*SearchCaseInPerfPlanV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCaseInPerfPlanV2Resp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_SearchCaseInPerfPlanV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) SearchProtobufInPerfPlanV2(ctx context.Context, in *SearchProtobufInPerfPlanV2Req, opts ...grpc.CallOption) (*SearchProtobufInPerfPlanV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchProtobufInPerfPlanV2Resp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_SearchProtobufInPerfPlanV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) SearchRuleInPerfPlanV2(ctx context.Context, in *SearchRuleInPerfPlanV2Req, opts ...grpc.CallOption) (*SearchRuleInPerfPlanV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchRuleInPerfPlanV2Resp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_SearchRuleInPerfPlanV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) UpdatePerfPlanByCase(ctx context.Context, in *UpdatePerfPlanByCaseReq, opts ...grpc.CallOption) (*UpdatePerfPlanByCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePerfPlanByCaseResp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_UpdatePerfPlanByCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfPlanV2ServiceClient) UpdatePerfPlanByChatID(ctx context.Context, in *UpdatePerfPlanByChatIDReq, opts ...grpc.CallOption) (*UpdatePerfPlanByChatIDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePerfPlanByChatIDResp)
	err := c.cc.Invoke(ctx, PerfPlanV2Service_UpdatePerfPlanByChatID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfPlanV2ServiceServer is the server API for PerfPlanV2Service service.
// All implementations must embed UnimplementedPerfPlanV2ServiceServer
// for forward compatibility.
//
// PerfPlanV2Service 压测计划V2服务
type PerfPlanV2ServiceServer interface {
	//CreatePerfPlanV2 创建压测计划
	CreatePerfPlanV2(context.Context, *CreatePerfPlanV2Req) (*CreatePerfPlanV2Resp, error)
	//RemovePerfPlanV2 删除压测计划
	RemovePerfPlanV2(context.Context, *RemovePerfPlanV2Req) (*RemovePerfPlanV2Resp, error)
	//ModifyPerfPlanV2 编辑压测计划
	ModifyPerfPlanV2(context.Context, *ModifyPerfPlanV2Req) (*ModifyPerfPlanV2Resp, error)
	//SearchPerfPlanV2 搜索压测计划
	SearchPerfPlanV2(context.Context, *SearchPerfPlanV2Req) (*SearchPerfPlanV2Resp, error)
	//ViewPerfPlanV2 查看压测计划
	ViewPerfPlanV2(context.Context, *ViewPerfPlanV2Req) (*ViewPerfPlanV2Resp, error)
	//SearchCaseInPerfPlanV2 搜索压测计划中的压测用例
	SearchCaseInPerfPlanV2(context.Context, *SearchCaseInPerfPlanV2Req) (*SearchCaseInPerfPlanV2Resp, error)
	//SearchProtobufInPerfPlanV2 搜索压测计划中的Protobuf配置
	SearchProtobufInPerfPlanV2(context.Context, *SearchProtobufInPerfPlanV2Req) (*SearchProtobufInPerfPlanV2Resp, error)
	//SearchRuleInPerfPlanV2 搜索压测计划中的停止规则
	SearchRuleInPerfPlanV2(context.Context, *SearchRuleInPerfPlanV2Req) (*SearchRuleInPerfPlanV2Resp, error)
	//UpdatePerfPlanByCase 通过编辑压测用例触发关联的压测计划进行相应的更新（包括：持续时长、压测数据、施压机资源）
	UpdatePerfPlanByCase(context.Context, *UpdatePerfPlanByCaseReq) (*UpdatePerfPlanByCaseResp, error)
	//UpdatePerfPlanByChatID 更新压测计划中自动创建的飞书群ID
	UpdatePerfPlanByChatID(context.Context, *UpdatePerfPlanByChatIDReq) (*UpdatePerfPlanByChatIDResp, error)
	mustEmbedUnimplementedPerfPlanV2ServiceServer()
}

// UnimplementedPerfPlanV2ServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfPlanV2ServiceServer struct{}

func (UnimplementedPerfPlanV2ServiceServer) CreatePerfPlanV2(context.Context, *CreatePerfPlanV2Req) (*CreatePerfPlanV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePerfPlanV2 not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) RemovePerfPlanV2(context.Context, *RemovePerfPlanV2Req) (*RemovePerfPlanV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePerfPlanV2 not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) ModifyPerfPlanV2(context.Context, *ModifyPerfPlanV2Req) (*ModifyPerfPlanV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPerfPlanV2 not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) SearchPerfPlanV2(context.Context, *SearchPerfPlanV2Req) (*SearchPerfPlanV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfPlanV2 not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) ViewPerfPlanV2(context.Context, *ViewPerfPlanV2Req) (*ViewPerfPlanV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewPerfPlanV2 not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) SearchCaseInPerfPlanV2(context.Context, *SearchCaseInPerfPlanV2Req) (*SearchCaseInPerfPlanV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCaseInPerfPlanV2 not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) SearchProtobufInPerfPlanV2(context.Context, *SearchProtobufInPerfPlanV2Req) (*SearchProtobufInPerfPlanV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchProtobufInPerfPlanV2 not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) SearchRuleInPerfPlanV2(context.Context, *SearchRuleInPerfPlanV2Req) (*SearchRuleInPerfPlanV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchRuleInPerfPlanV2 not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) UpdatePerfPlanByCase(context.Context, *UpdatePerfPlanByCaseReq) (*UpdatePerfPlanByCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePerfPlanByCase not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) UpdatePerfPlanByChatID(context.Context, *UpdatePerfPlanByChatIDReq) (*UpdatePerfPlanByChatIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePerfPlanByChatID not implemented")
}
func (UnimplementedPerfPlanV2ServiceServer) mustEmbedUnimplementedPerfPlanV2ServiceServer() {}
func (UnimplementedPerfPlanV2ServiceServer) testEmbeddedByValue()                           {}

// UnsafePerfPlanV2ServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfPlanV2ServiceServer will
// result in compilation errors.
type UnsafePerfPlanV2ServiceServer interface {
	mustEmbedUnimplementedPerfPlanV2ServiceServer()
}

func RegisterPerfPlanV2ServiceServer(s grpc.ServiceRegistrar, srv PerfPlanV2ServiceServer) {
	// If the following call pancis, it indicates UnimplementedPerfPlanV2ServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfPlanV2Service_ServiceDesc, srv)
}

func _PerfPlanV2Service_CreatePerfPlanV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePerfPlanV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).CreatePerfPlanV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_CreatePerfPlanV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).CreatePerfPlanV2(ctx, req.(*CreatePerfPlanV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_RemovePerfPlanV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePerfPlanV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).RemovePerfPlanV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_RemovePerfPlanV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).RemovePerfPlanV2(ctx, req.(*RemovePerfPlanV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_ModifyPerfPlanV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPerfPlanV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).ModifyPerfPlanV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_ModifyPerfPlanV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).ModifyPerfPlanV2(ctx, req.(*ModifyPerfPlanV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_SearchPerfPlanV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfPlanV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).SearchPerfPlanV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_SearchPerfPlanV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).SearchPerfPlanV2(ctx, req.(*SearchPerfPlanV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_ViewPerfPlanV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewPerfPlanV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).ViewPerfPlanV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_ViewPerfPlanV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).ViewPerfPlanV2(ctx, req.(*ViewPerfPlanV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_SearchCaseInPerfPlanV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCaseInPerfPlanV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).SearchCaseInPerfPlanV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_SearchCaseInPerfPlanV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).SearchCaseInPerfPlanV2(ctx, req.(*SearchCaseInPerfPlanV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_SearchProtobufInPerfPlanV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchProtobufInPerfPlanV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).SearchProtobufInPerfPlanV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_SearchProtobufInPerfPlanV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).SearchProtobufInPerfPlanV2(ctx, req.(*SearchProtobufInPerfPlanV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_SearchRuleInPerfPlanV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchRuleInPerfPlanV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).SearchRuleInPerfPlanV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_SearchRuleInPerfPlanV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).SearchRuleInPerfPlanV2(ctx, req.(*SearchRuleInPerfPlanV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_UpdatePerfPlanByCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePerfPlanByCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).UpdatePerfPlanByCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_UpdatePerfPlanByCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).UpdatePerfPlanByCase(ctx, req.(*UpdatePerfPlanByCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfPlanV2Service_UpdatePerfPlanByChatID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePerfPlanByChatIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfPlanV2ServiceServer).UpdatePerfPlanByChatID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfPlanV2Service_UpdatePerfPlanByChatID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfPlanV2ServiceServer).UpdatePerfPlanByChatID(ctx, req.(*UpdatePerfPlanByChatIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfPlanV2Service_ServiceDesc is the grpc.ServiceDesc for PerfPlanV2Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfPlanV2Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PerfPlanV2Service",
	HandlerType: (*PerfPlanV2ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePerfPlanV2",
			Handler:    _PerfPlanV2Service_CreatePerfPlanV2_Handler,
		},
		{
			MethodName: "RemovePerfPlanV2",
			Handler:    _PerfPlanV2Service_RemovePerfPlanV2_Handler,
		},
		{
			MethodName: "ModifyPerfPlanV2",
			Handler:    _PerfPlanV2Service_ModifyPerfPlanV2_Handler,
		},
		{
			MethodName: "SearchPerfPlanV2",
			Handler:    _PerfPlanV2Service_SearchPerfPlanV2_Handler,
		},
		{
			MethodName: "ViewPerfPlanV2",
			Handler:    _PerfPlanV2Service_ViewPerfPlanV2_Handler,
		},
		{
			MethodName: "SearchCaseInPerfPlanV2",
			Handler:    _PerfPlanV2Service_SearchCaseInPerfPlanV2_Handler,
		},
		{
			MethodName: "SearchProtobufInPerfPlanV2",
			Handler:    _PerfPlanV2Service_SearchProtobufInPerfPlanV2_Handler,
		},
		{
			MethodName: "SearchRuleInPerfPlanV2",
			Handler:    _PerfPlanV2Service_SearchRuleInPerfPlanV2_Handler,
		},
		{
			MethodName: "UpdatePerfPlanByCase",
			Handler:    _PerfPlanV2Service_UpdatePerfPlanByCase_Handler,
		},
		{
			MethodName: "UpdatePerfPlanByChatID",
			Handler:    _PerfPlanV2Service_UpdatePerfPlanByChatID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PerfStopRuleService_CreatePerfStopRule_FullMethodName = "/manager.PerfStopRuleService/CreatePerfStopRule"
	PerfStopRuleService_RemovePerfStopRule_FullMethodName = "/manager.PerfStopRuleService/RemovePerfStopRule"
	PerfStopRuleService_ModifyPerfStopRule_FullMethodName = "/manager.PerfStopRuleService/ModifyPerfStopRule"
	PerfStopRuleService_SearchPerfStopRule_FullMethodName = "/manager.PerfStopRuleService/SearchPerfStopRule"
	PerfStopRuleService_ViewPerfStopRule_FullMethodName   = "/manager.PerfStopRuleService/ViewPerfStopRule"
)

// PerfStopRuleServiceClient is the client API for PerfStopRuleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PerfStopRuleService 压测停止规则服务
type PerfStopRuleServiceClient interface {
	//CreatePerfStopRule 创建压测停止规则
	CreatePerfStopRule(ctx context.Context, in *CreatePerfStopRuleReq, opts ...grpc.CallOption) (*CreatePerfStopRuleResp, error)
	//RemovePerfStopRule 删除压测停止规则
	RemovePerfStopRule(ctx context.Context, in *RemovePerfStopRuleReq, opts ...grpc.CallOption) (*RemovePerfStopRuleResp, error)
	//ModifyPerfStopRule 编辑压测停止规则
	ModifyPerfStopRule(ctx context.Context, in *ModifyPerfStopRuleReq, opts ...grpc.CallOption) (*ModifyPerfStopRuleResp, error)
	//SearchPerfStopRule 搜索压测停止规则
	SearchPerfStopRule(ctx context.Context, in *SearchPerfStopRuleReq, opts ...grpc.CallOption) (*SearchPerfStopRuleResp, error)
	//ViewPerfStopRule 查看压测停止规则
	ViewPerfStopRule(ctx context.Context, in *ViewPerfStopRuleReq, opts ...grpc.CallOption) (*ViewPerfStopRuleResp, error)
}

type perfStopRuleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfStopRuleServiceClient(cc grpc.ClientConnInterface) PerfStopRuleServiceClient {
	return &perfStopRuleServiceClient{cc}
}

func (c *perfStopRuleServiceClient) CreatePerfStopRule(ctx context.Context, in *CreatePerfStopRuleReq, opts ...grpc.CallOption) (*CreatePerfStopRuleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePerfStopRuleResp)
	err := c.cc.Invoke(ctx, PerfStopRuleService_CreatePerfStopRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfStopRuleServiceClient) RemovePerfStopRule(ctx context.Context, in *RemovePerfStopRuleReq, opts ...grpc.CallOption) (*RemovePerfStopRuleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePerfStopRuleResp)
	err := c.cc.Invoke(ctx, PerfStopRuleService_RemovePerfStopRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfStopRuleServiceClient) ModifyPerfStopRule(ctx context.Context, in *ModifyPerfStopRuleReq, opts ...grpc.CallOption) (*ModifyPerfStopRuleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPerfStopRuleResp)
	err := c.cc.Invoke(ctx, PerfStopRuleService_ModifyPerfStopRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfStopRuleServiceClient) SearchPerfStopRule(ctx context.Context, in *SearchPerfStopRuleReq, opts ...grpc.CallOption) (*SearchPerfStopRuleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfStopRuleResp)
	err := c.cc.Invoke(ctx, PerfStopRuleService_SearchPerfStopRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfStopRuleServiceClient) ViewPerfStopRule(ctx context.Context, in *ViewPerfStopRuleReq, opts ...grpc.CallOption) (*ViewPerfStopRuleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewPerfStopRuleResp)
	err := c.cc.Invoke(ctx, PerfStopRuleService_ViewPerfStopRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfStopRuleServiceServer is the server API for PerfStopRuleService service.
// All implementations must embed UnimplementedPerfStopRuleServiceServer
// for forward compatibility.
//
// PerfStopRuleService 压测停止规则服务
type PerfStopRuleServiceServer interface {
	//CreatePerfStopRule 创建压测停止规则
	CreatePerfStopRule(context.Context, *CreatePerfStopRuleReq) (*CreatePerfStopRuleResp, error)
	//RemovePerfStopRule 删除压测停止规则
	RemovePerfStopRule(context.Context, *RemovePerfStopRuleReq) (*RemovePerfStopRuleResp, error)
	//ModifyPerfStopRule 编辑压测停止规则
	ModifyPerfStopRule(context.Context, *ModifyPerfStopRuleReq) (*ModifyPerfStopRuleResp, error)
	//SearchPerfStopRule 搜索压测停止规则
	SearchPerfStopRule(context.Context, *SearchPerfStopRuleReq) (*SearchPerfStopRuleResp, error)
	//ViewPerfStopRule 查看压测停止规则
	ViewPerfStopRule(context.Context, *ViewPerfStopRuleReq) (*ViewPerfStopRuleResp, error)
	mustEmbedUnimplementedPerfStopRuleServiceServer()
}

// UnimplementedPerfStopRuleServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfStopRuleServiceServer struct{}

func (UnimplementedPerfStopRuleServiceServer) CreatePerfStopRule(context.Context, *CreatePerfStopRuleReq) (*CreatePerfStopRuleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePerfStopRule not implemented")
}
func (UnimplementedPerfStopRuleServiceServer) RemovePerfStopRule(context.Context, *RemovePerfStopRuleReq) (*RemovePerfStopRuleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePerfStopRule not implemented")
}
func (UnimplementedPerfStopRuleServiceServer) ModifyPerfStopRule(context.Context, *ModifyPerfStopRuleReq) (*ModifyPerfStopRuleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPerfStopRule not implemented")
}
func (UnimplementedPerfStopRuleServiceServer) SearchPerfStopRule(context.Context, *SearchPerfStopRuleReq) (*SearchPerfStopRuleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfStopRule not implemented")
}
func (UnimplementedPerfStopRuleServiceServer) ViewPerfStopRule(context.Context, *ViewPerfStopRuleReq) (*ViewPerfStopRuleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewPerfStopRule not implemented")
}
func (UnimplementedPerfStopRuleServiceServer) mustEmbedUnimplementedPerfStopRuleServiceServer() {}
func (UnimplementedPerfStopRuleServiceServer) testEmbeddedByValue()                             {}

// UnsafePerfStopRuleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfStopRuleServiceServer will
// result in compilation errors.
type UnsafePerfStopRuleServiceServer interface {
	mustEmbedUnimplementedPerfStopRuleServiceServer()
}

func RegisterPerfStopRuleServiceServer(s grpc.ServiceRegistrar, srv PerfStopRuleServiceServer) {
	// If the following call pancis, it indicates UnimplementedPerfStopRuleServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfStopRuleService_ServiceDesc, srv)
}

func _PerfStopRuleService_CreatePerfStopRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePerfStopRuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfStopRuleServiceServer).CreatePerfStopRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfStopRuleService_CreatePerfStopRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfStopRuleServiceServer).CreatePerfStopRule(ctx, req.(*CreatePerfStopRuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfStopRuleService_RemovePerfStopRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePerfStopRuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfStopRuleServiceServer).RemovePerfStopRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfStopRuleService_RemovePerfStopRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfStopRuleServiceServer).RemovePerfStopRule(ctx, req.(*RemovePerfStopRuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfStopRuleService_ModifyPerfStopRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPerfStopRuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfStopRuleServiceServer).ModifyPerfStopRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfStopRuleService_ModifyPerfStopRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfStopRuleServiceServer).ModifyPerfStopRule(ctx, req.(*ModifyPerfStopRuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfStopRuleService_SearchPerfStopRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfStopRuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfStopRuleServiceServer).SearchPerfStopRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfStopRuleService_SearchPerfStopRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfStopRuleServiceServer).SearchPerfStopRule(ctx, req.(*SearchPerfStopRuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfStopRuleService_ViewPerfStopRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewPerfStopRuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfStopRuleServiceServer).ViewPerfStopRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfStopRuleService_ViewPerfStopRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfStopRuleServiceServer).ViewPerfStopRule(ctx, req.(*ViewPerfStopRuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfStopRuleService_ServiceDesc is the grpc.ServiceDesc for PerfStopRuleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfStopRuleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PerfStopRuleService",
	HandlerType: (*PerfStopRuleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePerfStopRule",
			Handler:    _PerfStopRuleService_CreatePerfStopRule_Handler,
		},
		{
			MethodName: "RemovePerfStopRule",
			Handler:    _PerfStopRuleService_RemovePerfStopRule_Handler,
		},
		{
			MethodName: "ModifyPerfStopRule",
			Handler:    _PerfStopRuleService_ModifyPerfStopRule_Handler,
		},
		{
			MethodName: "SearchPerfStopRule",
			Handler:    _PerfStopRuleService_SearchPerfStopRule_Handler,
		},
		{
			MethodName: "ViewPerfStopRule",
			Handler:    _PerfStopRuleService_ViewPerfStopRule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PerfLarkChatService_ModifyPerfLarkChat_FullMethodName = "/manager.PerfLarkChatService/ModifyPerfLarkChat"
	PerfLarkChatService_SearchPerfLarkChat_FullMethodName = "/manager.PerfLarkChatService/SearchPerfLarkChat"
	PerfLarkChatService_DeletePerfLarkChat_FullMethodName = "/manager.PerfLarkChatService/DeletePerfLarkChat"
	PerfLarkChatService_UpdatePerfLarkChat_FullMethodName = "/manager.PerfLarkChatService/UpdatePerfLarkChat"
)

// PerfLarkChatServiceClient is the client API for PerfLarkChatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PerfLarkChatService 压测通知飞书群组服务
type PerfLarkChatServiceClient interface {
	//ModifyPerfLarkChat 编辑压测通知飞书群组列表
	ModifyPerfLarkChat(ctx context.Context, in *ModifyPerfLarkChatReq, opts ...grpc.CallOption) (*ModifyPerfLarkChatResp, error)
	//SearchPerfLarkChat 搜索压测通知飞书群组
	SearchPerfLarkChat(ctx context.Context, in *SearchPerfLarkChatReq, opts ...grpc.CallOption) (*SearchPerfLarkChatResp, error)
	//DeletePerfLarkChat 删除压测通知飞书群组（由飞书群解散事件触发）
	DeletePerfLarkChat(ctx context.Context, in *DeletePerfLarkChatReq, opts ...grpc.CallOption) (*DeletePerfLarkChatResp, error)
	//UpdatePerfLarkChat 更新压测通知飞书群组（由飞书群配置修改事件触发）
	UpdatePerfLarkChat(ctx context.Context, in *UpdatePerfLarkChatReq, opts ...grpc.CallOption) (*UpdatePerfLarkChatResp, error)
}

type perfLarkChatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfLarkChatServiceClient(cc grpc.ClientConnInterface) PerfLarkChatServiceClient {
	return &perfLarkChatServiceClient{cc}
}

func (c *perfLarkChatServiceClient) ModifyPerfLarkChat(ctx context.Context, in *ModifyPerfLarkChatReq, opts ...grpc.CallOption) (*ModifyPerfLarkChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyPerfLarkChatResp)
	err := c.cc.Invoke(ctx, PerfLarkChatService_ModifyPerfLarkChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfLarkChatServiceClient) SearchPerfLarkChat(ctx context.Context, in *SearchPerfLarkChatReq, opts ...grpc.CallOption) (*SearchPerfLarkChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfLarkChatResp)
	err := c.cc.Invoke(ctx, PerfLarkChatService_SearchPerfLarkChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfLarkChatServiceClient) DeletePerfLarkChat(ctx context.Context, in *DeletePerfLarkChatReq, opts ...grpc.CallOption) (*DeletePerfLarkChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeletePerfLarkChatResp)
	err := c.cc.Invoke(ctx, PerfLarkChatService_DeletePerfLarkChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfLarkChatServiceClient) UpdatePerfLarkChat(ctx context.Context, in *UpdatePerfLarkChatReq, opts ...grpc.CallOption) (*UpdatePerfLarkChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePerfLarkChatResp)
	err := c.cc.Invoke(ctx, PerfLarkChatService_UpdatePerfLarkChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfLarkChatServiceServer is the server API for PerfLarkChatService service.
// All implementations must embed UnimplementedPerfLarkChatServiceServer
// for forward compatibility.
//
// PerfLarkChatService 压测通知飞书群组服务
type PerfLarkChatServiceServer interface {
	//ModifyPerfLarkChat 编辑压测通知飞书群组列表
	ModifyPerfLarkChat(context.Context, *ModifyPerfLarkChatReq) (*ModifyPerfLarkChatResp, error)
	//SearchPerfLarkChat 搜索压测通知飞书群组
	SearchPerfLarkChat(context.Context, *SearchPerfLarkChatReq) (*SearchPerfLarkChatResp, error)
	//DeletePerfLarkChat 删除压测通知飞书群组（由飞书群解散事件触发）
	DeletePerfLarkChat(context.Context, *DeletePerfLarkChatReq) (*DeletePerfLarkChatResp, error)
	//UpdatePerfLarkChat 更新压测通知飞书群组（由飞书群配置修改事件触发）
	UpdatePerfLarkChat(context.Context, *UpdatePerfLarkChatReq) (*UpdatePerfLarkChatResp, error)
	mustEmbedUnimplementedPerfLarkChatServiceServer()
}

// UnimplementedPerfLarkChatServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfLarkChatServiceServer struct{}

func (UnimplementedPerfLarkChatServiceServer) ModifyPerfLarkChat(context.Context, *ModifyPerfLarkChatReq) (*ModifyPerfLarkChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPerfLarkChat not implemented")
}
func (UnimplementedPerfLarkChatServiceServer) SearchPerfLarkChat(context.Context, *SearchPerfLarkChatReq) (*SearchPerfLarkChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfLarkChat not implemented")
}
func (UnimplementedPerfLarkChatServiceServer) DeletePerfLarkChat(context.Context, *DeletePerfLarkChatReq) (*DeletePerfLarkChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePerfLarkChat not implemented")
}
func (UnimplementedPerfLarkChatServiceServer) UpdatePerfLarkChat(context.Context, *UpdatePerfLarkChatReq) (*UpdatePerfLarkChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePerfLarkChat not implemented")
}
func (UnimplementedPerfLarkChatServiceServer) mustEmbedUnimplementedPerfLarkChatServiceServer() {}
func (UnimplementedPerfLarkChatServiceServer) testEmbeddedByValue()                             {}

// UnsafePerfLarkChatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfLarkChatServiceServer will
// result in compilation errors.
type UnsafePerfLarkChatServiceServer interface {
	mustEmbedUnimplementedPerfLarkChatServiceServer()
}

func RegisterPerfLarkChatServiceServer(s grpc.ServiceRegistrar, srv PerfLarkChatServiceServer) {
	// If the following call pancis, it indicates UnimplementedPerfLarkChatServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfLarkChatService_ServiceDesc, srv)
}

func _PerfLarkChatService_ModifyPerfLarkChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPerfLarkChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfLarkChatServiceServer).ModifyPerfLarkChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfLarkChatService_ModifyPerfLarkChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfLarkChatServiceServer).ModifyPerfLarkChat(ctx, req.(*ModifyPerfLarkChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfLarkChatService_SearchPerfLarkChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfLarkChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfLarkChatServiceServer).SearchPerfLarkChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfLarkChatService_SearchPerfLarkChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfLarkChatServiceServer).SearchPerfLarkChat(ctx, req.(*SearchPerfLarkChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfLarkChatService_DeletePerfLarkChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePerfLarkChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfLarkChatServiceServer).DeletePerfLarkChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfLarkChatService_DeletePerfLarkChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfLarkChatServiceServer).DeletePerfLarkChat(ctx, req.(*DeletePerfLarkChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfLarkChatService_UpdatePerfLarkChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePerfLarkChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfLarkChatServiceServer).UpdatePerfLarkChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfLarkChatService_UpdatePerfLarkChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfLarkChatServiceServer).UpdatePerfLarkChat(ctx, req.(*UpdatePerfLarkChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfLarkChatService_ServiceDesc is the grpc.ServiceDesc for PerfLarkChatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfLarkChatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PerfLarkChatService",
	HandlerType: (*PerfLarkChatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ModifyPerfLarkChat",
			Handler:    _PerfLarkChatService_ModifyPerfLarkChat_Handler,
		},
		{
			MethodName: "SearchPerfLarkChat",
			Handler:    _PerfLarkChatService_SearchPerfLarkChat_Handler,
		},
		{
			MethodName: "DeletePerfLarkChat",
			Handler:    _PerfLarkChatService_DeletePerfLarkChat_Handler,
		},
		{
			MethodName: "UpdatePerfLarkChat",
			Handler:    _PerfLarkChatService_UpdatePerfLarkChat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	PerfLarkMemberService_CreatePerfLarkMember_FullMethodName = "/manager.PerfLarkMemberService/CreatePerfLarkMember"
	PerfLarkMemberService_RemovePerfLarkMember_FullMethodName = "/manager.PerfLarkMemberService/RemovePerfLarkMember"
	PerfLarkMemberService_SearchPerfLarkMember_FullMethodName = "/manager.PerfLarkMemberService/SearchPerfLarkMember"
)

// PerfLarkMemberServiceClient is the client API for PerfLarkMemberService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PerfLarkMemberService 压测飞书自动拉群成员服务
type PerfLarkMemberServiceClient interface {
	//CreatePerfLarkMember 添加飞书自动拉群成员
	CreatePerfLarkMember(ctx context.Context, in *CreatePerfLarkMemberReq, opts ...grpc.CallOption) (*CreatePerfLarkMemberResp, error)
	//RemovePerfLarkMember 删除飞书自动拉群成员
	RemovePerfLarkMember(ctx context.Context, in *RemovePerfLarkMemberReq, opts ...grpc.CallOption) (*RemovePerfLarkMemberResp, error)
	//SearchPerfLarkMember 搜索飞书自动拉群成员
	SearchPerfLarkMember(ctx context.Context, in *SearchPerfLarkMemberReq, opts ...grpc.CallOption) (*SearchPerfLarkMemberResp, error)
}

type perfLarkMemberServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPerfLarkMemberServiceClient(cc grpc.ClientConnInterface) PerfLarkMemberServiceClient {
	return &perfLarkMemberServiceClient{cc}
}

func (c *perfLarkMemberServiceClient) CreatePerfLarkMember(ctx context.Context, in *CreatePerfLarkMemberReq, opts ...grpc.CallOption) (*CreatePerfLarkMemberResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePerfLarkMemberResp)
	err := c.cc.Invoke(ctx, PerfLarkMemberService_CreatePerfLarkMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfLarkMemberServiceClient) RemovePerfLarkMember(ctx context.Context, in *RemovePerfLarkMemberReq, opts ...grpc.CallOption) (*RemovePerfLarkMemberResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePerfLarkMemberResp)
	err := c.cc.Invoke(ctx, PerfLarkMemberService_RemovePerfLarkMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfLarkMemberServiceClient) SearchPerfLarkMember(ctx context.Context, in *SearchPerfLarkMemberReq, opts ...grpc.CallOption) (*SearchPerfLarkMemberResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPerfLarkMemberResp)
	err := c.cc.Invoke(ctx, PerfLarkMemberService_SearchPerfLarkMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfLarkMemberServiceServer is the server API for PerfLarkMemberService service.
// All implementations must embed UnimplementedPerfLarkMemberServiceServer
// for forward compatibility.
//
// PerfLarkMemberService 压测飞书自动拉群成员服务
type PerfLarkMemberServiceServer interface {
	//CreatePerfLarkMember 添加飞书自动拉群成员
	CreatePerfLarkMember(context.Context, *CreatePerfLarkMemberReq) (*CreatePerfLarkMemberResp, error)
	//RemovePerfLarkMember 删除飞书自动拉群成员
	RemovePerfLarkMember(context.Context, *RemovePerfLarkMemberReq) (*RemovePerfLarkMemberResp, error)
	//SearchPerfLarkMember 搜索飞书自动拉群成员
	SearchPerfLarkMember(context.Context, *SearchPerfLarkMemberReq) (*SearchPerfLarkMemberResp, error)
	mustEmbedUnimplementedPerfLarkMemberServiceServer()
}

// UnimplementedPerfLarkMemberServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPerfLarkMemberServiceServer struct{}

func (UnimplementedPerfLarkMemberServiceServer) CreatePerfLarkMember(context.Context, *CreatePerfLarkMemberReq) (*CreatePerfLarkMemberResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePerfLarkMember not implemented")
}
func (UnimplementedPerfLarkMemberServiceServer) RemovePerfLarkMember(context.Context, *RemovePerfLarkMemberReq) (*RemovePerfLarkMemberResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePerfLarkMember not implemented")
}
func (UnimplementedPerfLarkMemberServiceServer) SearchPerfLarkMember(context.Context, *SearchPerfLarkMemberReq) (*SearchPerfLarkMemberResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPerfLarkMember not implemented")
}
func (UnimplementedPerfLarkMemberServiceServer) mustEmbedUnimplementedPerfLarkMemberServiceServer() {}
func (UnimplementedPerfLarkMemberServiceServer) testEmbeddedByValue()                               {}

// UnsafePerfLarkMemberServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PerfLarkMemberServiceServer will
// result in compilation errors.
type UnsafePerfLarkMemberServiceServer interface {
	mustEmbedUnimplementedPerfLarkMemberServiceServer()
}

func RegisterPerfLarkMemberServiceServer(s grpc.ServiceRegistrar, srv PerfLarkMemberServiceServer) {
	// If the following call pancis, it indicates UnimplementedPerfLarkMemberServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PerfLarkMemberService_ServiceDesc, srv)
}

func _PerfLarkMemberService_CreatePerfLarkMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePerfLarkMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfLarkMemberServiceServer).CreatePerfLarkMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfLarkMemberService_CreatePerfLarkMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfLarkMemberServiceServer).CreatePerfLarkMember(ctx, req.(*CreatePerfLarkMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfLarkMemberService_RemovePerfLarkMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePerfLarkMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfLarkMemberServiceServer).RemovePerfLarkMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfLarkMemberService_RemovePerfLarkMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfLarkMemberServiceServer).RemovePerfLarkMember(ctx, req.(*RemovePerfLarkMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfLarkMemberService_SearchPerfLarkMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPerfLarkMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfLarkMemberServiceServer).SearchPerfLarkMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PerfLarkMemberService_SearchPerfLarkMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfLarkMemberServiceServer).SearchPerfLarkMember(ctx, req.(*SearchPerfLarkMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PerfLarkMemberService_ServiceDesc is the grpc.ServiceDesc for PerfLarkMemberService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PerfLarkMemberService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.PerfLarkMemberService",
	HandlerType: (*PerfLarkMemberServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePerfLarkMember",
			Handler:    _PerfLarkMemberService_CreatePerfLarkMember_Handler,
		},
		{
			MethodName: "RemovePerfLarkMember",
			Handler:    _PerfLarkMemberService_RemovePerfLarkMember_Handler,
		},
		{
			MethodName: "SearchPerfLarkMember",
			Handler:    _PerfLarkMemberService_SearchPerfLarkMember_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	ProjectDeviceService_ModifyProjectDevice_FullMethodName           = "/manager.ProjectDeviceService/ModifyProjectDevice"
	ProjectDeviceService_GetProjectDevice_FullMethodName              = "/manager.ProjectDeviceService/GetProjectDevice"
	ProjectDeviceService_SearchProjectDevice_FullMethodName           = "/manager.ProjectDeviceService/SearchProjectDevice"
	ProjectDeviceService_SearchUnassignedProjectDevice_FullMethodName = "/manager.ProjectDeviceService/SearchUnassignedProjectDevice"
	ProjectDeviceService_SearchProjectDeviceReference_FullMethodName  = "/manager.ProjectDeviceService/SearchProjectDeviceReference"
	ProjectDeviceService_AcquireProjectDevice_FullMethodName          = "/manager.ProjectDeviceService/AcquireProjectDevice"
	ProjectDeviceService_ReleaseProjectDevice_FullMethodName          = "/manager.ProjectDeviceService/ReleaseProjectDevice"
	ProjectDeviceService_DeleteDisabledProjectDevice_FullMethodName   = "/manager.ProjectDeviceService/DeleteDisabledProjectDevice"
)

// ProjectDeviceServiceClient is the client API for ProjectDeviceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ProjectDeviceService 项目设备服务
type ProjectDeviceServiceClient interface {
	//ModifyProjectDevice 编辑项目设备列表
	ModifyProjectDevice(ctx context.Context, in *ModifyProjectDeviceReq, opts ...grpc.CallOption) (*ModifyProjectDeviceResp, error)
	//GetProjectDevice 获取项目设备
	GetProjectDevice(ctx context.Context, in *GetProjectDeviceReq, opts ...grpc.CallOption) (*GetProjectDeviceResp, error)
	//SearchProjectDevice 搜索项目设备
	SearchProjectDevice(ctx context.Context, in *SearchProjectDeviceReq, opts ...grpc.CallOption) (*SearchProjectDeviceResp, error)
	//SearchUnassignedProjectDevice 搜索未分配到项目的设备
	SearchUnassignedProjectDevice(ctx context.Context, in *SearchUnassignedProjectDeviceReq, opts ...grpc.CallOption) (*SearchUnassignedProjectDeviceResp, error)
	//SearchProjectDeviceReference 搜索项目设备引用详情
	SearchProjectDeviceReference(ctx context.Context, in *SearchProjectDeviceReferenceReq, opts ...grpc.CallOption) (*SearchProjectDeviceReferenceResp, error)
	//AcquireProjectDevice 获取项目设备
	AcquireProjectDevice(ctx context.Context, in *AcquireProjectDeviceReq, opts ...grpc.CallOption) (*AcquireProjectDeviceResp, error)
	//ReleaseProjectDevice 释放项目设备
	ReleaseProjectDevice(ctx context.Context, in *ReleaseProjectDeviceReq, opts ...grpc.CallOption) (*ReleaseProjectDeviceResp, error)
	//DeleteDisabledProjectDevice 删除无效（设备中心不存在）的项目设备
	DeleteDisabledProjectDevice(ctx context.Context, in *DeleteDisabledProjectDeviceReq, opts ...grpc.CallOption) (*DeleteDisabledProjectDeviceResp, error)
}

type projectDeviceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewProjectDeviceServiceClient(cc grpc.ClientConnInterface) ProjectDeviceServiceClient {
	return &projectDeviceServiceClient{cc}
}

func (c *projectDeviceServiceClient) ModifyProjectDevice(ctx context.Context, in *ModifyProjectDeviceReq, opts ...grpc.CallOption) (*ModifyProjectDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyProjectDeviceResp)
	err := c.cc.Invoke(ctx, ProjectDeviceService_ModifyProjectDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectDeviceServiceClient) GetProjectDevice(ctx context.Context, in *GetProjectDeviceReq, opts ...grpc.CallOption) (*GetProjectDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProjectDeviceResp)
	err := c.cc.Invoke(ctx, ProjectDeviceService_GetProjectDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectDeviceServiceClient) SearchProjectDevice(ctx context.Context, in *SearchProjectDeviceReq, opts ...grpc.CallOption) (*SearchProjectDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchProjectDeviceResp)
	err := c.cc.Invoke(ctx, ProjectDeviceService_SearchProjectDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectDeviceServiceClient) SearchUnassignedProjectDevice(ctx context.Context, in *SearchUnassignedProjectDeviceReq, opts ...grpc.CallOption) (*SearchUnassignedProjectDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUnassignedProjectDeviceResp)
	err := c.cc.Invoke(ctx, ProjectDeviceService_SearchUnassignedProjectDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectDeviceServiceClient) SearchProjectDeviceReference(ctx context.Context, in *SearchProjectDeviceReferenceReq, opts ...grpc.CallOption) (*SearchProjectDeviceReferenceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchProjectDeviceReferenceResp)
	err := c.cc.Invoke(ctx, ProjectDeviceService_SearchProjectDeviceReference_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectDeviceServiceClient) AcquireProjectDevice(ctx context.Context, in *AcquireProjectDeviceReq, opts ...grpc.CallOption) (*AcquireProjectDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AcquireProjectDeviceResp)
	err := c.cc.Invoke(ctx, ProjectDeviceService_AcquireProjectDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectDeviceServiceClient) ReleaseProjectDevice(ctx context.Context, in *ReleaseProjectDeviceReq, opts ...grpc.CallOption) (*ReleaseProjectDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReleaseProjectDeviceResp)
	err := c.cc.Invoke(ctx, ProjectDeviceService_ReleaseProjectDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectDeviceServiceClient) DeleteDisabledProjectDevice(ctx context.Context, in *DeleteDisabledProjectDeviceReq, opts ...grpc.CallOption) (*DeleteDisabledProjectDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDisabledProjectDeviceResp)
	err := c.cc.Invoke(ctx, ProjectDeviceService_DeleteDisabledProjectDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProjectDeviceServiceServer is the server API for ProjectDeviceService service.
// All implementations must embed UnimplementedProjectDeviceServiceServer
// for forward compatibility.
//
// ProjectDeviceService 项目设备服务
type ProjectDeviceServiceServer interface {
	//ModifyProjectDevice 编辑项目设备列表
	ModifyProjectDevice(context.Context, *ModifyProjectDeviceReq) (*ModifyProjectDeviceResp, error)
	//GetProjectDevice 获取项目设备
	GetProjectDevice(context.Context, *GetProjectDeviceReq) (*GetProjectDeviceResp, error)
	//SearchProjectDevice 搜索项目设备
	SearchProjectDevice(context.Context, *SearchProjectDeviceReq) (*SearchProjectDeviceResp, error)
	//SearchUnassignedProjectDevice 搜索未分配到项目的设备
	SearchUnassignedProjectDevice(context.Context, *SearchUnassignedProjectDeviceReq) (*SearchUnassignedProjectDeviceResp, error)
	//SearchProjectDeviceReference 搜索项目设备引用详情
	SearchProjectDeviceReference(context.Context, *SearchProjectDeviceReferenceReq) (*SearchProjectDeviceReferenceResp, error)
	//AcquireProjectDevice 获取项目设备
	AcquireProjectDevice(context.Context, *AcquireProjectDeviceReq) (*AcquireProjectDeviceResp, error)
	//ReleaseProjectDevice 释放项目设备
	ReleaseProjectDevice(context.Context, *ReleaseProjectDeviceReq) (*ReleaseProjectDeviceResp, error)
	//DeleteDisabledProjectDevice 删除无效（设备中心不存在）的项目设备
	DeleteDisabledProjectDevice(context.Context, *DeleteDisabledProjectDeviceReq) (*DeleteDisabledProjectDeviceResp, error)
	mustEmbedUnimplementedProjectDeviceServiceServer()
}

// UnimplementedProjectDeviceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedProjectDeviceServiceServer struct{}

func (UnimplementedProjectDeviceServiceServer) ModifyProjectDevice(context.Context, *ModifyProjectDeviceReq) (*ModifyProjectDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyProjectDevice not implemented")
}
func (UnimplementedProjectDeviceServiceServer) GetProjectDevice(context.Context, *GetProjectDeviceReq) (*GetProjectDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectDevice not implemented")
}
func (UnimplementedProjectDeviceServiceServer) SearchProjectDevice(context.Context, *SearchProjectDeviceReq) (*SearchProjectDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchProjectDevice not implemented")
}
func (UnimplementedProjectDeviceServiceServer) SearchUnassignedProjectDevice(context.Context, *SearchUnassignedProjectDeviceReq) (*SearchUnassignedProjectDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUnassignedProjectDevice not implemented")
}
func (UnimplementedProjectDeviceServiceServer) SearchProjectDeviceReference(context.Context, *SearchProjectDeviceReferenceReq) (*SearchProjectDeviceReferenceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchProjectDeviceReference not implemented")
}
func (UnimplementedProjectDeviceServiceServer) AcquireProjectDevice(context.Context, *AcquireProjectDeviceReq) (*AcquireProjectDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcquireProjectDevice not implemented")
}
func (UnimplementedProjectDeviceServiceServer) ReleaseProjectDevice(context.Context, *ReleaseProjectDeviceReq) (*ReleaseProjectDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseProjectDevice not implemented")
}
func (UnimplementedProjectDeviceServiceServer) DeleteDisabledProjectDevice(context.Context, *DeleteDisabledProjectDeviceReq) (*DeleteDisabledProjectDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDisabledProjectDevice not implemented")
}
func (UnimplementedProjectDeviceServiceServer) mustEmbedUnimplementedProjectDeviceServiceServer() {}
func (UnimplementedProjectDeviceServiceServer) testEmbeddedByValue()                              {}

// UnsafeProjectDeviceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProjectDeviceServiceServer will
// result in compilation errors.
type UnsafeProjectDeviceServiceServer interface {
	mustEmbedUnimplementedProjectDeviceServiceServer()
}

func RegisterProjectDeviceServiceServer(s grpc.ServiceRegistrar, srv ProjectDeviceServiceServer) {
	// If the following call pancis, it indicates UnimplementedProjectDeviceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ProjectDeviceService_ServiceDesc, srv)
}

func _ProjectDeviceService_ModifyProjectDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyProjectDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectDeviceServiceServer).ModifyProjectDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectDeviceService_ModifyProjectDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectDeviceServiceServer).ModifyProjectDevice(ctx, req.(*ModifyProjectDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectDeviceService_GetProjectDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProjectDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectDeviceServiceServer).GetProjectDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectDeviceService_GetProjectDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectDeviceServiceServer).GetProjectDevice(ctx, req.(*GetProjectDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectDeviceService_SearchProjectDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchProjectDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectDeviceServiceServer).SearchProjectDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectDeviceService_SearchProjectDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectDeviceServiceServer).SearchProjectDevice(ctx, req.(*SearchProjectDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectDeviceService_SearchUnassignedProjectDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUnassignedProjectDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectDeviceServiceServer).SearchUnassignedProjectDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectDeviceService_SearchUnassignedProjectDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectDeviceServiceServer).SearchUnassignedProjectDevice(ctx, req.(*SearchUnassignedProjectDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectDeviceService_SearchProjectDeviceReference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchProjectDeviceReferenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectDeviceServiceServer).SearchProjectDeviceReference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectDeviceService_SearchProjectDeviceReference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectDeviceServiceServer).SearchProjectDeviceReference(ctx, req.(*SearchProjectDeviceReferenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectDeviceService_AcquireProjectDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcquireProjectDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectDeviceServiceServer).AcquireProjectDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectDeviceService_AcquireProjectDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectDeviceServiceServer).AcquireProjectDevice(ctx, req.(*AcquireProjectDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectDeviceService_ReleaseProjectDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseProjectDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectDeviceServiceServer).ReleaseProjectDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectDeviceService_ReleaseProjectDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectDeviceServiceServer).ReleaseProjectDevice(ctx, req.(*ReleaseProjectDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectDeviceService_DeleteDisabledProjectDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDisabledProjectDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectDeviceServiceServer).DeleteDisabledProjectDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectDeviceService_DeleteDisabledProjectDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectDeviceServiceServer).DeleteDisabledProjectDevice(ctx, req.(*DeleteDisabledProjectDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ProjectDeviceService_ServiceDesc is the grpc.ServiceDesc for ProjectDeviceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProjectDeviceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ProjectDeviceService",
	HandlerType: (*ProjectDeviceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ModifyProjectDevice",
			Handler:    _ProjectDeviceService_ModifyProjectDevice_Handler,
		},
		{
			MethodName: "GetProjectDevice",
			Handler:    _ProjectDeviceService_GetProjectDevice_Handler,
		},
		{
			MethodName: "SearchProjectDevice",
			Handler:    _ProjectDeviceService_SearchProjectDevice_Handler,
		},
		{
			MethodName: "SearchUnassignedProjectDevice",
			Handler:    _ProjectDeviceService_SearchUnassignedProjectDevice_Handler,
		},
		{
			MethodName: "SearchProjectDeviceReference",
			Handler:    _ProjectDeviceService_SearchProjectDeviceReference_Handler,
		},
		{
			MethodName: "AcquireProjectDevice",
			Handler:    _ProjectDeviceService_AcquireProjectDevice_Handler,
		},
		{
			MethodName: "ReleaseProjectDevice",
			Handler:    _ProjectDeviceService_ReleaseProjectDevice_Handler,
		},
		{
			MethodName: "DeleteDisabledProjectDevice",
			Handler:    _ProjectDeviceService_DeleteDisabledProjectDevice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	StabilityPlanService_CreateStabilityPlan_FullMethodName = "/manager.StabilityPlanService/CreateStabilityPlan"
	StabilityPlanService_RemoveStabilityPlan_FullMethodName = "/manager.StabilityPlanService/RemoveStabilityPlan"
	StabilityPlanService_ModifyStabilityPlan_FullMethodName = "/manager.StabilityPlanService/ModifyStabilityPlan"
	StabilityPlanService_SearchStabilityPlan_FullMethodName = "/manager.StabilityPlanService/SearchStabilityPlan"
	StabilityPlanService_ViewStabilityPlan_FullMethodName   = "/manager.StabilityPlanService/ViewStabilityPlan"
)

// StabilityPlanServiceClient is the client API for StabilityPlanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// StabilityPlanService 稳定性测试计划服务
type StabilityPlanServiceClient interface {
	//CreateStabilityPlan 创建稳定性测试计划
	CreateStabilityPlan(ctx context.Context, in *CreateStabilityPlanReq, opts ...grpc.CallOption) (*CreateStabilityPlanResp, error)
	//RemoveStabilityPlan 删除稳定性测试计划
	RemoveStabilityPlan(ctx context.Context, in *RemoveStabilityPlanReq, opts ...grpc.CallOption) (*RemoveStabilityPlanResp, error)
	//ModifyStabilityPlan 编辑稳定性测试计划
	ModifyStabilityPlan(ctx context.Context, in *ModifyStabilityPlanReq, opts ...grpc.CallOption) (*ModifyStabilityPlanResp, error)
	//SearchStabilityPlan 搜索稳定性测试计划
	SearchStabilityPlan(ctx context.Context, in *SearchStabilityPlanReq, opts ...grpc.CallOption) (*SearchStabilityPlanResp, error)
	//ViewStabilityPlan   查看稳定性测试计划
	ViewStabilityPlan(ctx context.Context, in *ViewStabilityPlanReq, opts ...grpc.CallOption) (*ViewStabilityPlanResp, error)
}

type stabilityPlanServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStabilityPlanServiceClient(cc grpc.ClientConnInterface) StabilityPlanServiceClient {
	return &stabilityPlanServiceClient{cc}
}

func (c *stabilityPlanServiceClient) CreateStabilityPlan(ctx context.Context, in *CreateStabilityPlanReq, opts ...grpc.CallOption) (*CreateStabilityPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateStabilityPlanResp)
	err := c.cc.Invoke(ctx, StabilityPlanService_CreateStabilityPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityPlanServiceClient) RemoveStabilityPlan(ctx context.Context, in *RemoveStabilityPlanReq, opts ...grpc.CallOption) (*RemoveStabilityPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveStabilityPlanResp)
	err := c.cc.Invoke(ctx, StabilityPlanService_RemoveStabilityPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityPlanServiceClient) ModifyStabilityPlan(ctx context.Context, in *ModifyStabilityPlanReq, opts ...grpc.CallOption) (*ModifyStabilityPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyStabilityPlanResp)
	err := c.cc.Invoke(ctx, StabilityPlanService_ModifyStabilityPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityPlanServiceClient) SearchStabilityPlan(ctx context.Context, in *SearchStabilityPlanReq, opts ...grpc.CallOption) (*SearchStabilityPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchStabilityPlanResp)
	err := c.cc.Invoke(ctx, StabilityPlanService_SearchStabilityPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stabilityPlanServiceClient) ViewStabilityPlan(ctx context.Context, in *ViewStabilityPlanReq, opts ...grpc.CallOption) (*ViewStabilityPlanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewStabilityPlanResp)
	err := c.cc.Invoke(ctx, StabilityPlanService_ViewStabilityPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StabilityPlanServiceServer is the server API for StabilityPlanService service.
// All implementations must embed UnimplementedStabilityPlanServiceServer
// for forward compatibility.
//
// StabilityPlanService 稳定性测试计划服务
type StabilityPlanServiceServer interface {
	//CreateStabilityPlan 创建稳定性测试计划
	CreateStabilityPlan(context.Context, *CreateStabilityPlanReq) (*CreateStabilityPlanResp, error)
	//RemoveStabilityPlan 删除稳定性测试计划
	RemoveStabilityPlan(context.Context, *RemoveStabilityPlanReq) (*RemoveStabilityPlanResp, error)
	//ModifyStabilityPlan 编辑稳定性测试计划
	ModifyStabilityPlan(context.Context, *ModifyStabilityPlanReq) (*ModifyStabilityPlanResp, error)
	//SearchStabilityPlan 搜索稳定性测试计划
	SearchStabilityPlan(context.Context, *SearchStabilityPlanReq) (*SearchStabilityPlanResp, error)
	//ViewStabilityPlan   查看稳定性测试计划
	ViewStabilityPlan(context.Context, *ViewStabilityPlanReq) (*ViewStabilityPlanResp, error)
	mustEmbedUnimplementedStabilityPlanServiceServer()
}

// UnimplementedStabilityPlanServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedStabilityPlanServiceServer struct{}

func (UnimplementedStabilityPlanServiceServer) CreateStabilityPlan(context.Context, *CreateStabilityPlanReq) (*CreateStabilityPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStabilityPlan not implemented")
}
func (UnimplementedStabilityPlanServiceServer) RemoveStabilityPlan(context.Context, *RemoveStabilityPlanReq) (*RemoveStabilityPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveStabilityPlan not implemented")
}
func (UnimplementedStabilityPlanServiceServer) ModifyStabilityPlan(context.Context, *ModifyStabilityPlanReq) (*ModifyStabilityPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyStabilityPlan not implemented")
}
func (UnimplementedStabilityPlanServiceServer) SearchStabilityPlan(context.Context, *SearchStabilityPlanReq) (*SearchStabilityPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchStabilityPlan not implemented")
}
func (UnimplementedStabilityPlanServiceServer) ViewStabilityPlan(context.Context, *ViewStabilityPlanReq) (*ViewStabilityPlanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewStabilityPlan not implemented")
}
func (UnimplementedStabilityPlanServiceServer) mustEmbedUnimplementedStabilityPlanServiceServer() {}
func (UnimplementedStabilityPlanServiceServer) testEmbeddedByValue()                              {}

// UnsafeStabilityPlanServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StabilityPlanServiceServer will
// result in compilation errors.
type UnsafeStabilityPlanServiceServer interface {
	mustEmbedUnimplementedStabilityPlanServiceServer()
}

func RegisterStabilityPlanServiceServer(s grpc.ServiceRegistrar, srv StabilityPlanServiceServer) {
	// If the following call pancis, it indicates UnimplementedStabilityPlanServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&StabilityPlanService_ServiceDesc, srv)
}

func _StabilityPlanService_CreateStabilityPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStabilityPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityPlanServiceServer).CreateStabilityPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityPlanService_CreateStabilityPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityPlanServiceServer).CreateStabilityPlan(ctx, req.(*CreateStabilityPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityPlanService_RemoveStabilityPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveStabilityPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityPlanServiceServer).RemoveStabilityPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityPlanService_RemoveStabilityPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityPlanServiceServer).RemoveStabilityPlan(ctx, req.(*RemoveStabilityPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityPlanService_ModifyStabilityPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyStabilityPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityPlanServiceServer).ModifyStabilityPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityPlanService_ModifyStabilityPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityPlanServiceServer).ModifyStabilityPlan(ctx, req.(*ModifyStabilityPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityPlanService_SearchStabilityPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchStabilityPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityPlanServiceServer).SearchStabilityPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityPlanService_SearchStabilityPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityPlanServiceServer).SearchStabilityPlan(ctx, req.(*SearchStabilityPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StabilityPlanService_ViewStabilityPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewStabilityPlanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StabilityPlanServiceServer).ViewStabilityPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StabilityPlanService_ViewStabilityPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StabilityPlanServiceServer).ViewStabilityPlan(ctx, req.(*ViewStabilityPlanReq))
	}
	return interceptor(ctx, in, info, handler)
}

// StabilityPlanService_ServiceDesc is the grpc.ServiceDesc for StabilityPlanService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StabilityPlanService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.StabilityPlanService",
	HandlerType: (*StabilityPlanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateStabilityPlan",
			Handler:    _StabilityPlanService_CreateStabilityPlan_Handler,
		},
		{
			MethodName: "RemoveStabilityPlan",
			Handler:    _StabilityPlanService_RemoveStabilityPlan_Handler,
		},
		{
			MethodName: "ModifyStabilityPlan",
			Handler:    _StabilityPlanService_ModifyStabilityPlan_Handler,
		},
		{
			MethodName: "SearchStabilityPlan",
			Handler:    _StabilityPlanService_SearchStabilityPlan_Handler,
		},
		{
			MethodName: "ViewStabilityPlan",
			Handler:    _StabilityPlanService_ViewStabilityPlan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}

const (
	LarkChatService_ModifyLarkChat_FullMethodName = "/manager.LarkChatService/ModifyLarkChat"
	LarkChatService_SearchLarkChat_FullMethodName = "/manager.LarkChatService/SearchLarkChat"
	LarkChatService_DeleteLarkChat_FullMethodName = "/manager.LarkChatService/DeleteLarkChat"
	LarkChatService_UpdateLarkChat_FullMethodName = "/manager.LarkChatService/UpdateLarkChat"
)

// LarkChatServiceClient is the client API for LarkChatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// LarkChatService 测试通知飞书群组服务
type LarkChatServiceClient interface {
	//ModifyLarkChat 编辑测试通知飞书群组列表
	ModifyLarkChat(ctx context.Context, in *ModifyLarkChatReq, opts ...grpc.CallOption) (*ModifyLarkChatResp, error)
	//SearchLarkChat 搜索测试通知飞书群组
	SearchLarkChat(ctx context.Context, in *SearchLarkChatReq, opts ...grpc.CallOption) (*SearchLarkChatResp, error)
	//DeleteLarkChat 删除测试通知飞书群组（由飞书群解散事件触发）
	DeleteLarkChat(ctx context.Context, in *DeleteLarkChatReq, opts ...grpc.CallOption) (*DeleteLarkChatResp, error)
	//UpdateLarkChat 更新测试通知飞书群组（由飞书群配置修改事件触发）
	UpdateLarkChat(ctx context.Context, in *UpdateLarkChatReq, opts ...grpc.CallOption) (*UpdateLarkChatResp, error)
}

type larkChatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLarkChatServiceClient(cc grpc.ClientConnInterface) LarkChatServiceClient {
	return &larkChatServiceClient{cc}
}

func (c *larkChatServiceClient) ModifyLarkChat(ctx context.Context, in *ModifyLarkChatReq, opts ...grpc.CallOption) (*ModifyLarkChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyLarkChatResp)
	err := c.cc.Invoke(ctx, LarkChatService_ModifyLarkChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *larkChatServiceClient) SearchLarkChat(ctx context.Context, in *SearchLarkChatReq, opts ...grpc.CallOption) (*SearchLarkChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchLarkChatResp)
	err := c.cc.Invoke(ctx, LarkChatService_SearchLarkChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *larkChatServiceClient) DeleteLarkChat(ctx context.Context, in *DeleteLarkChatReq, opts ...grpc.CallOption) (*DeleteLarkChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteLarkChatResp)
	err := c.cc.Invoke(ctx, LarkChatService_DeleteLarkChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *larkChatServiceClient) UpdateLarkChat(ctx context.Context, in *UpdateLarkChatReq, opts ...grpc.CallOption) (*UpdateLarkChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateLarkChatResp)
	err := c.cc.Invoke(ctx, LarkChatService_UpdateLarkChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LarkChatServiceServer is the server API for LarkChatService service.
// All implementations must embed UnimplementedLarkChatServiceServer
// for forward compatibility.
//
// LarkChatService 测试通知飞书群组服务
type LarkChatServiceServer interface {
	//ModifyLarkChat 编辑测试通知飞书群组列表
	ModifyLarkChat(context.Context, *ModifyLarkChatReq) (*ModifyLarkChatResp, error)
	//SearchLarkChat 搜索测试通知飞书群组
	SearchLarkChat(context.Context, *SearchLarkChatReq) (*SearchLarkChatResp, error)
	//DeleteLarkChat 删除测试通知飞书群组（由飞书群解散事件触发）
	DeleteLarkChat(context.Context, *DeleteLarkChatReq) (*DeleteLarkChatResp, error)
	//UpdateLarkChat 更新测试通知飞书群组（由飞书群配置修改事件触发）
	UpdateLarkChat(context.Context, *UpdateLarkChatReq) (*UpdateLarkChatResp, error)
	mustEmbedUnimplementedLarkChatServiceServer()
}

// UnimplementedLarkChatServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLarkChatServiceServer struct{}

func (UnimplementedLarkChatServiceServer) ModifyLarkChat(context.Context, *ModifyLarkChatReq) (*ModifyLarkChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyLarkChat not implemented")
}
func (UnimplementedLarkChatServiceServer) SearchLarkChat(context.Context, *SearchLarkChatReq) (*SearchLarkChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchLarkChat not implemented")
}
func (UnimplementedLarkChatServiceServer) DeleteLarkChat(context.Context, *DeleteLarkChatReq) (*DeleteLarkChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLarkChat not implemented")
}
func (UnimplementedLarkChatServiceServer) UpdateLarkChat(context.Context, *UpdateLarkChatReq) (*UpdateLarkChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLarkChat not implemented")
}
func (UnimplementedLarkChatServiceServer) mustEmbedUnimplementedLarkChatServiceServer() {}
func (UnimplementedLarkChatServiceServer) testEmbeddedByValue()                         {}

// UnsafeLarkChatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LarkChatServiceServer will
// result in compilation errors.
type UnsafeLarkChatServiceServer interface {
	mustEmbedUnimplementedLarkChatServiceServer()
}

func RegisterLarkChatServiceServer(s grpc.ServiceRegistrar, srv LarkChatServiceServer) {
	// If the following call pancis, it indicates UnimplementedLarkChatServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LarkChatService_ServiceDesc, srv)
}

func _LarkChatService_ModifyLarkChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyLarkChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkChatServiceServer).ModifyLarkChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkChatService_ModifyLarkChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkChatServiceServer).ModifyLarkChat(ctx, req.(*ModifyLarkChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LarkChatService_SearchLarkChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchLarkChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkChatServiceServer).SearchLarkChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkChatService_SearchLarkChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkChatServiceServer).SearchLarkChat(ctx, req.(*SearchLarkChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LarkChatService_DeleteLarkChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLarkChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkChatServiceServer).DeleteLarkChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkChatService_DeleteLarkChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkChatServiceServer).DeleteLarkChat(ctx, req.(*DeleteLarkChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LarkChatService_UpdateLarkChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLarkChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkChatServiceServer).UpdateLarkChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkChatService_UpdateLarkChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkChatServiceServer).UpdateLarkChat(ctx, req.(*UpdateLarkChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LarkChatService_ServiceDesc is the grpc.ServiceDesc for LarkChatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LarkChatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.LarkChatService",
	HandlerType: (*LarkChatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ModifyLarkChat",
			Handler:    _LarkChatService_ModifyLarkChat_Handler,
		},
		{
			MethodName: "SearchLarkChat",
			Handler:    _LarkChatService_SearchLarkChat_Handler,
		},
		{
			MethodName: "DeleteLarkChat",
			Handler:    _LarkChatService_DeleteLarkChat_Handler,
		},
		{
			MethodName: "UpdateLarkChat",
			Handler:    _LarkChatService_UpdateLarkChat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "manager/manager.proto",
}
