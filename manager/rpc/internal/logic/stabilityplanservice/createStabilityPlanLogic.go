package stabilityplanservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateStabilityPlanLogic struct {
	*BaseLogic
}

func NewCreateStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateStabilityPlanLogic {
	return &CreateStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateStabilityPlan 创建稳定性测试计划
func (l *CreateStabilityPlanLogic) CreateStabilityPlan(in *pb.CreateStabilityPlanReq) (out *pb.CreateStabilityPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeStabilityPlan, in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the type of parent category[%s] does not support creation of sub category",
				c.CategoryType,
			),
		)
	}

	// validate the account_config_id in req
	if in.GetAccountConfigId() != "" {
		if _, err = model.CheckAccountConfigByConfigId(
			l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), in.GetAccountConfigId(),
		); err != nil {
			return nil, err
		}
	}

	stabilityPlan, err := l.create(in)
	if err != nil {
		return nil, err
	}

	out = &pb.CreateStabilityPlanResp{Plan: &pb.StabilityPlan{}}
	if err = utils.Copy(out.Plan, stabilityPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to response, stability plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(stabilityPlan), err,
		)
	}

	return out, nil
}

func (l *CreateStabilityPlanLogic) create(req *pb.CreateStabilityPlanReq) (*model.StabilityPlan, error) {
	var (
		projectID       = req.GetProjectId()
		description     = req.GetDescription()
		cronExpression  = req.GetCronExpression()
		accountConfigID = req.GetAccountConfigId()
		maintainedBy    = req.GetMaintainedBy()

		tags, activities sql.NullString
	)

	planID, err := l.generatePlanID(projectID)
	if err != nil {
		return nil, err
	}

	if len(req.GetTags()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		tags.String = jsonx.MarshalToStringIgnoreError(req.GetTags())
		tags.Valid = true
	}

	if len(req.GetActivities()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		activities.String = jsonx.MarshalToStringIgnoreError(req.GetActivities())
		activities.Valid = true
	}

	if maintainedBy != "" {
		var user *userpb.UserInfo
		if user, err = l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of stability plan, project_id: %s, maintained_by: %s",
				projectID, maintainedBy,
			)
		}
	} else {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	// validate the cron_expression in req if the plan type is SCHEDULE
	if req.GetType() == commonpb.TriggerMode_SCHEDULE {
		_, err := cronexpr.Parse(req.GetCronExpression())
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CronExpressionParseError, err.Error()),
				"failed to parse the cron expression, cron: %s, error: %+v",
				req.GetCronExpression(), err,
			)
		}
	}

	now := time.Now()
	stabilityPlan := &model.StabilityPlan{
		ProjectId:  projectID,
		CategoryId: req.GetCategoryId(),
		PlanId:     planID,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: description,
			Valid:  description != "",
		},
		State:        int64(req.GetState()),
		Type:         protobuf.GetEnumStringOf(req.GetType()),
		PriorityType: int64(req.GetPriorityType()),
		CronExpression: sql.NullString{
			String: cronExpression,
			Valid:  cronExpression != "",
		},
		Tags: tags,
		AccountConfigId: sql.NullString{
			String: accountConfigID,
			Valid:  accountConfigID != "",
		},
		DeviceType:   int64(req.GetDeviceType()),
		PlatformType: int64(req.GetPlatformType()),
		Devices: sql.NullString{
			String: protobuf.MarshalJSONToStringIgnoreError(req.GetDevices()),
			Valid:  true,
		},
		PackageName:     req.GetPackageName(),
		AppDownloadLink: req.GetAppDownloadLink(),
		Duration:        int64(req.GetDuration()),
		Activities:      activities,
		CustomScript: sql.NullString{
			String: protobuf.MarshalJSONToStringIgnoreError(req.GetCustomScript()),
			Valid:  true,
		},
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}

	if err = l.svcCtx.StabilityPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// create the stability plan
			if _, err := l.svcCtx.StabilityPlanModel.Insert(context, session, stabilityPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.StabilityPlanModel.Table(), jsonx.MarshalIgnoreError(stabilityPlan), err,
				)
			}

			// create the new tag and tag reference of perf plan
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     stabilityPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypeStabilityPlan,
					ReferenceId:   stabilityPlan.PlanId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			// create the project and devices reference of stability plan
			if err := l.updateProjectDeviceRelationship(context, session, stabilityPlan, req.GetDevices()); err != nil {
				return err
			}

			// create notify item of stability plan
			if err := l.updateNotifyItems(context, session, stabilityPlan, req.GetLarkChats()); err != nil {
				return err
			}

			return l.createScheduleTask(req, stabilityPlan)
		},
	); err != nil {
		return nil, err
	}

	return stabilityPlan, nil
}
