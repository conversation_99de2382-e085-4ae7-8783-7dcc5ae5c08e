package apiexecutionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetApiExecutionDataLogic struct {
	*BaseLogic
}

func NewGetApiExecutionDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetApiExecutionDataLogic {
	return &GetApiExecutionDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetApiExecutionData 获取API执行数据
func (l *GetApiExecutionDataLogic) GetApiExecutionData(in *pb.GetApiExecutionDataReq) (
	resp *pb.ApiExecutionData, err error,
) {
	// validate the project_id
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	switch in.GetType() {
	case pb.ApiExecutionDataType_API_COMPONENT_GROUP: // API组件组
		return l.getComponentGroupExecutionData(in.GetProjectId(), in.GetId(), in.GetVersion())
	case pb.ApiExecutionDataType_API_CASE: // API用例
		return l.getApiCaseExecutionData(in.GetProjectId(), in.GetId(), in.GetVersion())
	case pb.ApiExecutionDataType_INTERFACE_CASE: // 接口用例
		return l.getInterfaceCaseExecutionData(in.GetProjectId(), in.GetId(), in.GetVersion())
	case pb.ApiExecutionDataType_UI_PLAN: // UI计划
		return l.getUIPlanExecutionData(in.GetProjectId(), in.GetId(), in.GetUiPlanExtraData())
	case pb.ApiExecutionDataType_PERF_PLAN: // 压测计划
		// return l.getPerfPlanExecutionData(in.GetProjectId(), in.GetId())
		return l.getPerfPlanV2ExecutionData(in.GetProjectId(), in.GetId())
	case pb.ApiExecutionDataType_STABILITY_PLAN: // 稳测计划
		return l.getStabilityPlanExecutionData(in.GetProjectId(), in.GetId())
	}

	return nil, errors.WithStack(
		errorx.Errorf(
			codes.GenerateApiExecutionDataFailure,
			"the type of api execution data is not currently supported, project_id: %s, type: %s, type_id: %s",
			in.GetProjectId(), in.GetType(), in.GetId(),
		),
	)
}
