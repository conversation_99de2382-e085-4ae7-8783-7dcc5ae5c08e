package projectdeviceservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetProjectDeviceLogic struct {
	*BaseLogic
}

func NewGetProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProjectDeviceLogic {
	return &GetProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetProjectDevice 获取项目设备
func (l *GetProjectDeviceLogic) GetProjectDevice(in *pb.GetProjectDeviceReq) (out *pb.GetProjectDeviceResp, err error) {
	var (
		projectID = in.GetProjectId()
		usage     = in.GetUsage()
		udid      = in.GetUdid()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	_, projectDevices, err := l.getProjectDevices(projectID, usage)
	if err != nil {
		return nil, err
	}

	if _, ok := projectDevices.Get(udid); !ok {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"the project device doesn't exist, project_id: %s, udid: %s",
			projectID, udid,
		)
	}

	device, err := l.svcCtx.DeviceHubRPC.GetDevice(l.ctx, &devicehubpb.GetDeviceReq{Udid: udid})
	if err != nil {
		return nil, err
	}

	return &pb.GetProjectDeviceResp{
		Device: &pb.ProjectDevice{
			Device: device.GetDevice(),

			ProjectId: projectID,
			Usage:     usage,
		},
	}, nil
}
