package uitest

import (
	"context"
	"database/sql"
	"fmt"
	"math/rand"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/electricbubble/gadb"
	gid "github.com/electricbubble/gidevice"
	"github.com/electricbubble/gidevice/pkg/ipa"
	"github.com/hibiken/asynq"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/zrpc"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/collector"
	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/svc"
)

const (
	pythonEnvPrefix = "python_env"

	basePathOnAndroid = "/data/local/tmp"

	usbMuxPort = 27015

	retryTimesOfADBConnect = 3

	timeoutOfWaitForDevice      = 30 * time.Minute
	delayTimeOfADBConnect       = 2 * time.Second
	timeoutOfInstallApp         = 5 * time.Minute
	intervalOfCheckSubTask      = 5 * time.Second
	expireTimeOfSubTask         = 60 * 60 * 12
	delayTimeOfAcquireRedisLock = 10 * time.Second
)

var manualConfirmationTextList = []string{"继续安装", "确定", "完成"}

type ExecuteBaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	logger     *common.StepLogger
	stopCh     <-chan lang.PlaceholderType
	converters []utils.TypeConverter

	taskInfo        *dispatcherpb.UITestTaskInfo
	shortTaskID     string
	basePath        string                      // 根目录
	scriptPath      string                      // 基础脚本路径（python使用虚拟环境）
	frameworkPath   string                      // 测试框架项目路径
	packageSavePath string                      // 待测试App包存放目录路径
	packageName     string                      // 待测试App包名称
	packagePath     string                      // 待测试App包存放路径
	appName         string                      // 待测试App名称（Android: package_name, iOS: bundle_id）
	devices         []*devicehubpb.Device       // 占用的设备列表
	state           dispatcherpb.ComponentState // 执行状态
	uuid            string                      // 任务UUID
	priorityType    commonpb.PriorityType       // 优先级
	c               collector.Collector         // 设备性能数据采集器

	confirmationTexts []string
}

func NewExecuteBaseLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
	stepLogger *common.StepLogger, taskInfo *dispatcherpb.UITestTaskInfo, stopCh <-chan lang.PlaceholderType,
) *ExecuteBaseLogic {
	l := &ExecuteBaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		logger:     stepLogger,
		stopCh:     stopCh,
		converters: []utils.TypeConverter{},

		taskInfo: taskInfo,
	}

	l.init()
	return l
}

func (l *ExecuteBaseLogic) init() {
	// set the short task id
	taskID := l.taskInfo.GetTaskId()
	if ss := strings.Split(taskID, ":"); len(ss) >= 2 {
		taskID = ss[1]
	}
	l.shortTaskID = taskID

	// set uuid
	var ok bool
	l.uuid, ok = asynq.GetTaskID(l.ctx)
	if !ok {
		l.Errorf("failed to get the task_id of asynq from context, task_id: %s", l.taskInfo.GetTaskId())
	}

	// set the base path
	l.basePath = l.svcCtx.Config.PVCPath

	// set the framework path
	l.frameworkPath = filepath.Join(l.basePath, common.ConstUITestFrameworkFolderName, l.shortTaskID)

	// set the package save path
	l.packageSavePath = filepath.Join(l.basePath, common.ConstAppDownloadFolderName)

	// set the package name
	l.setPackageName()

	// set the package path
	l.packagePath = filepath.Join(l.packageSavePath, l.packageName)

	// set manual confirmation texts
	texts := manualConfirmationTextList
	if len(l.svcCtx.Config.ManualConfirmationTexts) > 0 {
		texts = l.svcCtx.Config.ManualConfirmationTexts
	}
	l.confirmationTexts = texts
}

func (l *ExecuteBaseLogic) setPackageName() {
	var suffix string
	switch l.taskInfo.GetPlatformType() {
	case commonpb.PlatformType_ANDROID:
		suffix = common.ConstSuffixOfApk
	case commonpb.PlatformType_IOS:
		suffix = common.ConstSuffixOfIpa
	default:
		l.Warnf(
			"invalid platform type, task_id: %q, target: %q, platform_type: %q",
			l.taskInfo.GetTaskId(), l.taskInfo.GetTestTarget(), l.taskInfo.GetPlatformType().String(),
		)
	}

	l.packageName = fmt.Sprintf("%s%s", l.shortTaskID, suffix)
}

func (l *ExecuteBaseLogic) SetState(State dispatcherpb.ComponentState) {
	l.state = State
}

func (l *ExecuteBaseLogic) getRedisKeyStatus(key string) (err error, ok bool) {
	status, err := l.svcCtx.Redis.GetCtx(l.ctx, key)
	if err != nil {
		return errors.Errorf(
			"failed to get redis key status, task_id: %s, target: %s, key: %s, error: %+v",
			l.taskInfo.GetTaskId(), l.taskInfo.GetTestTarget(), key, err,
		), false
	}

	switch status {
	case commonconsts.SUCCESS:
		return nil, true
	case commonconsts.FAILURE:
		return errors.Errorf(
			"got a failed status of reids key, task_id: %s, target: %s, key: %s, status: %s",
			l.taskInfo.GetTaskId(), l.taskInfo.GetTestTarget(), key, status,
		), true
	default:
		return nil, false
	}
}

func (l *ExecuteBaseLogic) setRedisKeyStatus(key string, status commonconsts.TaskResult) error {
	if err := l.svcCtx.Redis.SetexCtx(l.ctx, key, status, expireTimeOfSubTask); err != nil {
		return errors.Errorf(
			"failed to set redis key status, task_id: %s, target: %s, key: %s, status: %s, error: %+v",
			l.taskInfo.GetTaskId(), l.taskInfo.GetTestTarget(), key, status, err,
		)
	}

	return nil
}

// ExecuteSubTaskOnce 只需要完成一次的前置任务，目前使用轮询
func (l *ExecuteBaseLogic) ExecuteSubTaskOnce(
	subTaskID string, timeout time.Duration, subTaskFunc func(ctx context.Context) error,
) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		lockKey   = fmt.Sprintf("lock::%s::%s", subTaskID, taskID)
		statusKey = fmt.Sprintf("status::%s::%s", subTaskID, taskID)

		ctx    context.Context
		cancel context.CancelFunc
	)

	l.Infof("start to execute the sub task, task_id: %s, target: %s, sub_task: %q", taskID, target, subTaskID)
	defer func() {
		l.Infof("finish to execute the sub task, task_id: %s, target: %s, sub_task:  %q", taskID, target, subTaskID)
	}()

	if timeout != 0 {
		ctx, cancel = commonutils.NewTimeoutContext(l.ctx, timeout)
		defer cancel()
	} else {
		ctx = l.ctx
	}

	err, ok := l.getRedisKeyStatus(statusKey)
	if err != nil {
		return err
	} else if ok {
		return nil
	}

	// acquire redis lock
	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, lockKey, redislock.WithExpire(timeout+delayTimeOfAcquireRedisLock),
	)
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			l.Errorf("failed to acquire the redis lock with key[%s], error: %+v", lockKey, err)
			return err
		}

		return l.waitingForSubTask(ctx, subTaskID)
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.executingForSubTask(ctx, subTaskID, subTaskFunc)
}

func (l *ExecuteBaseLogic) executingForSubTask(
	ctx context.Context, subTaskID string, subTaskFunc func(ctx context.Context) error,
) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		errCh = make(chan error, 1)

		subTaskStatus = commonconsts.FAILURE
		statusKey     = fmt.Sprintf("status::%s::%s", subTaskID, taskID)
	)

	threading.GoSafe(
		func() {
			errCh <- subTaskFunc(ctx)
		},
	)

	select {
	case <-ctx.Done():
		err = errors.Errorf(
			"executing sub task timed out, task_id: %s, target: %s, sub_task: %q, error: %+v",
			taskID, target, subTaskID, ctx.Err(),
		)
	case <-l.stopCh:
		err = errors.Errorf(
			"got a stop signal while executing sub task, task_id: %s, target: %s, sub_task: %q",
			taskID, target, subTaskID,
		)
	case err = <-errCh:
	}

	if err == nil {
		subTaskStatus = commonconsts.SUCCESS
	}
	err1 := l.setRedisKeyStatus(statusKey, subTaskStatus)
	if err != nil {
		return err
	} else if err1 != nil {
		return err1
	}

	return nil
}

func (l *ExecuteBaseLogic) waitingForSubTask(ctx context.Context, subTaskID string) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		errCh = make(chan error, 1)

		statusKey = fmt.Sprintf("status::%s::%s", subTaskID, taskID)
	)

	l.logger.Infof(
		"another worker is executing sub task, wait for the execution result, task_id: %s, target: %s, sub_task: %q",
		taskID, target, subTaskID,
	)

	threading.GoSafe(
		func() {
			var (
				_err error
				_ok  bool
			)
			defer func() {
				errCh <- _err
			}()

			ticker := timewheel.NewTicker(intervalOfCheckSubTask)
			defer func() {
				ticker.Stop()
			}()

			for {
				select {
				case <-ctx.Done():
					return
				case <-l.stopCh:
					return
				case <-ticker.C:
					_err, _ok = l.getRedisKeyStatus(statusKey)
					if _err != nil {
						l.logger.Errorf(
							"got an error from sub task, task_id: %s, target: %s, sub_task: %q, error: %+v",
							taskID, target, subTaskID, _err,
						)
						return
					} else if _ok {
						l.logger.Infof(
							"sub task has been done, task_id: %s, target: %s, sub_task: %q",
							taskID, target, subTaskID,
						)
						return
					}

					l.Debugf(
						"sub task is still running, wait for the execution result, task_id: %s, target: %s, sub_task: %q",
						taskID, target, subTaskID,
					)
				}
			}
		},
	)

	select {
	case <-ctx.Done():
		return errors.Errorf(
			"waiting for the result of sub task timed out, task_id: %s, target: %s, sub_task: %q, error: %+v",
			taskID, target, subTaskID, ctx.Err(),
		)
	case <-l.stopCh:
		return errors.Errorf(
			"got a stop signal while waiting for the result of sub task, task_id: %s, target: %s, sub_task: %q",
			taskID, target, subTaskID,
		)
	case err = <-errCh:
		return err
	}
}

func (l *ExecuteBaseLogic) cloneExecuteProject(ctx context.Context) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		gitURL     = l.taskInfo.GetTestFrameworkUrl()
		targetPath = l.frameworkPath
		branch     = l.taskInfo.GetTestFrameworkBranch()
	)

	l.logger.Infof(
		"begin to clone code, task_id: %s, target: %s, git: %s, path: %s, branch: %s",
		taskID, target, gitURL, targetPath, branch,
	)

	commit, err := utils.CloneWithContext(ctx, gitURL, targetPath, branch, utils.WithCloneProgress(l.logger))
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.GitOperationFailure, err.Error()),
			"failed to clone code, task_id: %s, target: %s, git: %s, path: %s, branch: %s, error: %+v",
			taskID, target, gitURL, targetPath, branch, err,
		)
	}

	l.logger.Infof(
		"finish to clone code, task_id: %s, target: %s, git: %s, path: %s, branch: %s, commit: %s",
		taskID, target, gitURL, targetPath, branch, commit.String(),
	)

	return nil
}

func (l *ExecuteBaseLogic) downloadTestPackage(ctx context.Context) (err error) {
	var (
		taskID      = l.taskInfo.GetTaskId()
		target      = l.taskInfo.GetTestTarget()
		pkgNameType = l.taskInfo.GetPackageName()

		link       = l.taskInfo.GetAppDownloadLink()
		targetPath = l.packagePath
	)

	l.logger.Infof(
		"begin to download the test package, task_id: %s, target: %s, url: %s, path: %s",
		taskID, target, link, targetPath,
	)

	var newLink string
	if len(link) > 0 {
		err = utils.DownloadFromUrl(ctx, link, targetPath)
	} else {
		var puller pkgpuller.AppPkgPuller
		puller, err = pkgpuller.NewAppPkgPuller(ctx, pkgpuller.AppPkgNameType(pkgNameType), targetPath)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DoesNotSupport, err.Error()),
				"failed to new app package puller, task_id: %s, target: %s, url: %s, path: %s, error: %+v",
				taskID, target, link, targetPath, err,
			)
		}
		newLink, err = puller.Pull()
		link = newLink
	}
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.FileOperationFailure, err.Error()),
			"failed to download the test package, task_id: %s, target: %s, url: %s, path: %s, error: %+v",
			taskID, target, link, targetPath, err,
		)
	}

	if len(newLink) > 0 {
		err = l.updateUIPlanRecordExecuteData(ctx, newLink, "")
		if err != nil {
			l.logger.Errorf(
				"failed to update ui_plan record, task_id: %s, app_download_link: %s, error: %+v",
				taskID, newLink, err,
			)
		}
	}

	l.logger.Infof(
		"finish to download the test package, task_id: %s, target: %s, url: %s, path: %s",
		taskID, target, link, targetPath,
	)

	return nil
}

func (l *ExecuteBaseLogic) acquireDevicesByDeviceTypeAndPlatformType(
	projectID string, deviceType commonpb.DeviceType, platformType commonpb.PlatformType,
) (
	resp *managerpb.AcquireProjectDeviceResp, err error,
) {
	return l.svcCtx.ManagerRPC.AcquireProjectDevice(
		l.ctx, &managerpb.AcquireProjectDeviceReq{
			ProjectId: projectID,
			Usage:     commonpb.DeviceUsage_UI_TESTING,
			Condition: &rpc.Condition{
				Group: &rpc.GroupCondition{
					Relationship: constants.AND,
					Conditions: []*rpc.Condition{
						{
							Single: &rpc.SingleCondition{
								Field:   string(devicehubcommon.DeviceFieldOfType),
								Compare: constants.EQ,
								Other: &rpc.Other{
									Value: strconv.Itoa(int(deviceType.Number())),
								},
							},
						},
						{
							Single: &rpc.SingleCondition{
								Field:   string(devicehubcommon.DeviceFieldOfPlatform),
								Compare: constants.EQ,
								Other: &rpc.Other{
									Value: strconv.Itoa(int(platformType.Number())),
								},
							},
						},
					},
				},
			},
			Count: 1,
		},
		zrpc.WithCallTimeout(devicehubcommon.ConstRPCSearchAcquireDeviceTimeout),
	)
}

func (l *ExecuteBaseLogic) acquireDevicesByDeviceUdid(projectID, deviceUdid string) (
	resp *managerpb.AcquireProjectDeviceResp, err error,
) {
	return l.svcCtx.ManagerRPC.AcquireProjectDevice(
		l.ctx, &managerpb.AcquireProjectDeviceReq{
			ProjectId: projectID,
			Usage:     commonpb.DeviceUsage_UI_TESTING,
			Condition: &rpc.Condition{
				Group: &rpc.GroupCondition{
					Relationship: constants.AND,
					Conditions: []*rpc.Condition{
						{
							Single: &rpc.SingleCondition{
								Field:   string(devicehubcommon.DeviceFieldOfUDID),
								Compare: constants.EQ,
								Other: &rpc.Other{
									Value: deviceUdid,
								},
							},
						},
					},
				},
			},
			Count: 1,
		},
		zrpc.WithCallTimeout(devicehubcommon.ConstRPCSearchAcquireDeviceTimeout),
	)
}

func (l *ExecuteBaseLogic) acquireDevices() (err error) {
	var (
		projectID = l.taskInfo.GetProjectId()
		taskID    = l.taskInfo.GetTaskId()
		target    = l.taskInfo.GetTestTarget()
		executeID = l.taskInfo.GetUiCase().GetUiCaseExecuteId()

		deviceType   = l.taskInfo.GetDeviceType()
		platformType = l.taskInfo.GetPlatformType()
		devices      = l.taskInfo.GetDevices()
	)

	defer func() {
		if err == nil && len(l.devices) > 0 && deviceType == commonpb.DeviceType_CLOUD_PHONE {
			i := rand.Int63n(10)                       // [0, 10)
			sleep := time.Duration(i*30) * time.Second // [0, 30, 60, 90, 120, 150, 180, 210, 240, 270]s

			l.logger.Infof(
				"randomly sleep for %f seconds after acquiring devices, task_id: %s, target: %s, device_type: %s, platform_type: %s",
				sleep.Seconds(), taskID, target, deviceType.String(), platformType.String(),
			)
			time.Sleep(sleep)
			l.logger.Infof(
				"complete random sleep %f seconds, task_id: %s, target: %s, device_type: %s, platform_type: %s",
				sleep.Seconds(), taskID, target, deviceType.String(), platformType.String(),
			)
		}

		if err != nil {
			_ = l.releaseDevices()
		}
	}()

	token := fmt.Sprintf("%s_%s", taskID, executeID)
	data := &model.WaitingTasks{
		Platform:   int64(platformType.Number()),
		DeviceType: int64(deviceType.Number()),
		Token:      token,
	}
	if len(devices) > 0 {
		data.Devices = sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(devices),
			Valid:  true,
		}
	}

	_, err = l.svcCtx.WaitingTasksModel.Insert(l.ctx, nil, data)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to insert table[%s] with values[%+v], task_id: %s, target: %s, error: %+v",
			l.svcCtx.WaitingTasksModel.Table(), data, taskID, target, err,
		)
	}

	defer func() {
		if _, e := l.svcCtx.WaitingTasksModel.RemoveByToken(l.ctx, nil, token); e != nil {
			l.Errorf(
				"failed to update table[%s] with values[%+v], task_id: %s, target: %s, error: %+v",
				l.svcCtx.WaitingTasksModel.Table(), token, taskID, target, e,
			)
		}
	}()

	ps := l.svcCtx.RedisNode.Subscribe(l.ctx, common.ConstChannelNameOfIdleDevicesNotifyByToken)
	defer func(ps *redis.PubSub) {
		if e := ps.Close(); e != nil {
			l.Errorf("failed to close PubSub, task_id: %s, target: %s, error: %+v", taskID, target, e)
		}
	}(ps)

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, timeoutOfWaitForDevice)
	defer cancel()

	failed := 0
	found := false
	var resp *managerpb.AcquireProjectDeviceResp
	for !found {
		select {
		case <-l.ctx.Done():
			return errors.Errorf(
				"got an error from context, task_id: %s, target: %s, error: %+v", taskID, target, l.ctx.Err(),
			)
		case <-l.stopCh:
			return errors.Errorf(
				"got a stop signal while waiting idle devices, task_id: %s, target: %s", taskID, target,
			)
		case <-ctx.Done():
			return errors.Errorf("timeout waiting for idle devices, task_id: %s, target: %s", taskID, target)
		case msg := <-ps.Channel():
			if !strings.HasPrefix(msg.Payload, token) {
				continue
			}
			deviceUdid := strings.TrimPrefix(msg.Payload, token+"_")

			l.Infof(
				"received notification, devicehub has IDLE device[%s], task_id: %s, target: %s, token: %s",
				deviceUdid, taskID, target, token,
			)

			resp, err = l.acquireDevicesByDeviceUdid(projectID, deviceUdid)
			if err != nil {
				if e := errorx.Convert(errors.Cause(err)); e.Code() != codes.InsufficientDevicesError {
					l.logger.Error(err.Error())
					failed += 1
					if failed >= 3 {
						return err
					}
				} else {
					l.Warnf(
						"acquire devices again but still no enough devices, task_id: %s, target: %s, token: %s",
						taskID, target, token,
					)
				}

				continue
			}

			failed = 0
			found = true
		}
	}

	if resp == nil || len(resp.GetDevices()) == 0 {
		return errors.Errorf(
			"not found devices, task_id: %s, target: %s, device_type: %s, platform_type: %s",
			taskID, target, deviceType.String(), platformType.String(),
		)
	}
	l.logger.Infof(
		"acquire devices, task_id: %s, target: %s, resp: %s", taskID, target, protobuf.MarshalJSONIgnoreError(resp),
	)

	for _, device := range resp.GetDevices() {
		if device == nil || device.GetDevice() == nil {
			continue
		}

		l.devices = append(l.devices, device.GetDevice())
	}

	return nil
}

func (l *ExecuteBaseLogic) releaseDevices() (err error) {
	var (
		projectID = l.taskInfo.GetProjectId()
		taskID    = l.taskInfo.GetTaskId()
		target    = l.taskInfo.GetTestTarget()
	)

	if err = mr.MapReduceVoid[*devicehubpb.Device, any](
		func(source chan<- *devicehubpb.Device) {
			for _, device := range l.devices {
				if device == nil || device.GetUdid() == "" || device.GetToken() == "" {
					continue
				}

				source <- device
			}
		}, func(item *devicehubpb.Device, writer mr.Writer[any], cancel func(error)) {
			var (
				udid  = item.GetUdid()
				token = item.GetToken()
			)

			l.logger.Infof(
				"ready to release the device, task_id: %s, target: %s, udid: %s, token: %s",
				taskID, target, udid, token,
			)

			ctx1, cancel1 := commonutils.NewTimeoutContext(l.ctx, devicehubcommon.ConstRPCReleaseDeviceTimeout)
			defer cancel1()

			if _, err := l.svcCtx.ManagerRPC.ReleaseProjectDevice(
				ctx1, &managerpb.ReleaseProjectDeviceReq{
					ProjectId: projectID,
					Udid:      item.GetUdid(),
					Token:     item.GetToken(),
				}, zrpc.WithCallTimeout(devicehubcommon.ConstRPCReleaseDeviceTimeout),
			); err != nil {
				l.logger.Errorf(
					"failed to release the device by grpc, task_id: %s, target: %s, udid: %s, token: %s, error: %+v",
					taskID, target, udid, token, err,
				)

				l.logger.Infof(
					"try to release the device by mq, task_id: %s, target: %s, udid: %s, token: %s",
					taskID, target, udid, token,
				)
				ctx2, cancel2 := commonutils.NewTimeoutContext(l.ctx, devicehubcommon.ConstRPCReleaseDeviceTimeout)
				defer cancel2()

				if _, err = l.svcCtx.DeviceHubProducer.Send(
					ctx2, base.NewTask(
						commonconsts.MQTaskTypeDeviceHubHandleReleaseDevice,
						protobuf.MarshalJSONIgnoreError(
							&devicehubpb.ReleaseDeviceReq{
								Udid:  item.GetUdid(),
								Token: item.GetToken(),
							},
						),
						base.WithRetentionOptions(2*time.Hour),
					), base.QueuePriorityDefault,
				); err != nil {
					l.logger.Errorf(
						"failed to release the device by mq, task_id: %s, target: %s, udid: %s, token: %s, error: %+v",
						taskID, target, udid, token, err,
					)
				}

				return
			}

			l.logger.Infof(
				"finish to release the device, task_id: %s, target: %s, udid: %s, token: %s",
				taskID, target, udid, token,
			)
		}, func(pipe <-chan any, cancel func(error)) {
		},
	); err != nil {
		l.logger.Errorf(
			"got an error while releasing the devices, task_id: %s, target: %s, devices: %s, error: %+v",
			taskID, target, protobuf.MarshalJSONIgnoreError(l.devices), err,
		)
	}

	return err
}

func (l *ExecuteBaseLogic) installAppToDevices() (err error) {
	ctx, _cancel := commonutils.NewTimeoutContext(l.ctx, timeoutOfInstallApp)
	defer _cancel()

	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()
	)

	err = mr.MapReduceVoid[*devicehubpb.Device, any](
		func(source chan<- *devicehubpb.Device) {
			for _, device := range l.devices {
				source <- device
			}
		}, func(item *devicehubpb.Device, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					l.logger.Errorf(
						"failed to install app, task_id: %s, target: %s, udid: %s, token: %s, package_path: %s, error: %+v",
						taskID, target, item.GetUdid(), item.GetToken(), l.packagePath, err,
					)
					cancel(err)
					return
				}

				l.logger.Infof(
					"install app successfully, task_id: %s, target: %s, udid: %s, token: %s, package_path: %s",
					taskID, target, item.GetUdid(), item.GetToken(), l.packagePath,
				)
			}()

			switch l.taskInfo.GetPlatformType() {
			case commonpb.PlatformType_IOS:
				err = l.GIDInstallApp(ctx, item, l.packagePath)
			default:
				err = l.ADBInstallApp(ctx, item, l.packagePath)
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(len(l.devices)),
	)

	return err
}

func (l *ExecuteBaseLogic) adbConnect(device *devicehubpb.Device) (d *gadb.Device, err error) {
	var (
		udid = device.GetUdid()
		addr = device.GetRemoteAddress()
	)

	client, err := gadb.NewClient()
	if err != nil {
		return nil, errors.Errorf("failed to new adb client, udid: %s, error: %+v", udid, err)
	}

	//if err = client.StartServer(); err != nil {
	//	return nil, errors.Errorf("failed to start the adb server, udid: %s, error: %+v", udid, err)
	//}

	hp := strings.Split(device.GetRemoteAddress(), ":")
	if len(hp) != 2 {
		return nil, errors.Errorf("invalid device remote address, udid: %s, remote_address: %s", udid, addr)
	}

	port, err := strconv.Atoi(hp[1])
	if err != nil {
		return nil, errors.Errorf(
			"can not to convert the port of device remote address to integer, udid: %s, remote_address: %s, port: %s, error: %+v",
			udid, addr, hp[1], err,
		)
	}

	for i := 1; i <= retryTimesOfADBConnect; i++ {
		if err = client.Connect(hp[0], gadb.WithPort(port)); err != nil {
			// ignore the `failed to authenticate to` error because it might have actually connected successfully.
			// if it indeed fails to connect, an error will be returned in the later 'FindDeviceBySerial' call.

			l.logger.Debugf(
				"failed to connect to the device, times: %d, udid: %s, ip: %s, port: %d, error: %+v",
				i, udid, hp[0], port, err,
			)
			if i == retryTimesOfADBConnect {
				return nil, errors.Errorf(
					"failed to connect to the device, udid: %s, ip: %s, port: %d, error: %+v",
					udid, hp[0], port, err,
				)
			}

			// even if it has connected successfully, there is a possibility of encountering a
			// 'device unauthorized' situation, so we delay here for a bit.
			time.Sleep(delayTimeOfADBConnect)
		} else {
			l.logger.Infof("succeed to connect to the device, udid: %s, ip: %s, port: %d", udid, hp[0], port)
			break
		}
	}

	d, err = client.FindDeviceBySerial(device.GetRemoteAddress())
	if err != nil {
		return nil, errors.Errorf("failed to find device by serial, udid: %s, serial: %s, error: %+v", udid, addr, err)
	}

	return d, nil
}

func (l *ExecuteBaseLogic) ADBInstallApp(ctx context.Context, device *devicehubpb.Device, apkPath string) error {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		udid = device.GetUdid()

		needToReinstall bool
		needToInstall   bool

		driver *gu2.Driver
		exitCh chan lang.PlaceholderType
	)

	defer func() {
		if exitCh != nil {
			close(exitCh)
		}
		if driver != nil {
			_ = driver.Close()
		}
	}()

	d, err := l.adbConnect(device)
	if err != nil {
		return err
	}

	_apk, err := apk.OpenFile(apkPath)
	if err != nil {
		return errors.Errorf(
			"failed to open the apk file, udid: %s, file: %s, error: %+v", udid, apkPath, err,
		)
	}
	l.appName = _apk.PackageName()

	defer func() {
		if len(_apk.Manifest().VersionName) > 0 {
			appVersion := _apk.Manifest().VersionName
			if err = l.updateUIPlanRecordExecuteData(ctx, "", appVersion); err != nil {
				l.Errorf(
					"failed to update ui_plan record, task_id: %s, target: %s, udid: %s, app_version: %s, error: %+v",
					taskID, target, udid, appVersion, err,
				)
			}
		}
	}()

	cmd := fmt.Sprintf("pm list packages %s", _apk.PackageName())
	output, err := d.RunShellCommand(cmd)
	if err != nil {
		return errors.Errorf(
			"failed to run the shell command, udid: %s, command: %q, error: %+v", udid, cmd, err,
		)
	}

	output = strings.TrimSpace(output)
	if output != "" {
		cmd = fmt.Sprintf("dumpsys package %s | grep versionName", _apk.PackageName())
		output, err = d.RunShellCommand(cmd)
		if err != nil {
			return errors.Errorf(
				"failed to run the shell command, udid: %s, command: %q, error: %+v", udid, cmd, err,
			)
		}

		target := strings.TrimSpace(output)
		target = strings.TrimPrefix(target, "versionName=")
		source := _apk.Manifest().VersionName

		if !strings.EqualFold(source, target) {
			l.logger.Infof(
				"need to reinstall the app, udid: %s, app_version: %s(%s => %s)", udid, apkPath, source, target,
			)
			needToInstall = true
			needToReinstall = true
		}
	} else {
		needToInstall = true
	}

	if needToInstall {
		if needToReinstall {
			cmd = fmt.Sprintf("pm uninstall %s", _apk.PackageName())
			if _, err = d.RunShellCommand(cmd); err != nil {
				return errors.Errorf(
					"failed to run the shell command, udid: %s, command: %q, error: %+v", udid, cmd, err,
				)
			}
		}

		pathOnDevice := filepath.Join(basePathOnAndroid, fmt.Sprintf("%s.apk", _apk.PackageName()))
		if err = d.PushFile(apkPath, pathOnDevice); err != nil {
			return errors.Errorf(
				"failed to push apk file to the device, udid: %s, file: %s, path: %s, error: %+v",
				udid, apkPath, pathOnDevice, err,
			)
		}

		// if the device is an Android real phone, then try to check whether
		// it requires manual confirmation when installing an app.
		if device.GetType() == commonpb.DeviceType_REAL_PHONE {
			driver, err = gu2.NewDriver(d)
			if err != nil {
				l.logger.Errorf("failed to new uiautomator2 driver, udid: %s, error: %+v", udid, err)
			} else {
				exitCh = make(chan lang.PlaceholderType)
				threading.GoSafe(
					func() {
						l.handleManualConfirmationByUIAutomator(ctx, udid, driver, exitCh)
					},
				)
			}
		}

		cmd = fmt.Sprintf("pm install -r -t %s", pathOnDevice)
		output, err = d.RunShellCommand(cmd)
		if err != nil {
			return errors.Errorf(
				"failed to run the shell command, udid: %s, command: %q, error: %+v", udid, cmd, err,
			)
		}

		if device.GetType() == commonpb.DeviceType_REAL_PHONE {
			// sleep for 3 seconds to avoid missing the pop-up window after installing the app.
			time.Sleep(3 * time.Second)
		}
		l.logger.Infof("finish to install the android app, udid: %s, command: %q, result: %s", udid, cmd, output)
	} else {
		l.logger.Debugf("no need to install the android app, udid: %s, file: %s", udid, apkPath)
	}

	return nil
}

func (l *ExecuteBaseLogic) handleManualConfirmationByUIAutomator(
	ctx context.Context, udid string, driver *gu2.Driver, exitCh <-chan lang.PlaceholderType,
) {
	for {
		select {
		case <-l.ctx.Done():
			l.logger.Errorf("got a cancel signal from main task, udid: %s", udid)
			return
		case <-ctx.Done():
			l.logger.Errorf("got a cancel signal from sub task of installing the android app, udid: %s", udid)
			return
		case <-exitCh:
			l.logger.Debugf("got an exit signal from sub task of installing the android app, udid: %s", udid)
			return
		default:
			for _, text := range l.confirmationTexts {
				element := driver.FindElementBySelectorOptions(gu2.ByText(text))
				if element == nil {
					continue
				} else if ok, err := element.Exist(); err != nil || !ok {
					continue
				} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
					continue
				}

				l.logger.Debugf(
					"click the element successfully while handling manual confirmation, udid: %s, text: %s",
					udid, text,
				)
				break
			}

			time.Sleep(time.Second)
		}
	}
}

func (l *ExecuteBaseLogic) ADBUnInstallApp(device *devicehubpb.Device, apkPath string) error {
	udid := device.GetUdid()

	d, err := l.adbConnect(device)
	if err != nil {
		return err
	}

	_apk, err := apk.OpenFile(apkPath)
	if err != nil {
		return errors.Errorf("failed to open the apk file, udid: %s, file: %s, error: %+v", udid, apkPath, err)
	}

	cmd := fmt.Sprintf("pm list packages %s", _apk.PackageName())
	output, err := d.RunShellCommand(cmd)
	if err != nil {
		return errors.Errorf("failed to run the shell command, udid: %s, command: %q, error: %+v", udid, cmd, err)
	}

	output = strings.TrimSpace(output)
	if output != "" {
		output, err = d.RunShellCommand("pm", "uninstall", _apk.PackageName())
		if err != nil {
			return errors.Errorf("failed to run the shell command, udid: %s, command: %q, error: %+v", udid, cmd, err)
		}

		l.logger.Infof("finish to uninstall the android app, udid: %s, command: %q, result: %s", udid, cmd, output)
	} else {
		l.logger.Debugf("no need to uninstall the android app, udid: %s, package_name: %s", udid, _apk.PackageName())
	}

	return nil
}

func (l *ExecuteBaseLogic) gidConnect(device *devicehubpb.Device) (usb gid.Usbmux, d gid.Device, err error) {
	var (
		udid = device.GetUdid()
		addr = device.GetRemoteAddress()
	)

	u, err := url.Parse(addr)
	if err != nil {
		return nil, nil, errors.Errorf(
			"failed to parse the remote address, udid: %s, address: %s, error: %+v", udid, addr, err,
		)
	}
	address := fmt.Sprintf("%s:%d", u.Hostname(), usbMuxPort)

	if err = caller.RetryDo(
		caller.MaxRetryCount, func() error {
			usb, err = gid.NewUsbmuxWithAddress(address)
			if err != nil {
				return errors.Errorf(
					"failed to new usbmux, udid: %s, address: %s, error: %+v", udid, address, err,
				)
			}

			d, err = usb.FindDeviceByUDID(device.GetUdid())
			if err != nil {
				return errors.Errorf("failed to find the device, udid: %s, error: %+v", udid, err)
			}

			return nil
		},
	); err != nil {
		return nil, nil, err
	}

	return usb, d, nil
}

func (l *ExecuteBaseLogic) GIDInstallApp(ctx context.Context, device *devicehubpb.Device, ipaPath string) error {
	udid := device.GetUdid()

	usb, d, err := l.gidConnect(device)
	if err != nil {
		return err
	}
	defer func() {
		usb.Close()
	}()

	info, err := ipa.Info(ipaPath)
	if err != nil {
		return errors.Errorf("failed to get ipa info, udid: %s, file: %s, error: %+v", udid, ipaPath, err)
	}
	l.appName = info.CFBundleIdentifier

	defer func() {
		if len(info.CFBundleShortVersionString) > 0 {
			appVersion := info.CFBundleShortVersionString
			if len(info.CFBundleVersion) > 0 {
				appVersion += "-" + info.CFBundleVersion
			}
			err = l.updateUIPlanRecordExecuteData(ctx, "", appVersion)
			if err != nil {
				l.logger.Errorf(
					"failed to update ui_plan record, task_id: %s, app_version: %s, error: %+v",
					l.taskInfo.GetTaskId(), appVersion, err,
				)
			}
		}
	}()

	var apps []*ipa.InfoPlist
	if err = caller.RetryDo(
		caller.MaxRetryCount, func() error {
			defer func() {
				if err != nil {
					time.Sleep(3 * time.Second)
				}
			}()

			apps, err = d.InstallationProxyBrowse(gid.WithBundleIDs(info.CFBundleIdentifier))
			if err != nil {
				return errors.Errorf(
					"failed to get the app list through the installation proxy, udid: %s, error: %+v", udid, err,
				)
			}

			return nil
		},
	); err != nil {
		return err
	}

	needToInstall := true
	if len(apps) > 0 {
		if strings.EqualFold(apps[0].CFBundleVersion, info.CFBundleVersion) {
			needToInstall = false
		} else {
			l.logger.Infof(
				"need to re-install the app, udid: %s, app_version: %s (%s => %s)",
				udid, ipaPath, apps[0].CFBundleVersion, info.CFBundleVersion,
			)
		}
	}

	if needToInstall {
		// try to kill the app by bundle id
		if err = d.AppKillByBundleID(info.CFBundleIdentifier); err != nil {
			l.logger.Warnf(
				"failed to kill the ios app, udid: %s, bundle_id: %s, error: %+v", udid, info.CFBundleIdentifier, err,
			)
		}
		if err = d.AppInstall(ipaPath); err != nil {
			return errors.Errorf(
				"failed to install the ios app, udid: %s, file: %s, error: %+v", udid, ipaPath, err,
			)
		}

		l.logger.Infof("finish to install the ios app, udid: %s, file: %s", udid, ipaPath)
	} else {
		l.logger.Debugf("no need to install the ios app, udid: %s, file: %s", udid, ipaPath)
	}

	return nil
}

func (l *ExecuteBaseLogic) GIDUnInstallApp(device *devicehubpb.Device, ipaPath string) error {
	udid := device.GetUdid()

	usb, d, err := l.gidConnect(device)
	if err != nil {
		return err
	}
	defer func() {
		usb.Close()
	}()

	info, err := ipa.Info(ipaPath)
	if err != nil {
		return errors.Errorf("failed to get ipa info, udid: %s, file: %s, error: %+v", udid, ipaPath, err)
	}

	apps, err := d.InstallationProxyBrowse(gid.WithBundleIDs(info.CFBundleIdentifier))
	if err != nil {
		return errors.Errorf(
			"failed to get the app list through the installation proxy, udid: %s, error: %+v", udid, err,
		)
	}

	if len(apps) > 0 && apps[0].CFBundleIdentifier == info.CFBundleIdentifier {
		if err = d.AppUninstall(info.CFBundleIdentifier); err != nil {
			return errors.Errorf(
				"failed to uninstall the ios app, udid: %s, file: %s, bundle_id: %s, error: %+v",
				udid, ipaPath, info.CFBundleIdentifier, err,
			)
		}

		l.logger.Infof("finish to uninstall the ios app, udid: %s, bundle_id: %s", udid, info.CFBundleIdentifier)
	} else {
		l.logger.Debugf(
			"no need to uninstall the ios app, udid: %s, file: %s, bundle_id: %s",
			udid, ipaPath, info.CFBundleIdentifier,
		)
	}

	return nil
}

func (l *ExecuteBaseLogic) updateUIPlanRecordExecuteData(
	ctx context.Context, appDownloadLink, appVersion string,
) (err error) {
	if len(appDownloadLink) == 0 && len(appVersion) == 0 {
		return nil
	}
	var (
		taskID    = l.taskInfo.GetTaskId()
		planID    = l.taskInfo.GetPlanId()
		projectID = l.taskInfo.GetProjectId()
		executeID = l.taskInfo.GetUiCase().UiPlanExecuteId
	)

	if len(appDownloadLink) > 0 {
		lockKey := fmt.Sprintf("lock::app_download_link:%s::%s", l.shortTaskID, taskID)
		if ok, err := l.svcCtx.Redis.SetnxExCtx(ctx, lockKey, appDownloadLink, expireTimeOfSubTask); err != nil {
			return err
		} else if !ok {
			return nil
		}
	}

	if len(appVersion) > 0 {
		l.taskInfo.AppVersion = appVersion
		lockKey := fmt.Sprintf("lock::app_version:%s::%s", l.shortTaskID, taskID)
		if ok, err := l.svcCtx.Redis.SetnxExCtx(ctx, lockKey, appVersion, expireTimeOfSubTask); err != nil {
			return err
		} else if !ok {
			return nil
		}
		defer func() {
			if err != nil {
				_, _ = l.svcCtx.Redis.DelCtx(ctx, lockKey)
			}
		}()
	}

	record, err := l.svcCtx.ReporterRPC.ViewPlanRecord(
		ctx, &reporterpb.ViewUIPlanRecordRequest{
			TaskId:    taskID,
			ProjectId: projectID,
			ExecuteId: executeID,
		},
	)
	if err != nil {
		return err
	}

	executeData := record.GetExecuteData()

	var uiPlan managerpb.UIPlanComponent
	err = protobuf.UnmarshalJSONFromString(executeData, &uiPlan)
	if err != nil {
		return err
	}

	var needToModify bool
	if len(appDownloadLink) > 0 && uiPlan.MetaData.AppDownloadLink != appDownloadLink {
		needToModify = true
		uiPlan.MetaData.AppDownloadLink = appDownloadLink
	}
	if len(appVersion) > 0 && uiPlan.MetaData.AppVersion != appVersion {
		needToModify = true
		uiPlan.MetaData.AppVersion = appVersion
	}

	if needToModify {
		_, err = l.svcCtx.ReporterRPC.ModifyPlanRecord(
			ctx, &reporterpb.PutUIPlanRecordRequest{
				ProjectId:   projectID,
				PlanId:      planID,
				TaskId:      taskID,
				ExecuteId:   executeID,
				ExecuteData: protobuf.MarshalJSONToStringIgnoreError(&uiPlan),
			},
		)
		if err != nil {
			return err
		}
	}

	return nil
}

func (l *ExecuteBaseLogic) getIOSDevice(device *devicehubpb.Device) (*ios.DeviceEntry, error) {
	var (
		udid = device.GetUdid()
		addr = device.GetRemoteAddress()
	)

	u, err := url.Parse(addr)
	if err != nil {
		return nil, errors.Errorf(
			"failed to parse the remote address, udid: %s, address: %s, error: %+v", udid, addr, err,
		)
	}

	d, err := helpers.GetDevice(
		&options.Options{
			Socket:         fmt.Sprintf("%s:%d", u.Hostname(), usbMuxPort),
			UDID:           udid,
			TunnelInfoHost: u.Hostname(),
			UserspaceHost:  u.Hostname(),
		},
	)
	if err != nil {
		return nil, errors.Errorf("failed to get the device, udid: %s, error: %+v", udid, err)
	}

	return &d, nil
}

func (l *ExecuteBaseLogic) startToCollectPerfData(device *devicehubpb.Device) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		deviceType   = device.GetType()
		platformType = device.GetPlatform()
		udid         = device.GetUdid()
	)

	if deviceType != commonpb.DeviceType_REAL_PHONE {
		return
	}

	switch platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		d, err := l.adbConnect(device)
		if err != nil {
			l.logger.Errorf(
				"failed to connect to the android device, task_id: %s, target: %s, udid: %s, error: %+v",
				taskID, target, udid, err,
			)
			return
		}

		l.c = collector.NewAndroidCollector(l.ctx, d, l.appName, collector.WithCallback(l.sendPerfDataToReporter))
	case commonpb.PlatformType_IOS:
		d, err := l.getIOSDevice(device)
		if err != nil {
			l.logger.Errorf(
				"failed to get the ios device, task_id: %s, target: %s, udid: %s, error: %+v",
				taskID, target, udid, err,
			)
			return
		}

		l.c = collector.NewIOSCollector(l.ctx, d, l.appName, collector.WithCallback(l.sendPerfDataToReporter))
	default:
		l.Errorf(
			"unknown platform type, task_id: %s, target: %s, platform: %s, udid: %s",
			taskID, target, protobuf.GetEnumStringOf(platformType), udid,
		)
		return
	}

	if l.c == nil {
		return
	}

	if err := l.c.Start(); err != nil {
		l.logger.Errorf(
			"failed to start to collect the perf data, task_id: %s, target: %s, udid: %s, error: %+v",
			taskID, target, udid, err,
		)
		return
	}

	l.logger.Infof("begin to collect the perf data, task_id: %s, target: %s, udid: %s", taskID, target, udid)
}

func (l *ExecuteBaseLogic) sendPerfDataToReporter(
	dataType collector.DataType, interval time.Duration, points []collector.PointData,
) {
	var (
		taskID     = l.taskInfo.GetTaskId()
		executeID  = l.taskInfo.GetUiCase().GetUiCaseExecuteId()
		projectID  = l.taskInfo.GetProjectId()
		target     = l.taskInfo.GetTestTarget()
		executedBy = l.taskInfo.GetExecutedBy()
	)

	if l.c == nil || len(points) == 0 {
		return
	}

	for _, point := range points {
		payload := protobuf.MarshalJSONIgnoreError(
			&commonpb.SaveDevicePerfDataTaskInfo{
				TaskId:     taskID,
				ExecuteId:  executeID,
				ProjectId:  projectID,
				Udid:       l.c.DeviceID(),
				Usage:      commonpb.DeviceUsage_UI_TESTING,
				DataType:   dataType.ConvertToPerfDataType(),
				Interval:   interval.Milliseconds(),
				Series:     string(point.Series),
				Unit:       string(point.Unit),
				X:          point.X,
				Y:          point.Y,
				ExecutedBy: executedBy,
			},
		)
		if _, err := l.svcCtx.ReporterProducer.Send(
			l.ctx, base.NewTask(
				commonconsts.MQTaskTypeReporterSaveDevicePerfDataTask,
				payload,
				base.WithRetentionOptions(time.Minute),
			), base.QueuePriorityDefault,
		); err != nil {
			l.logger.Errorf(
				"failed to send the perf data to reporter, task_id: %s, target: %s, payload: %s, error: %+v",
				taskID, target, payload, err,
			)
		}
	}
}

func (l *ExecuteBaseLogic) stopToCollectPerfData() {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()
	)

	if l.c == nil {
		return
	}

	deviceID := l.c.DeviceID()
	if err := l.c.Stop(); err != nil {
		l.Errorf(
			"failed to stop to collect the perf data, task_id: %s, target: %s, udid: %s, error: %+v",
			taskID, target, deviceID, err,
		)
	} else {
		l.logger.Infof(
			"finish to collect the perf data, task_id: %s, target: %s, udid: %s", taskID, target, deviceID,
		)
	}
}
