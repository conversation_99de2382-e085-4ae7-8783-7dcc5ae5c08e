# probe-backend 质量平台

## 服务端口
> **服务端口分配规则（生产）：不同服务相同功能使用相同的端口**
> - API服务端口：80
> - RPC服务端口：8080
> - Prometheus服务端口：9101 - 91xx
> - 健康检查服务端口：6470 - 64xx
> 
> **服务端口分配规则（本地）：PSF（P：项目，占1位；S：服务，占2位；F：功能，占2位）**
> - 项目（P）：公共服务（1），质量平台（2），效能平台（3）
> - 服务：`account`（01），`manager`（02），`dispatcher`（03），`apiworker`（04），`reporter`（05）
> - 功能：API服务（0x），RPC服务（1x），Prometheus（2x），健康检查（3x）

| service name | api service port (2ss0x) | rpc service port (2ss1x) |    prometheus port (2ss2x) | health service port (2ss3x) |
|--------------|-------------------------:|-------------------------:|---------------------------:|----------------------------:|
| account      |                    20101 |                    20111 |               20121, 20122 |                20131, 20132 |
| manager      |                    20201 |                    20211 |               20221, 20222 |                20231, 20232 |
| dispatcher   |                    20301 |                    20311 | 20321, 20322, 20323, 20324 |  20331, 20332, 20333, 20334 |
| apiworker    |                    20401 |                    20411 |        20421, 20422, 20423 |         20431, 20432, 20433 |
| reporter     |                    20501 |                    20511 |               20521, 20522 |                20531, 20532 |
| worker       |                    20601 |                    20611 |               20621, 20622 |                20631, 20632 |
| relation     |                    20701 |                    20711 |          20721,20722,20723 |         20731, 20732, 20733 |
| devicehub    |                    20801 |                    20811 |               20821, 20822 |                20831, 20832 |
| uiworker     |                        - |                        - |                      20921 |                       20931 |
| ai           |                    21001 |                    21011 |               21021, 21022 |                21031, 21032 |
| perfworker   |                    21101 |                        - |               21121, 21122 |                21131, 21132 |
| perftool     |                        - |                        - |                      21221 |                       21231 |
| larkproxy    |                    21301 |                    21311 |                      21321 |                       21331 |
| staworker    |                        - |                        - |                      21421 |                       21431 |
| discovery    |                          |                    21511 |                          - |                       21531 |
## 错误码
> global: 全局错误码
> - 0: OK
> - 99: Internal Error
> - 1 - 16: GRPC错误码 ([codes.go](https://github.com/grpc/grpc-go/blob/master/codes/codes.go))
> - 100 - 511: HTTP状态码 ([status.go](https://github.com/golang/go/blob/master/src/net/http/status.go))
>
> system: 系统错误（10000 - 49999）
> - 公共服务：10000 - 11999 [`qet-backend-common`, `qet-backend-middleware`]
> - 质量平台：12000 - 13999 [`probe-backend`]
> - 效能平台：14000 - 15999 [`insight-backend`]
>
> business: 业务错误（50000 - 99999）
> - 公共服务：50000 - 51999 [`qet-backend-common`, `qet-backend-middleware`]
> - 质量平台：52000 - 53999 [`probe-backend`]
> - 效能平台：54000 - 55999 [`insight-backend`]
>
> **注：项目下的各服务的错误码统一管理，区间划分各项目可按实际情况进行规划**

## Redis Database Number
**注：大几率不同项目使用不同的`Redis`实例，因此原来再用的暂不修改`db`**

| project name | service name | database number |
|--------------|--------------|----------------:|
| probe        | manager      |               2 |
| probe        | dispatcher   |               3 |
| probe        | apiworker    |               4 |
| probe        | reporter     |               5 |
| probe        | account      |               6 |
| common       | beat         |               7 |
| common       | notifier     |               8 |
| common       | user         |               9 |
| common       | permission   |              10 |
| common       | redashquery  |              11 |
| common       | redashsync   |              12 |
| common       | tapdnotify   |              13 |
| common       | announcement |              14 |
| probe        | worker       |              15 |
| probe        | relation     |              16 |
| probe        | devicehub    |              17 |
| probe        | uiworker     |              18 |
| probe        | ai           |              19 |
| probe        | perfworker   |              20 |
| probe        | perftool     |              21 |
| probe        | larkproxy    |              22 |
| probe        | staworker    |              23 |

## 生成私钥、公钥、证书
```shell
# 生成私钥
openssl genrsa -out probe_pri.key 2048

# 生成公钥
openssl rsa -in probe_pri.key -pubout -out probe_pub.key

# 生成公钥证书
openssl req -new -x509 -sha256 -key probe_pri.key -out probe.pem -subj '/C=CN/ST=GD/L=GZ/O=TT'
```

## 微服务
1. `account`
   ```shell
   # 通过`./account/generate.sh`生产代码
   # 生成`api`服务代码
   ./account/generate.sh api

   # 生成`rpc`服务代码
   ./account/generate.sh rpc

   # 生成`model`代码
   ./account/generate.sh model
   ```

2. `dispatcher`
   ```shell
   # 通过`./dispatcher/generate.sh`生产代码
   # 生成`api`服务代码
   ./dispatcher/generate.sh api

   # 生成`rpc`服务代码
   ./dispatcher/generate.sh rpc
   ```

3. `manager`
   ```shell
   # 通过`./manager/generate.sh`生产代码
   # 生成`api`服务代码
   ./manager/generate.sh api

   # 生成`rpc`服务代码
   ./manager/generate.sh rpc

   # 生成`model`代码
   ./manager/generate.sh model
   ```

4. `reporter`
   ```shell
   # 通过`./reporter/generate.sh`生产代码
   # 生成`api`服务代码
   ./reporter/generate.sh api

   # 生成`rpc`服务代码
   ./reporter/generate.sh rpc

   # 生成`model`代码
   ./reporter/generate.sh model
   ```

5. `relation`
   ```shell
   # 通过`./relation/generate.sh`生产代码
   # 生成`api`服务代码
   ./relation/generate.sh api

   # 生成`rpc`服务代码
   ./relation/generate.sh rpc

   # 生成`model`代码
   ./relation/generate.sh model
   ```
