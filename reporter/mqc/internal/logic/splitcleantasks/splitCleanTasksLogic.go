package splitcleantaskslogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/types"
)

type SplitCleanTasksLogic struct {
	*BaseLogic
}

func NewSplitCleanTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SplitCleanTasksLogic {
	return &SplitCleanTasksLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SplitCleanTasksLogic) filterRecordByNumber(
	records []*model.RedundantRecord, value string,
) []*model.RedundantRecord {
	cache := make(map[string]int, len(records))
	filteredRecords := make([]*model.RedundantRecord, 0, len(records))

	valueInt, err := strconv.Atoi(value)
	if err != nil {
		l.Errorf("failed to convert string to integer, value: %s, error: %+v", value, err)
		valueInt = common.ConstDefaultKeepNumber
	}
	if valueInt > common.ConstDefaultMaxNumber {
		l.Warnf("the value is greater than the maximum, value: %d, max: %d", valueInt, common.ConstDefaultMaxNumber)
		valueInt = common.ConstDefaultMaxNumber
	}

	for _, v := range records {
		key := v.ProjectId + v.ExecuteTypeId
		if _, ok := cache[key]; !ok {
			cache[key] = 0
		}

		if cache[key] < valueInt {
			cache[key] += 1
		} else {
			filteredRecords = append(filteredRecords, v)
		}
	}

	return filteredRecords
}

func (l *SplitCleanTasksLogic) filterRecordByTime(
	records []*model.RedundantRecord, value string,
) []*model.RedundantRecord {
	cache := make(map[string]int, len(records))
	filteredRecords := make([]*model.RedundantRecord, 0, len(records))

	duration, err := time.ParseDuration("-" + value)
	if err != nil {
		l.Errorf("failed to parse duration, value: %s, error: %+v", value, err)
		duration = -common.ConstDefaultKeepDuration
	}
	targetTime := time.Now().Add(duration)

	for _, v := range records {
		key := v.ProjectId + v.ExecuteTypeId
		if _, ok := cache[key]; !ok {
			cache[key] = 0
		}

		if cache[key] < common.ConstDefaultKeepNumber {
			cache[key] += 1
		} else {
			if v.CreateAt.Before(targetTime) || cache[key] >= common.ConstDefaultMaxNumber {
				filteredRecords = append(filteredRecords, v)
			} else {
				cache[key] += 1
			}
		}
	}

	return filteredRecords
}

func (l *SplitCleanTasksLogic) parseCleanTask(cc *types.CleanConfig) []*model.RedundantRecord {
	var (
		records []*model.RedundantRecord
		err     error
	)

	switch cc.Type {
	case constants.CleanTypeApiPlan:
		records, err = l.svcCtx.PlanExecutionRecordModel.FindPlanExecutionRecord(l.ctx)
		if err != nil {
			l.Errorf("failed to find execution records of api plan, error: %+v", err)
			return nil
		}
	case constants.CleanTypeUiPlan:
		records, err = l.svcCtx.UIPlanExecutionRecordModel.FindPlanExecutionRecord(l.ctx)
		if err != nil {
			l.Errorf("failed to find execution records of ui plan, error: %+v", err)
			return nil
		}
	case constants.CleanTypePerfPlan:
		records, err = l.svcCtx.PerfPlanExecutionRecordModel.FindPlanExecutionRecord(l.ctx)
		if err != nil {
			l.Errorf("failed to find execution records of perf plan, error: %+v", err)
			return nil
		}
	case constants.CleanTypeStabilityPlan:
		records, err = l.svcCtx.StabilityPlanExecutionRecordModel.FindPlanExecutionRecord(l.ctx)
		if err != nil {
			l.Errorf("failed to find execution records of stability plan, error: %+v", err)
			return nil
		}
	case constants.CleanTypeApiSuite:
		records, err = l.svcCtx.SuiteExecutionRecordModel.FindSuiteExecutionRecord(l.ctx)
		if err != nil {
			l.Errorf("failed to find execution records of api suite, error: %+v", err)
			return nil
		}
	case constants.CleanTypeInterfaceDocument:
		records, err = l.svcCtx.InterfaceExecutionRecordModel.FindInterFaceDocumentExecutionRecord(l.ctx)
		if err != nil {
			l.Errorf("failed to find execution records of interface document, error: %+v", err)
			return nil
		}
	case constants.CleanTypeApiCase, constants.CleanTypeInterfaceCase, constants.CleanTypeApiComponentGroup:
		records, err = l.svcCtx.ExecutionRecordModel.FindExecutionRecordByExecuteType(l.ctx, cc.Type)
		if err != nil {
			l.Errorf("failed to find execution records of case or component group, error: %+v", err)
			return nil
		}
	default:
		return nil
	}

	l.Infof(
		"find execution records successfully, type: %s, mode: %s, value: %s, number: %d",
		cc.Type, cc.Mode, cc.Value, len(records),
	)

	switch cc.Mode {
	case constants.CleanModeNumber:
		records = l.filterRecordByNumber(records, cc.Value)
	case constants.CleanModeTime:
		records = l.filterRecordByTime(records, cc.Value)
	default:
		return nil
	}

	return records
}

func (l *SplitCleanTasksLogic) sendSetCleanedTask(taskId, projectId, cleanType string) {
	payload := jsonx.MarshalIgnoreError(
		&types.SetCleanedTask{
			TaskId:    taskId,
			ProjectId: projectId,
			CleanType: cleanType,
		},
	)

	if _, err := l.svcCtx.ReporterProducer.Send(
		l.ctx,
		base.NewTask(constants.MQTaskTypeReporterSetCleanedTask, payload),
		base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to send task to mq, name: %s, payload: %s", constants.MQTaskTypeReporterSetCleanedTask, payload,
		)
	}
}

func (l *SplitCleanTasksLogic) sendDelCleanedTask(cleanType string, keepDays int) {
	payload := jsonx.MarshalIgnoreError(
		&types.DelCleanedTask{
			CleanType: cleanType,
			KeepDays:  keepDays,
		},
	)

	if _, err := l.svcCtx.ReporterProducer.SendDelay(
		l.ctx,
		base.NewTask(constants.MQTaskTypeReporterDelCleanedTask, payload),
		10*time.Minute,
		base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to send task to mq, name: %s, payload: %s", constants.MQTaskTypeReporterDelCleanedTask, payload,
		)
	}
}

// Deprecated: use `SplitCleanTasksV2` instead.
func (l *SplitCleanTasksLogic) SplitCleanTasks(data []byte) {
	var taskValue types.CleanConfigs
	err := json.Unmarshal(data, &taskValue)
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, cleanConfig := range taskValue.Items {
		cc := &types.CleanConfig{
			Type:  cleanConfig.Type,
			Mode:  cleanConfig.Mode,
			Value: cleanConfig.Value,
		}
		for _, v := range l.parseCleanTask(cc) {
			if v == nil {
				continue
			}
			l.sendSetCleanedTask(v.TaskId, v.ProjectId, cleanConfig.Type)
		}
	}
}

func (l *SplitCleanTasksLogic) SplitCleanTasksV2() {
	cache := make(map[string]lang.PlaceholderType, len(supportedExecuteTypes))
	_ = mr.MapReduceVoid[*types.CleanConfig, any](
		func(source chan<- *types.CleanConfig) {
			for _, rule := range l.svcCtx.Config.ReporterCleaner.CleanRules {
				if rule.Type == "" || rule.Mode == "" || rule.Value == "" {
					continue
				}

				source <- &types.CleanConfig{
					Type:     rule.Type,
					Mode:     rule.Mode,
					Value:    rule.Value,
					KeepDays: rule.KeepDays,
				}
				cache[rule.Type] = lang.Placeholder
			}

			for _, v := range supportedExecuteTypes {
				if _, ok := cache[v]; !ok {
					source <- &types.CleanConfig{
						Type:     v,
						Mode:     l.svcCtx.Config.ReporterCleaner.DefaultMode,
						Value:    l.svcCtx.Config.ReporterCleaner.DefaultValue,
						KeepDays: l.svcCtx.Config.ReporterCleaner.DefaultKeepDays,
					}
					cache[v] = lang.Placeholder
				}
			}
		}, func(item *types.CleanConfig, writer mr.Writer[any], cancel func(error)) {
			for _, v := range l.parseCleanTask(item) {
				if v == nil {
					continue
				}

				l.sendSetCleanedTask(v.TaskId, v.ProjectId, item.Type)
			}

			l.sendDelCleanedTask(item.Type, item.KeepDays)
		}, func(pipe <-chan any, cancel func(error)) {
		},
	)
}
