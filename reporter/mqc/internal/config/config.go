package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type CleanRule struct {
	Type     string
	Mode     string
	Value    string
	KeepDays int
}

type CleanerConfig struct {
	CleanCron       string
	CleanRules      []CleanRule
	DefaultMode     string `json:",default=number"`
	DefaultValue    string `json:",default=10"`
	DefaultKeepDays int    `json:",default=7"`
}

type Config struct {
	service.ServiceConf

	DB    types.DBConfig
	Redis redis.RedisConf

	Discovery zrpc.RpcClientConf `json:",optional"`

	Consumer         consumerv2.Config
	ReporterProducer producer.Config

	ReporterCleaner   CleanerConfig
	FailedCaseCleaner commontypes.ClearStrategy

	LocalPath string
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
