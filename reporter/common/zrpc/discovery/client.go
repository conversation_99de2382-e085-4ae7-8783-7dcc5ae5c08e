package discovery

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/client/discoveryservice"
	discoverypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

var (
	_ IClient = (*RPCClient)(nil)
	_ IClient = (*NoopClient)(nil)
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	discovery discoveryservice.DiscoveryService
}

func NewRPCClient(c zrpc.RpcClientConf) IClient {
	if _, err := c.BuildTarget(); err != nil {
		return &NoopClient{}
	}

	return &RPCClient{
		conf: c,

		discovery: discoveryservice.NewDiscoveryService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) IsDiscovery() bool {
	return true
}

func (c *RPCClient) RemoveApp(
	ctx context.Context, in *discoverypb.RemoveAppReq, opts ...grpc.CallOption,
) (*discoverypb.RemoveAppResp, error) {
	return c.discovery.RemoveApp(ctx, in, opts...)
}

type NoopClient struct{}

func (c *NoopClient) IsDiscovery() bool {
	return false
}

func (c *NoopClient) RemoveApp(
	ctx context.Context, in *discoverypb.RemoveAppReq, opts ...grpc.CallOption,
) (*discoverypb.RemoveAppResp, error) {
	return &discoverypb.RemoveAppResp{}, nil
}
