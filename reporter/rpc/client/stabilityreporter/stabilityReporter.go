// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package stabilityreporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type (
	CaseFailForPlanStatForMq                                      = pb.CaseFailForPlanStatForMq
	CleanConfig                                                   = pb.CleanConfig
	CleanConfigs                                                  = pb.CleanConfigs
	CountFailedCaseInLastNDaysReq                                 = pb.CountFailedCaseInLastNDaysReq
	CountFailedCaseInLastNDaysResp                                = pb.CountFailedCaseInLastNDaysResp
	CreateInterfaceRecordResponse                                 = pb.CreateInterfaceRecordResponse
	CreatePerfCaseRecordReq                                       = pb.CreatePerfCaseRecordReq
	CreatePerfCaseRecordResp                                      = pb.CreatePerfCaseRecordResp
	CreatePerfPlanRecordReq                                       = pb.CreatePerfPlanRecordReq
	CreatePerfPlanRecordResp                                      = pb.CreatePerfPlanRecordResp
	CreatePerfSuiteRecordReq                                      = pb.CreatePerfSuiteRecordReq
	CreatePerfSuiteRecordResp                                     = pb.CreatePerfSuiteRecordResp
	CreatePlanRecordResponse                                      = pb.CreatePlanRecordResponse
	CreateRecordResponse                                          = pb.CreateRecordResponse
	CreateServiceRecordResponse                                   = pb.CreateServiceRecordResponse
	CreateStabilityDeviceRecordResp                               = pb.CreateStabilityDeviceRecordResp
	CreateStabilityPlanRecordResp                                 = pb.CreateStabilityPlanRecordResp
	CreateSuiteRecordResponse                                     = pb.CreateSuiteRecordResponse
	CreateUICaseRecordResponse                                    = pb.CreateUICaseRecordResponse
	CreateUIPlanRecordResponse                                    = pb.CreateUIPlanRecordResponse
	CreateUISuiteRecordResponse                                   = pb.CreateUISuiteRecordResponse
	DelCaseFailStatForPlanReq                                     = pb.DelCaseFailStatForPlanReq
	DelCaseFailStatForPlanResp                                    = pb.DelCaseFailStatForPlanResp
	FindRedundantPlanRecordResp                                   = pb.FindRedundantPlanRecordResp
	GetCaseLatestRecordRequest                                    = pb.GetCaseLatestRecordRequest
	GetCaseLatestRecordResponse                                   = pb.GetCaseLatestRecordResponse
	GetCaseLatestRecordResponse_RecordCaseRecord                  = pb.GetCaseLatestRecordResponse_RecordCaseRecord
	GetChildrenRecordRequest                                      = pb.GetChildrenRecordRequest
	GetChildrenRecordResponse                                     = pb.GetChildrenRecordResponse
	GetChildrenRecordResponse_ChildRecord                         = pb.GetChildrenRecordResponse_ChildRecord
	GetExecuteRecordRequest                                       = pb.GetExecuteRecordRequest
	GetExecuteRecordResponse                                      = pb.GetExecuteRecordResponse
	GetInterfaceRecordRequest                                     = pb.GetInterfaceRecordRequest
	GetInterfaceRecordResponse                                    = pb.GetInterfaceRecordResponse
	GetInterfaceRecordResponse_CaseItem                           = pb.GetInterfaceRecordResponse_CaseItem
	GetParentRecordRequest                                        = pb.GetParentRecordRequest
	GetParentRecordResponse                                       = pb.GetParentRecordResponse
	GetPerfCaseRecordReq                                          = pb.GetPerfCaseRecordReq
	GetPerfCaseRecordResp                                         = pb.GetPerfCaseRecordResp
	GetPerfPlanRecordReq                                          = pb.GetPerfPlanRecordReq
	GetPerfPlanRecordResp                                         = pb.GetPerfPlanRecordResp
	GetPerfSuiteRecordReq                                         = pb.GetPerfSuiteRecordReq
	GetPerfSuiteRecordResp                                        = pb.GetPerfSuiteRecordResp
	GetPlanCasesInfoRequest                                       = pb.GetPlanCasesInfoRequest
	GetPlanCasesInfoResponse                                      = pb.GetPlanCasesInfoResponse
	GetPlanRecordRequest                                          = pb.GetPlanRecordRequest
	GetPlanRecordResponse                                         = pb.GetPlanRecordResponse
	GetPlanRecordResponse_InterfaceDocumentItem                   = pb.GetPlanRecordResponse_InterfaceDocumentItem
	GetPlanRecordResponse_ServiceItem                             = pb.GetPlanRecordResponse_ServiceItem
	GetPlanRecordResponse_SuiteItem                               = pb.GetPlanRecordResponse_SuiteItem
	GetPlanSummaryRequest                                         = pb.GetPlanSummaryRequest
	GetPlanSummaryResponse                                        = pb.GetPlanSummaryResponse
	GetPlanSummaryResponse_Record                                 = pb.GetPlanSummaryResponse_Record
	GetPlanTimeScaleRequest                                       = pb.GetPlanTimeScaleRequest
	GetPlanTimeScaleResponse                                      = pb.GetPlanTimeScaleResponse
	GetPlanTimeScaleResponse_CaseRecord                           = pb.GetPlanTimeScaleResponse_CaseRecord
	GetPlanTimeScaleResponse_SuiteRecord                          = pb.GetPlanTimeScaleResponse_SuiteRecord
	GetServiceRecordRequest                                       = pb.GetServiceRecordRequest
	GetServiceRecordResponse                                      = pb.GetServiceRecordResponse
	GetServiceRecordResponse_CaseItem                             = pb.GetServiceRecordResponse_CaseItem
	GetStabilityDeviceActivityReq                                 = pb.GetStabilityDeviceActivityReq
	GetStabilityDeviceActivityResp                                = pb.GetStabilityDeviceActivityResp
	GetStabilityDevicePerfDataReq                                 = pb.GetStabilityDevicePerfDataReq
	GetStabilityDevicePerfDataResp                                = pb.GetStabilityDevicePerfDataResp
	GetStabilityPlanRecordReq                                     = pb.GetStabilityPlanRecordReq
	GetStabilityPlanRecordResp                                    = pb.GetStabilityPlanRecordResp
	GetSuiteRecordRequest                                         = pb.GetSuiteRecordRequest
	GetSuiteRecordResponse                                        = pb.GetSuiteRecordResponse
	GetSuiteRecordResponse_CaseItem                               = pb.GetSuiteRecordResponse_CaseItem
	GetUICaseRecordReq                                            = pb.GetUICaseRecordReq
	GetUICaseRecordResp                                           = pb.GetUICaseRecordResp
	GetUICaseStepReq                                              = pb.GetUICaseStepReq
	GetUICaseStepResp                                             = pb.GetUICaseStepResp
	GetUIDevicePerfDataReq                                        = pb.GetUIDevicePerfDataReq
	GetUIDevicePerfDataResp                                       = pb.GetUIDevicePerfDataResp
	GetUIPlanCasesInfoRequest                                     = pb.GetUIPlanCasesInfoRequest
	GetUIPlanCasesInfoResponse                                    = pb.GetUIPlanCasesInfoResponse
	GetUIPlanRecordReq                                            = pb.GetUIPlanRecordReq
	GetUIPlanRecordResp                                           = pb.GetUIPlanRecordResp
	ListFailCaseRecordForPlanRequest                              = pb.ListFailCaseRecordForPlanRequest
	ListFailCaseRecordForPlanResponse                             = pb.ListFailCaseRecordForPlanResponse
	ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord = pb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord
	ListInterfaceRecordRequest                                    = pb.ListInterfaceRecordRequest
	ListInterfaceRecordResponse                                   = pb.ListInterfaceRecordResponse
	ListInterfaceRecordResponse_InterfaceRecord                   = pb.ListInterfaceRecordResponse_InterfaceRecord
	ListPlanRecordRequest                                         = pb.ListPlanRecordRequest
	ListPlanRecordResponse                                        = pb.ListPlanRecordResponse
	ListPlanRecordResponse_PlanRecord                             = pb.ListPlanRecordResponse_PlanRecord
	ListServiceRecordRequest                                      = pb.ListServiceRecordRequest
	ListServiceRecordResponse                                     = pb.ListServiceRecordResponse
	ListServiceRecordResponse_ServiceRecord                       = pb.ListServiceRecordResponse_ServiceRecord
	ListStabilityDeviceStepReq                                    = pb.ListStabilityDeviceStepReq
	ListStabilityDeviceStepResp                                   = pb.ListStabilityDeviceStepResp
	ListStabilityPlanRecordReq                                    = pb.ListStabilityPlanRecordReq
	ListStabilityPlanRecordResp                                   = pb.ListStabilityPlanRecordResp
	ListSuiteRecordRequest                                        = pb.ListSuiteRecordRequest
	ListSuiteRecordResponse                                       = pb.ListSuiteRecordResponse
	ListSuiteRecordResponse_SuiteRecord                           = pb.ListSuiteRecordResponse_SuiteRecord
	ListUICaseStepReq                                             = pb.ListUICaseStepReq
	ListUICaseStepResp                                            = pb.ListUICaseStepResp
	ListUIPlanRecordRequest                                       = pb.ListUIPlanRecordRequest
	ListUIPlanRecordResponse                                      = pb.ListUIPlanRecordResponse
	ListUIPlanRecordResponse_PlanRecord                           = pb.ListUIPlanRecordResponse_PlanRecord
	ModifyInterfaceRecordResponse                                 = pb.ModifyInterfaceRecordResponse
	ModifyPerfCaseRecordReq                                       = pb.ModifyPerfCaseRecordReq
	ModifyPerfCaseRecordResp                                      = pb.ModifyPerfCaseRecordResp
	ModifyPerfPlanRecordReq                                       = pb.ModifyPerfPlanRecordReq
	ModifyPerfPlanRecordResp                                      = pb.ModifyPerfPlanRecordResp
	ModifyPerfSuiteRecordReq                                      = pb.ModifyPerfSuiteRecordReq
	ModifyPerfSuiteRecordResp                                     = pb.ModifyPerfSuiteRecordResp
	ModifyPlanRecordResponse                                      = pb.ModifyPlanRecordResponse
	ModifyRecordResponse                                          = pb.ModifyRecordResponse
	ModifyServiceRecordResponse                                   = pb.ModifyServiceRecordResponse
	ModifyStabilityDeviceRecordResp                               = pb.ModifyStabilityDeviceRecordResp
	ModifyStabilityPlanRecordResp                                 = pb.ModifyStabilityPlanRecordResp
	ModifySuiteRecordResponse                                     = pb.ModifySuiteRecordResponse
	ModifyUICaseRecordResponse                                    = pb.ModifyUICaseRecordResponse
	ModifyUIPlanRecordResponse                                    = pb.ModifyUIPlanRecordResponse
	ModifyUISuiteRecordResponse                                   = pb.ModifyUISuiteRecordResponse
	PutInterfaceRecordRequest                                     = pb.PutInterfaceRecordRequest
	PutPlanRecordRequest                                          = pb.PutPlanRecordRequest
	PutRecordRequest                                              = pb.PutRecordRequest
	PutServiceRecordRequest                                       = pb.PutServiceRecordRequest
	PutStabilityDeviceRecordReq                                   = pb.PutStabilityDeviceRecordReq
	PutStabilityPlanRecordReq                                     = pb.PutStabilityPlanRecordReq
	PutSuiteRecordRequest                                         = pb.PutSuiteRecordRequest
	PutUICaseRecordRequest                                        = pb.PutUICaseRecordRequest
	PutUIPlanRecordRequest                                        = pb.PutUIPlanRecordRequest
	PutUISuiteRecordRequest                                       = pb.PutUISuiteRecordRequest
	RedundantPlanRecord                                           = pb.RedundantPlanRecord
	SaveUIDevicePerfDataReq                                       = pb.SaveUIDevicePerfDataReq
	SaveUIDevicePerfDataResp                                      = pb.SaveUIDevicePerfDataResp
	SearchPerfCaseRecordReq                                       = pb.SearchPerfCaseRecordReq
	SearchPerfCaseRecordResp                                      = pb.SearchPerfCaseRecordResp
	SearchPerfPlanRecordReq                                       = pb.SearchPerfPlanRecordReq
	SearchPerfPlanRecordResp                                      = pb.SearchPerfPlanRecordResp
	SearchStabilityDeviceRecordReq                                = pb.SearchStabilityDeviceRecordReq
	SearchStabilityDeviceRecordResp                               = pb.SearchStabilityDeviceRecordResp
	SearchStabilityPlanRecordReq                                  = pb.SearchStabilityPlanRecordReq
	SearchStabilityPlanRecordResp                                 = pb.SearchStabilityPlanRecordResp
	SearchUICaseRecordReq                                         = pb.SearchUICaseRecordReq
	SearchUICaseRecordResp                                        = pb.SearchUICaseRecordResp
	SearchUIDeviceRecordReq                                       = pb.SearchUIDeviceRecordReq
	SearchUIDeviceRecordResp                                      = pb.SearchUIDeviceRecordResp
	SearchUISuiteRecordReq                                        = pb.SearchUISuiteRecordReq
	SearchUISuiteRecordResp                                       = pb.SearchUISuiteRecordResp
	UpdateMonitorURLOfPerfPlanRecordReq                           = pb.UpdateMonitorURLOfPerfPlanRecordReq
	UpdateMonitorURLOfPerfPlanRecordResp                          = pb.UpdateMonitorURLOfPerfPlanRecordResp
	ViewUIPlanRecordRequest                                       = pb.ViewUIPlanRecordRequest
	ViewUIPlanRecordResponse                                      = pb.ViewUIPlanRecordResponse

	StabilityReporter interface {
		// ListStabilityPlanRecord 获取稳测计划的执行记录
		ListStabilityPlanRecord(ctx context.Context, in *ListStabilityPlanRecordReq, opts ...grpc.CallOption) (*ListStabilityPlanRecordResp, error)
		// SearchStabilityPlanRecord 搜索稳测的执行记录
		SearchStabilityPlanRecord(ctx context.Context, in *SearchStabilityPlanRecordReq, opts ...grpc.CallOption) (*SearchStabilityPlanRecordResp, error)
		// GetStabilityPlanRecord 获取稳测执行报告的计划信息
		GetStabilityPlanRecord(ctx context.Context, in *GetStabilityPlanRecordReq, opts ...grpc.CallOption) (*GetStabilityPlanRecordResp, error)
		// CreateStabilityPlanRecord 创建稳测的执行记录
		CreateStabilityPlanRecord(ctx context.Context, in *PutStabilityPlanRecordReq, opts ...grpc.CallOption) (*CreateStabilityPlanRecordResp, error)
		// ModifyStabilityPlanRecord 修改稳测执行记录
		ModifyStabilityPlanRecord(ctx context.Context, in *PutStabilityPlanRecordReq, opts ...grpc.CallOption) (*ModifyStabilityPlanRecordResp, error)
		// SearchStabilityDeviceRecord 搜索稳测执行报告的总览设备
		SearchStabilityDeviceRecord(ctx context.Context, in *SearchStabilityDeviceRecordReq, opts ...grpc.CallOption) (*SearchStabilityDeviceRecordResp, error)
		// CreateStabilityDeviceRecord 创建稳测设备的执行记录
		CreateStabilityDeviceRecord(ctx context.Context, in *PutStabilityDeviceRecordReq, opts ...grpc.CallOption) (*CreateStabilityDeviceRecordResp, error)
		// ModifyStabilityDeviceRecord 修改稳测设备的执行记录
		ModifyStabilityDeviceRecord(ctx context.Context, in *PutStabilityDeviceRecordReq, opts ...grpc.CallOption) (*ModifyStabilityDeviceRecordResp, error)
		// ListStabilityDeviceStep 获取稳测执行报告的设备步骤日志
		ListStabilityDeviceStep(ctx context.Context, in *ListStabilityDeviceStepReq, opts ...grpc.CallOption) (*ListStabilityDeviceStepResp, error)
		// GetStabilityDevicePerfData 获取稳测执行报告的设备性能数据
		GetStabilityDevicePerfData(ctx context.Context, in *GetStabilityDevicePerfDataReq, opts ...grpc.CallOption) (*GetStabilityDevicePerfDataResp, error)
		// GetStabilityDeviceActivity 获取稳测执行报告的设备Activity统计
		GetStabilityDeviceActivity(ctx context.Context, in *GetStabilityDeviceActivityReq, opts ...grpc.CallOption) (*GetStabilityDeviceActivityResp, error)
	}

	defaultStabilityReporter struct {
		cli zrpc.Client
	}
)

func NewStabilityReporter(cli zrpc.Client) StabilityReporter {
	return &defaultStabilityReporter{
		cli: cli,
	}
}

// ListStabilityPlanRecord 获取稳测计划的执行记录
func (m *defaultStabilityReporter) ListStabilityPlanRecord(ctx context.Context, in *ListStabilityPlanRecordReq, opts ...grpc.CallOption) (*ListStabilityPlanRecordResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.ListStabilityPlanRecord(ctx, in, opts...)
}

// SearchStabilityPlanRecord 搜索稳测的执行记录
func (m *defaultStabilityReporter) SearchStabilityPlanRecord(ctx context.Context, in *SearchStabilityPlanRecordReq, opts ...grpc.CallOption) (*SearchStabilityPlanRecordResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.SearchStabilityPlanRecord(ctx, in, opts...)
}

// GetStabilityPlanRecord 获取稳测执行报告的计划信息
func (m *defaultStabilityReporter) GetStabilityPlanRecord(ctx context.Context, in *GetStabilityPlanRecordReq, opts ...grpc.CallOption) (*GetStabilityPlanRecordResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.GetStabilityPlanRecord(ctx, in, opts...)
}

// CreateStabilityPlanRecord 创建稳测的执行记录
func (m *defaultStabilityReporter) CreateStabilityPlanRecord(ctx context.Context, in *PutStabilityPlanRecordReq, opts ...grpc.CallOption) (*CreateStabilityPlanRecordResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.CreateStabilityPlanRecord(ctx, in, opts...)
}

// ModifyStabilityPlanRecord 修改稳测执行记录
func (m *defaultStabilityReporter) ModifyStabilityPlanRecord(ctx context.Context, in *PutStabilityPlanRecordReq, opts ...grpc.CallOption) (*ModifyStabilityPlanRecordResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.ModifyStabilityPlanRecord(ctx, in, opts...)
}

// SearchStabilityDeviceRecord 搜索稳测执行报告的总览设备
func (m *defaultStabilityReporter) SearchStabilityDeviceRecord(ctx context.Context, in *SearchStabilityDeviceRecordReq, opts ...grpc.CallOption) (*SearchStabilityDeviceRecordResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.SearchStabilityDeviceRecord(ctx, in, opts...)
}

// CreateStabilityDeviceRecord 创建稳测设备的执行记录
func (m *defaultStabilityReporter) CreateStabilityDeviceRecord(ctx context.Context, in *PutStabilityDeviceRecordReq, opts ...grpc.CallOption) (*CreateStabilityDeviceRecordResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.CreateStabilityDeviceRecord(ctx, in, opts...)
}

// ModifyStabilityDeviceRecord 修改稳测设备的执行记录
func (m *defaultStabilityReporter) ModifyStabilityDeviceRecord(ctx context.Context, in *PutStabilityDeviceRecordReq, opts ...grpc.CallOption) (*ModifyStabilityDeviceRecordResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.ModifyStabilityDeviceRecord(ctx, in, opts...)
}

// ListStabilityDeviceStep 获取稳测执行报告的设备步骤日志
func (m *defaultStabilityReporter) ListStabilityDeviceStep(ctx context.Context, in *ListStabilityDeviceStepReq, opts ...grpc.CallOption) (*ListStabilityDeviceStepResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.ListStabilityDeviceStep(ctx, in, opts...)
}

// GetStabilityDevicePerfData 获取稳测执行报告的设备性能数据
func (m *defaultStabilityReporter) GetStabilityDevicePerfData(ctx context.Context, in *GetStabilityDevicePerfDataReq, opts ...grpc.CallOption) (*GetStabilityDevicePerfDataResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.GetStabilityDevicePerfData(ctx, in, opts...)
}

// GetStabilityDeviceActivity 获取稳测执行报告的设备Activity统计
func (m *defaultStabilityReporter) GetStabilityDeviceActivity(ctx context.Context, in *GetStabilityDeviceActivityReq, opts ...grpc.CallOption) (*GetStabilityDeviceActivityResp, error) {
	client := pb.NewStabilityReporterClient(m.cli.Conn())
	return client.GetStabilityDeviceActivity(ctx, in, opts...)
}
