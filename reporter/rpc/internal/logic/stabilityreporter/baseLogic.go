package stabilityreporterlogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []commonutils.TypeConverter{
			commonpb.StringToTriggerMode(),
			commonpb.TriggerModeToString(),
		},
	}
}

const (
	lockTimeout = 5 * time.Second
)

func StringIfOr(a, b string) string {
	if a != "" {
		return a
	}
	return b
}

func Int64IfOr(a, b int64) int64 {
	if a > 0 {
		return a
	}
	return b
}

func TimeIfOr(a, b time.Time) time.Time {
	if !a.IsZero() {
		return a
	}
	return b
}

func (l *BaseLogic) ConvertStabilityPlanModel(
	in *pb.PutStabilityPlanRecordReq, old *model.StabilityPlanExecutionRecord,
) (out *model.StabilityPlanExecutionRecord) {
	if old == nil {
		old = &model.StabilityPlanExecutionRecord{}
	}

	var (
		status      = StringIfOr(in.GetStatus(), old.Status.String)
		executeData = StringIfOr(in.GetExecuteData(), old.ExecuteData.String)
		executedBy  = StringIfOr(old.ExecutedBy, in.GetExecutedBy())
		errMsg      = StringIfOr(in.GetErrMsg(), old.ErrMsg.String)
		costTime    = old.CostTime
		startedAt   = old.StartedAt.Time
		endedAt     = old.EndedAt.Time
	)

	if len(executedBy) == 0 {
		executedBy = l.currentUser.Account
	}
	if in.GetStartedAt() != nil && in.GetStartedAt().IsValid() && in.GetStartedAt().GetSeconds() > 0 {
		startedAt = in.GetStartedAt().AsTime()
	}
	if in.GetEndedAt() != nil && in.GetEndedAt().IsValid() && in.GetEndedAt().GetSeconds() > 0 {
		endedAt = in.GetEndedAt().AsTime()
		if !startedAt.IsZero() {
			costTime = int64(endedAt.Sub(startedAt).Seconds())
		}
	}

	out = &model.StabilityPlanExecutionRecord{
		Id:          old.Id,
		ProjectId:   StringIfOr(old.ProjectId, in.GetProjectId()),
		PlanId:      StringIfOr(old.PlanId, in.GetPlanId()),
		PlanName:    StringIfOr(old.PlanName, in.GetPlanName()),
		TriggerMode: StringIfOr(old.TriggerMode, in.GetTriggerMode()),
		TaskId:      StringIfOr(old.TaskId, in.GetTaskId()),
		ExecuteId:   StringIfOr(old.ExecuteId, in.GetExecuteId()),
		Status: sql.NullString{
			String: status,
			Valid:  status != "",
		},
		CostTime:       costTime,
		TargetDuration: Int64IfOr(int64(in.GetTargetDuration()), old.TargetDuration),
		ExecuteData: sql.NullString{
			String: executeData,
			Valid:  executeData != "",
		},
		ExecutedBy:    executedBy,
		SuccessDevice: old.SuccessDevice,
		FailureDevice: old.FailureDevice,
		TotalDevice:   Int64IfOr(int64(in.GetTotalDevice()), old.TotalDevice),
		StartedAt: sql.NullTime{
			Time:  startedAt,
			Valid: !startedAt.IsZero(),
		},
		EndedAt: sql.NullTime{
			Time:  endedAt,
			Valid: !endedAt.IsZero(),
		},
		ErrMsg: sql.NullString{
			String: errMsg,
			Valid:  errMsg != "",
		},
		Cleaned:   old.Cleaned,
		Deleted:   old.Deleted,
		CreatedBy: StringIfOr(old.CreatedBy, l.currentUser.Account),
		UpdatedBy: l.currentUser.Account,
		DeletedBy: old.DeletedBy,
		CreatedAt: old.CreatedAt,
		UpdatedAt: old.UpdatedAt,
		DeletedAt: old.DeletedAt,
	}

	return out
}

func (l *BaseLogic) ConvertStabilityDeviceModel(
	in *pb.PutStabilityDeviceRecordReq, old *model.StabilityDeviceExecutionRecord,
) (out *model.StabilityDeviceExecutionRecord) {
	if old == nil {
		old = &model.StabilityDeviceExecutionRecord{}
	}

	var (
		device     = StringIfOr(in.GetDevice(), old.Device.String)
		status     = StringIfOr(in.GetStatus(), old.Status.String)
		executedBy = StringIfOr(old.ExecutedBy, in.GetExecutedBy())
		errMsg     = StringIfOr(in.GetErrMsg(), old.ErrMsg.String)
		costTime   = old.CostTime
		startedAt  = old.StartedAt.Time
		endedAt    = old.EndedAt.Time
	)

	if len(executedBy) == 0 {
		executedBy = l.currentUser.Account
	}
	if in.GetStartedAt() != nil && in.GetStartedAt().IsValid() && in.GetStartedAt().GetSeconds() > 0 {
		startedAt = in.GetStartedAt().AsTime()
	}
	if in.GetEndedAt() != nil && in.GetEndedAt().IsValid() && in.GetEndedAt().GetSeconds() > 0 {
		endedAt = in.GetEndedAt().AsTime()
		if !startedAt.IsZero() {
			costTime = int64(endedAt.Sub(startedAt).Seconds())
		}
	}

	out = &model.StabilityDeviceExecutionRecord{
		Id:            old.Id,
		TaskId:        StringIfOr(old.TaskId, in.GetTaskId()),
		ExecuteId:     StringIfOr(old.ExecuteId, in.GetExecuteId()),
		ProjectId:     StringIfOr(old.ProjectId, in.GetProjectId()),
		PlanExecuteId: StringIfOr(old.PlanExecuteId, in.GetPlanExecuteId()),
		Udid:          StringIfOr(old.Udid, in.GetUdid()),
		Device: sql.NullString{
			String: device,
			Valid:  device != "",
		},
		Status: sql.NullString{
			String: status,
			Valid:  status != "",
		},
		CostTime:   costTime,
		CrashCount: Int64IfOr(int64(in.GetCrashCount()), old.CrashCount),
		AnrCount:   Int64IfOr(int64(in.GetAnrCount()), old.AnrCount),
		Result: sql.NullString{
			String: protobuf.MarshalJSONToStringIgnoreError(in.GetResult()),
			Valid:  in.GetResult() != nil,
		},
		ExecutedBy: executedBy,
		StartedAt: sql.NullTime{
			Time:  startedAt,
			Valid: !startedAt.IsZero(),
		},
		EndedAt: sql.NullTime{
			Time:  endedAt,
			Valid: !endedAt.IsZero(),
		},
		ErrMsg: sql.NullString{
			String: errMsg,
			Valid:  errMsg != "",
		},
		Cleaned:   old.Cleaned,
		Deleted:   old.Deleted,
		CreatedBy: StringIfOr(old.CreatedBy, l.currentUser.Account),
		UpdatedBy: l.currentUser.Account,
		DeletedBy: old.DeletedBy,
		CreatedAt: old.CreatedAt,
		UpdatedAt: old.UpdatedAt,
		DeletedAt: old.DeletedAt,
	}

	return out
}

func (l *BaseLogic) GeneratePlanRecordExecuteData(
	ctx context.Context,
	record *model.StabilityPlanExecutionRecord,
	appInfo *commonpb.AppInfo,
) (string, error) {
	var (
		taskID    = record.TaskId
		executeID = record.ExecuteId
		projectID = record.ProjectId
	)

	if !record.ExecuteData.Valid {
		return "", errors.Errorf(
			"failed to generate execute_data, task_id: %s, execute_id: %s, project_id: %s, execute_data: nil",
			taskID, executeID, projectID,
		)
	}

	if appInfo == nil {
		return "", errors.Errorf(
			"failed to generate execute_data, task_id: %s, execute_id: %s, project_id: %s, app_info: nil",
			taskID, executeID, projectID,
		)
	}

	var executeData managerpb.StabilityPlanComponent
	if err := protobuf.UnmarshalJSONFromString(record.ExecuteData.String, &executeData); err != nil {
		return "", errors.Errorf(
			"failed to generate execute_data, task_id: %s, execute_id: %s, project_id: %s, err: %v",
			taskID, executeID, projectID, err,
		)
	}

	metaData := executeData.GetMetaData()
	metaData.AppVersion = appInfo.GetVersion()
	metaData.AppDownloadLink = appInfo.GetDownloadLink()

	return protobuf.MarshalJSONToStringIgnoreError(&executeData), nil
}
