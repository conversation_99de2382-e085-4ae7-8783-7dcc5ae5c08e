package stabilityReporter

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchStabilityDeviceRecordLogic struct {
	*BaseLogic
}

func NewSearchStabilityDeviceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchStabilityDeviceRecordLogic {
	return &SearchStabilityDeviceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchStabilityDeviceRecordLogic) SearchStabilityDeviceRecord(req *types.SearchStabilityDeviceRecordReq) (
	resp *types.SearchStabilityDeviceRecordResp, err error,
) {
	in := &pb.SearchStabilityDeviceRecordReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.StabilityReporter.SearchStabilityDeviceRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchStabilityDeviceRecordResp{Items: make([]*types.StabilityDeviceRecordItem, 0, len(out.GetItems()))}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	for i, item := range resp.Items {
		if len(item.UDID) == 0 {
			item.UDID = out.GetItems()[i].GetUdid()
		}
	}

	return resp, nil
}
