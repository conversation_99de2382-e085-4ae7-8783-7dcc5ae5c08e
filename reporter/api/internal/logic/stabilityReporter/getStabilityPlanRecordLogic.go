package stabilityReporter

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetStabilityPlanRecordLogic struct {
	*BaseLogic
}

func NewGetStabilityPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetStabilityPlanRecordLogic {
	return &GetStabilityPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetStabilityPlanRecordLogic) GetStabilityPlanRecord(req *types.GetStabilityPlanRecordReq) (
	resp *types.GetStabilityPlanRecordResp, err error,
) {
	in := &pb.GetStabilityPlanRecordReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.StabilityReporter.GetStabilityPlanRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.GetStabilityPlanRecordResp{
		StabilityPlanRecordItem: &types.StabilityPlanRecordItem{},
		StabilityPlanMetaData:   &types.StabilityPlanMetaData{},
	}
	if err = utils.Copy(resp.StabilityPlanRecordItem, out.Item, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy stability plan execution record to response, record: %+v, error: %+v",
			out.Item, err,
		)
	}
	if err = utils.Copy(resp.StabilityPlanMetaData, out.MetaData, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy stability plan meta data to response, meta data: %+v, error: %+v",
			out.MetaData, err,
		)
	}
	resp.Devices = convertCustomDevicesApi(out.GetMetaData().GetDevices())
	resp.CustomScript = convertCustomScriptApi(out.MetaData.GetCustomScript())

	return resp, nil
}
