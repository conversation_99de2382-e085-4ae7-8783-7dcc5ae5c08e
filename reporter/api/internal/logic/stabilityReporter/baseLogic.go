package stabilityReporter

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	converters []utils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		converters: []utils.TypeConverter{
			usercommon.StringToUserInfo(ctx, svcCtx.UserRpc, nil),
			commonpb.PlatformTypeToString(),
			devicehubpb.DeviceStateToString(),
			commonpb.PerfDataTypeToString(),
			commonpb.StringToPerfDataType(),
			commonpb.StringToTriggerMode(),
			commonpb.TriggerModeToString(),
		},
	}
}

func convertCustomDevicesApi(device *commonpb.StabilityCustomDevices) *commontypes.StabilityDevices {
	var devices commontypes.StabilityDevices
	if device != nil {
		switch device.Devices.(type) {
		case *commonpb.StabilityCustomDevices_Udids:
			for _, value := range device.GetUdids().GetValues() {
				devices.Udids = append(devices.Udids, value.GetStringValue())
			}
		case *commonpb.StabilityCustomDevices_Count:
			devices.Count = device.GetCount()
		}
	}
	return &devices
}

func convertCustomScriptApi(script *commonpb.StabilityCustomScript) *commontypes.StabilityCustomScript {
	var ac *commontypes.StabilityCustomScript
	if script != nil {
		switch script.Script.(type) {
		case *commonpb.StabilityCustomScript_GitConfig:
			// TODO
		case *commonpb.StabilityCustomScript_Image:
			ac = &commontypes.StabilityCustomScript{
				Image: script.GetImage(),
			}
		}
	}
	return ac
}
